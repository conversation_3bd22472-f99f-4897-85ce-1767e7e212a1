<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\UserBalance;
use Illuminate\Database\Eloquent\Builder;

class UserBalanceRepository
{
    public function __construct(protected UserBalance $model)
    {
        //
    }

    public function all(int $limit = 10)
    {
        return $this->model
            ->whereHas('userable', function ($query) {
                $query->where('balance', '<>', 0)
                    ->withoutGlobalScopes(); // Include soft-deleted records
            })
            ->with(['userable' => function ($query) {
                $query->withTrashed(); // Load soft-deleted related models
            }])
            ->when(request('search'), function ($query) {
                $searchTerm = '%' . request('search') . '%';
                return $query->whereHas('userable', function ($q) use ($searchTerm) {
                    $q->where('name', 'LIKE', $searchTerm)
                        ->withoutGlobalScopes(); // Ensure soft-deleted records are included
                });
            })
            ->when(request('type'), function ($query) {
                return $query->whereHas('userable', function ($q) {
                    $q->where('type', 'LIKE', request('type'))
                        ->withoutGlobalScopes(); // Ensure soft-deleted records are included
                });
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }





    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }
}
