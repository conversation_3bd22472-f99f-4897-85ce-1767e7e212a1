<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArchivedTaskResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $archivedActivity = $this->activities->where('action', 'deleted')->last();

        return [
            'id' => $this->id,
            'title' => $this->title,
            'project' => $this->project->name,
            'user' => $this->user->name,
            'status' => $this->status,
            'priority' => $this->priority,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'deleted_at' => $this->deleted_at->format('Y-m-d'),
            'archived_by' => optional($archivedActivity?->user)->name,
        ];
    }
}
