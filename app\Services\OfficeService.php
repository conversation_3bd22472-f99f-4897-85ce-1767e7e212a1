<?php

namespace App\Services;

use App\DTO\OfficeData;
use App\DTO\SubscriptionData;
use App\DTO\TenantData;
use App\Mail\OfficeCredentials;
use App\Repositories\OfficeRepository;
use App\Repositories\PlanRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OfficeService
{
    public function __construct(public OfficeRepository $officeRepo, public PlanRepository $planRepo, public TenantService $tenantService, public AdminService $adminService) {}

    public function get()
    {
        $filterable = request()->only(['search', 'status']);

        if (request()->type === 'archive') {
            return $this->officeRepo->getTrashedWithPaginate();
        } else {
            return $this->officeRepo->getPaginated($filterable, 12);
        }
    }

    public function getById($id)
    {
        $office = $this->officeRepo->findById($id);

        if (! $office) {
            throw new NotFoundHttpException;
        }

        return $office;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = OfficeData::from($request)->all();

            $office = $this->officeRepo->create($data);

            if (request()->hasFile('image')) {
                $office->addMedia(request()->image)
                    ->toMediaCollection('offices');
            }

            $plan = $this->planRepo->findById($request['plan_id']);

            $subscriptionData = SubscriptionData::from([
                'plan_id' => $request['plan_id'],
                'duration' => $request['duration'],
                'plan_price' => $request['plan_price'],
                'total' => $request['total'],
                'status' => 'Active',
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonthsNoOverflow((int) $request['duration'])->format('Y-m-d'),
                'number_of_projects' => $plan->number_of_projects,
                'number_of_site_engineers' => $plan->number_of_site_engineers,
                'number_of_office_engineers' => $plan->number_of_office_engineers,
                'number_of_accountants' => $plan->number_of_accountants,
                'number_of_project_managers' => $plan->number_of_project_managers,
                'number_of_admins' => $plan->number_of_admins,
                'storage' => $plan->storage,
                'storage_type' => $plan->storage_type,
                'has_domain' => $plan->has_domain,
                'has_free_website' => $plan->has_free_website,
                'has_chat_availability' => $plan->has_chat_availability,
                'is_unlimited_projects' => $plan->is_unlimited_projects,
                'is_unlimited_site_engineers' => $plan->is_unlimited_site_engineers,
                'is_unlimited_office_engineers' => $plan->is_unlimited_office_engineers,
                'is_unlimited_accountants' => $plan->is_unlimited_accountants,
                'is_unlimited_project_managers' => $plan->is_unlimited_project_managers,
                'is_unlimited_admins' => $plan->is_unlimited_admins,
            ])->all();

            $office->subscription()->create($subscriptionData);

            $mailData = [
                'name' => $office->name,
                'email' => $office->email,
                'password' => $request['password'],
            ];

            try {
                Mail::to($office->email)->send(new OfficeCredentials($mailData));
            } catch (Exception $e) {
                logger($e);
            }

            DB::commit();

            $tenantData = TenantData::from([
                'id' => $request['domain_name'],
                'office_id' => $office->id,
                'subscription_id' => $office->subscription->id,
                'domain_name' => "{$request['domain_name']}." . config('app.domain'),
                'name' => $request['domain_name'],
                'email' => $request['email'],
                'phone' => $request['phone'],
                'address' => $request['address'] ?? null,
                'tenancy_db_name' => 'engsaas_' . request('domain_name'),
                'password' => bcrypt($request['password']),
                'can_access_all_projects' => false,
            ])->all();

            $this->tenantService->create($tenantData);

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e);

            return error($e->getMessage());
        }
    }

    public function update(array $request, $id)
    {
        $office = $this->getById($id);

        $data = OfficeData::from($request)->all();

        if (isset($request['domain'])) {
            if (! $office->subscription->has_domain) {
                return error(__('You cannot add a custom domain to this office'));
            }

            if ($office->domains()->count() === 2) {
                return error(__('You cannot add a custom domain to this office'));
            }

            $office->domains()->create([
                'tenant_id' => $office->tenant->id,
                'domain' => $request['domain'],
            ]);
        }

        $office->update($data);

        if (request()->hasFile('image')) {
            $office->clearMediaCollection('offices');
            $office->addMedia(request()->image)
                ->toMediaCollection('offices');
        }

        return success(__('Updated Successfully'));
    }

    public function delete($id)
    {
        $office = $this->getById($id);

        $office->receivers()->delete();
        $office->subscription()->update(['status' => 'Disabled']);

        return $office->delete();
    }

    public function restore($id)
    {
        $office = $this->officeRepo->findTrashedById($id);

        if (! $office) {
            throw new NotFoundHttpException;
        }

        return $office->restore();
    }

    public function forceDelete($id)
    {
        $office = $this->officeRepo->findTrashedById($id);

        if (! $office) {
            throw new NotFoundHttpException;
        }

        return $office->forceDelete();
    }

    public function updatePassword(string $password, $id)
    {
        $office = $this->getById($id);

        $mailData = [
            'name' => $office->name,
            'email' => $office->email,
            'password' => $password,
        ];

        try {
            Mail::to($office->email)->send(new OfficeCredentials($mailData));
        } catch (Exception $e) {
            logger($e);
        }

        return $office->update(['password' => $password]);
    }

    public function count()
    {
        $filterable = request()->only(['start_date', 'end_date']);

        return $this->officeRepo->count($filterable);
    }

    public function getAll()
    {
        return $this->officeRepo->all();
    }

    public function deleteDomain($id)
    {
        $office = $this->getById($id);

        if (! $office->subscription->has_domain || $office->domains->count() === 1) {
            return error(__('You cannot delete the domain'));
        }

        $domain = $office->domains()->where('domain', request('domain'))->first();

        if (! $domain) {
            throw new NotFoundHttpException;
        }

        $domain->delete();

        return success(__('Deleted Successfully'));
    }

    public function ddl()
    {
        return $this->officeRepo->getWhere(['status' => 'Active']);
    }
}
