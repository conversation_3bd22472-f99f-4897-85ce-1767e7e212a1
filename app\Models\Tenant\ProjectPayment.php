<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectPayment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'number',
        'title',
        'project_id',
        'client_id',
        'amount',
        'date',
        'notes',
        'created_by',
        'permanent_deleted'
    ];

    /**
     * Get the project associated with the payment.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the client associated with the payment.
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the user who created the payment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function getCreatorAttribute()
    {
        return $this->transactions()->where('is_creator', 1)->withoutGlobalScopes()->first();
    }
}
