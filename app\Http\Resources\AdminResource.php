<?php

namespace App\Http\Resources;

use App\Http\Resources\Admin\PermissionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'image' => new MediaResource($this->image),
            'status' => $this->status,
            'role_id' => $this->role_id,
            'role' => $this->role?->name ?? 'Super Admin',
            'permissions' => PermissionResource::collection($this->permissions->groupBy('group')),
        ];
    }
}
