<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:service {model} {--C|controller : Generate a controller} {--R|routes : Generate API routes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Service and Repository for a given Model';

    protected $serviceTemplate = <<<'EOT'
        <?php

        namespace App\Services;

        use App\Repositories\{model}Repository;

        class {model}Service
        {
            public function __construct(protected {model}Repository ${model}Repository)
            {
                //
            }

            // Add your service methods here
        }
        EOT;

    protected $repositoryTemplate = <<<'EOT'
            <?php

            namespace App\Repositories;

            use App\Models\{model};

            class {model}Repository
            {
                public function __construct(protected {model} ${model})
                {
                    //
                }

                public function all()
                {
                    return $this->model->all();
                }

                public function findById($id)
                {
                    return $this->model->find($id);
                }

                public function create(array $data)
                {
                    return $this->model->create($data);
                }
            }
            EOT;

    protected $controllerTemplate = <<<'EOT'
            <?php

            namespace App\Http\Controllers;

            use App\Http\Controllers\Controller;
            use App\Services\{model}Service;

            class {model}Controller extends Controller
            {
                public function __construct(protected {model}Service ${model}Service)
                {
                    //
                }

                // Add your controller methods here
            }
            EOT;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $model = $this->argument('model');

        // Check if directories exist, if not create them
        if (! File::exists(base_path('app/Services'))) {
            File::makeDirectory(base_path('app/Services'));
        }

        if (! File::exists(base_path('app/Repositories'))) {
            File::makeDirectory(base_path('app/Repositories'));
        }

        // Create Service Class
        $servicePath = base_path("app/Services/{$model}Service.php");
        if (! File::exists($servicePath)) {
            $serviceContent = str_replace('{model}', $model, $this->serviceTemplate);
            File::put($servicePath, $serviceContent);
            $this->info("Service class created successfully: {$model}Service.php");
        } else {
            $this->error("Service class already exists: {$model}Service.php");
        }

        // Create Repository Class
        $repositoryPath = base_path("app/Repositories/{$model}Repository.php");
        if (! File::exists($repositoryPath)) {
            $repositoryContent = str_replace('{model}', $model, $this->repositoryTemplate);
            File::put($repositoryPath, $repositoryContent);
            $this->info("Repository class created successfully: {$model}Repository.php");
        } else {
            $this->error("Repository class already exists: {$model}Repository.php");
        }

        if ($this->option('controller')) {
            // Create Controller Class
            $controllerPath = base_path("app/Http/Controllers/{$model}Controller.php");
            if (! File::exists($controllerPath)) {
                $controllerContent = str_replace('{model}', $model, $this->controllerTemplate);
                File::put($controllerPath, $controllerContent);
                $this->info("Controller class created successfully: {$model}Controller.php");
            } else {
                $this->error("Controller class already exists: {$model}Controller.php");
            }
        }

        if ($this->option('routes')) {
            // Create API Routes
            $routesFilePath = base_path('routes/api.php');
            $controllerImport = "use App\\Http\\Controllers\\{$model}Controller;";
            $routeDefinition = "Route::apiResource('".strtolower(Str::plural($model))."', {$model}Controller::class);";

            // Read the existing routes file
            $routesFileContent = File::get($routesFilePath);

            // Check if the import statement already exists
            if (! str_contains($routesFileContent, $controllerImport)) {
                // Add the import statement after the opening PHP tag
                $routesFileContent = preg_replace(
                    '/^<\?php\s*/',
                    "<?php\n\n{$controllerImport}\n",
                    $routesFileContent
                );
            }

            // Check if the route definition already exists
            if (! str_contains($routesFileContent, $routeDefinition)) {
                // Append the route definition to the file
                $routesFileContent .= "\n".$routeDefinition;
            }

            // Write the updated content back to the file
            File::put($routesFilePath, $routesFileContent);

            $this->info("API routes added successfully for {$model}.");
        }
    }
}
