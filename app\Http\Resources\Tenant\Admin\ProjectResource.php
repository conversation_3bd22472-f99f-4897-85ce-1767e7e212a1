<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'image' => new MediaResource($this->image),
            'name' => $this->name,
            'start_date' => $this->start_date,
            'clients' => $this->clients->pluck('name')->toArray(),
            'type' => $this->type,
            'status' => $this->status,
        ];
    }
}
