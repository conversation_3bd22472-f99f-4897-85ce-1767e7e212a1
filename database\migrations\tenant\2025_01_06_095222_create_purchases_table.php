<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('number')->unique();
            $table->foreignId('project_id')->constrained('projects')->cascadeOnDelete();
            $table->foreignId('partner_id')->constrained('partners')->cascadeOnDelete();
            $table->enum('type', ['detailed', 'printed']);
            $table->date('date');
            $table->double('total_before_discount')->default(0);
            $table->enum('discount_type', ['fixed', 'percentage'])->nullable();
            $table->double('discount_amount')->default(0);
            $table->double('total_after_discount')->default(0);
            $table->enum('status', ['paid', 'partial', 'unpaid'])->default('unpaid');
            $table->double('paid_amount')->default(0);
            $table->double('remaining_amount')->default(0);
            $table->longText('notes')->nullable();
            $table->string('created_by')->nullable();
            $table->boolean('permanent_deleted')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchases');
    }
};
