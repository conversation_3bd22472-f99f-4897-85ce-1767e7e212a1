<?php

namespace App\Models\Tenant;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Refund extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'project_id',
        'type',
        'purchase_id',
        'name',
        'date',
        'supplier_id',
        'total_before_discount',
        'discount_type',
        'number',
        'discount_value',
        'total_after_discount',
        'status',
        'paid_amount',
        'remaining_amount',
        'notes',
        'permanent_deleted',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    public function getDateAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d');
    }

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->format('Y-m-d');
    }

    public function getImageAttribute()
    {
        return $this->getFirstMedia('refunds');
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function invoiceCategory()
    {
        return $this->morphMany(InvoiceCategory::class, 'invoiceable');
    }

    public function items()
    {
        return $this->hasManyThrough(
            RefundItem::class,
            InvoiceCategory::class,
            'invoiceable_id', // Foreign key on `invoice_categories` table
            'invoice_category_id', // Foreign key on `refund_items` table
            'id', // Local key on `refunds` table
            'id' // Local key on `invoice_categories` table
        )->where('invoiceable_type', Refund::class);
    }


    /**
     * Relationship with Purchase.
     */
    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * Relationship with Supplier (Partner).
     */
    public function supplier()
    {
        return $this->belongsTo(Partner::class, 'supplier_id')->withTrashed();
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }
}

