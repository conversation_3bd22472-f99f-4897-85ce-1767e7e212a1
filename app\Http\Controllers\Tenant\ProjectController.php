<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateProjectRequest;
use App\Http\Requests\Tenant\Admin\UpdateProjectRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\ProjectDetailsResource;
use App\Http\Resources\Tenant\Admin\ProjectResource;
use App\Http\Resources\Tenant\DDLResource;
use App\Services\Tenant\ProjectService;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class ProjectController extends Controller implements HasMiddleware
{
    public function __construct(public ProjectService $projectService) {}

    public static function middleware(): array
    {
        return [
            new Middleware('can_create_project', ['store']),
        ];
    }

    public function index()
    {
        $projects = $this->projectService->get();

        return success(new BaseCollection($projects, ProjectResource::class));
    }

    public function store(CreateProjectRequest $request)
    {
        return $this->projectService->create($request->validated());
    }

    public function show(string $id)
    {
        $project = $this->projectService->getById($id);

        return success(new ProjectDetailsResource($project));
    }

    public function update(UpdateProjectRequest $request, string $id)
    {
        return $this->projectService->update($request->validated(), $id);
    }

    public function deleteMedia(string $projectId, string $mediaId)
    {
        return $this->projectService->deleteMedia($projectId, $mediaId);
    }

    public function ddl()
    {
        $projects = $this->projectService->ddl();

        return success(DDLResource::collection($projects));
    }
}
