<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\RoomService;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\ChatRoomResource;

class ChatRoomController extends Controller
{
    public function __construct(public RoomService $roomService) {}

    public function __invoke()
    {
        $rooms = $this->roomService->userChatRooms();
        $unreadCount = $this->roomService->unreadRoomsCount();

        return success(new BaseCollection($rooms, ChatRoomResource::class, [
            'unread_count' => $unreadCount,
        ]));
    }
}
