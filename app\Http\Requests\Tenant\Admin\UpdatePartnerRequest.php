<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Repositories\Tenant\PartnerRepository;
use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UpdatePartnerRequest extends FormRequest
{
    public function __construct(protected PartnerRepository $partnerRepo)
    {
        //
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $partner = $this->partnerRepo->findById($this->route('partner'));

        if (! $partner) {
            throw new NotFoundHttpException('Partner not found');
        }

        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'mobile' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('partners', 'mobile', ignoreId: $partner->id)],
            'email' => ['sometimes', 'email', 'unique:partners,email,' . $partner->id, 'min:3', 'max:100'],
            'address' => ['sometimes', 'string', 'min:3', 'max:100'],
            'details' => ['sometimes', 'string', 'min:3', 'max:255'],
            'national_id' => [
                'sometimes',
                'string',
                'digits:14',
                'unique:partners,national_id,' . $partner->id,
            ],
            'items' => ['required', 'array', 'min:1'],
            'items.*' => ['required', 'exists:items,id'],
            'status' => ['required', 'in:active,disabled'],
        ];
    }
}
