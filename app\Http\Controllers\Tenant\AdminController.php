<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateAdminRequest;
use App\Http\Requests\Tenant\Admin\UpdateAdminRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\AdminResource;
use App\Services\Tenant\AdminService;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class AdminController extends Controller implements HasMiddleware
{
    public function __construct(public AdminService $adminService) {}

    public static function middleware(): array
    {
        return [
            new Middleware('can_create_admin', ['store']),
        ];
    }

    public function index()
    {
        $admins = $this->adminService->get();

        return success(new BaseCollection($admins, AdminResource::class));
    }

    public function store(CreateAdminRequest $request)
    {
        return $this->adminService->create($request->validated());
    }

    public function show(string $id)
    {
        $admin = $this->adminService->getById($id);

        return success(new AdminResource($admin));
    }

    public function update(UpdateAdminRequest $request, string $id)
    {
        return $this->adminService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        $this->adminService->delete($id);

        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $this->adminService->restore($id);

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        return $this->adminService->forceDelete($id);
    }
}
