<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $show_route = 'tasks.show';
        $createdActivity = $this->activities->firstWhere('action', 'created');

        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'status' => $this->status,
            'priority' => $this->priority,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'images' => $this->when($request->routeIs($show_route), MediaResource::collection($this->whenLoaded('media'))),

            'project' => [
                'id' => $this->project->id,
                'name' => $this->project->name,
            ],
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
            ],
            'history' => $this->when($request->routeIs($show_route),  TaskHistoryResource::collection($this->whenLoaded('activities'))),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => optional($createdActivity?->user)->name,
        ];
    }
}
