<?php

namespace App\Http\Resources\Tenant;

use App\Http\Resources\Admin\PermissionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoginResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'address' => $this->address,
            'image' => MediaResource::make($this->image),
            'type' => $this->type,
            'status' => $this->status,
            'phone' => $this->phone,
            'balance' => $this->balance,
            'role_id' => $this->role_id,
            'role' => $this->role?->name,
            'permissions' => PermissionResource::collection($this->permissions->groupBy('group')),
        ];
    }
}
