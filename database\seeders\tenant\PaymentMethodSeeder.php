<?php

namespace Database\Seeders\tenant;

use App\Models\Tenant\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        PaymentMethod::insert([
            [
                'id' => 1,
                'name_ar' => 'نسبة مئوية من المصاريف',
                'name_en' => 'Percentage from expenses',
            ],
            [
                'id' => 2,
                'name_ar' => 'سعر ثابت (إشراف وتنفيذ)',
                'name_en' => 'Fixed price (supervision and implementation)',
            ],
            [
                'id' => 3,
                'name_ar' => 'سعر ثابت (إشراف)',
                'name_en' => 'Fixed price (supervision)',
            ],
            [
                'id' => 4,
                'name_ar' => 'سعر بالمتر (إشراف وتنفيذ)',
                'name_en' => 'Price per meter (supervision and implementation)',
            ],
            [
                'id' => 5,
                'name_ar' => 'سعر بالمتر (إشراف)',
                'name_en' => 'Price per meter (supervision)',
            ],
            [
                'id' => 6,
                'name_ar' => 'باقات',
                'name_en' => 'Packages',
            ],
        ]);
    }
}
