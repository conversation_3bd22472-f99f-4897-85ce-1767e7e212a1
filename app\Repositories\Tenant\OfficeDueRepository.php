<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\OfficeDue;

class OfficeDueRepository
{
    public function __construct(protected OfficeDue $model)
    {
        //
    }

    public function all()
    {
        return $this->model->all();
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function calculate(array $data)
    {
        return $this->model->updateOrCreate(['project_id' => $data['project_id']], $data);
    }


    public function findByProjectId()
    {
        return $this->model->with(['project.payments', 'project.paymentMethod', 'project.officeDueTransactions'])->where('project_id' , request()->project_id)->first();
    }
}
