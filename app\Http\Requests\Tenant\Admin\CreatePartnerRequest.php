<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CreatePartnerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'mobile' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('partners', 'mobile')],
            'email' => ['sometimes', 'email', 'unique:partners,email', 'min:3', 'max:100'],
            'address' => ['sometimes', 'string', 'min:3', 'max:100'],
            'details' => ['sometimes', 'string', 'min:3', 'max:255'],
            'type' => ['required', 'string', 'in:supplier,contractor'],
            'national_id' => ['sometimes', 'string', 'digits:14', 'unique:partners,national_id'],
            'items' => ['required', 'array', 'min:1'],
            'items.*' => ['required', 'exists:items,id'],
        ];
    }
}
