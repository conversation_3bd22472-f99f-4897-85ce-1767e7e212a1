<?php

namespace App\Services\Tenant;

use App\Models\Tenant\Partner;
use App\DTO\Tenant\PurchaseData;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\DebtPaymentData;
use App\Repositories\Tenant\PartnerRepository;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\PurchaseRepository;
use App\Repositories\Tenant\DebtPaymentRepository;
use App\Services\Tenant\OfficeDueService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\Tenant\Traits\HasSufficientBalanceCheck;


class PurchaseService
{
    use HasSufficientBalanceCheck;
    public function __construct(
        protected PurchaseRepository $purchaseRepo,
        protected ProjectRepository $projectRepo,
        protected DebtPaymentRepository $debtPaymentRepo,
        protected SettingRepository $settingRepository,
        protected PartnerRepository $partnerRepository,
        protected OfficeDueService $officeDueService
    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'partner', 'status', 'type', 'date_from', 'date_to']);
        return request()->type === 'archive'
            ? $this->purchaseRepo->getTrashedWithPaginate()
            : $this->purchaseRepo->getPaginated($filterable);
    }

    public function create(array $request)
    {
        DB::beginTransaction();
        try {
            $user = auth()->user();
            $this->prepareRequestForCreate($request, $user);
            $data = PurchaseData::from($request)->all();

            $this->assertSufficientBalance(
                'purchase',
                $request['paid_amount'],
                null,
                ['user_id' => $user->id, 'project_id' => $data['project_id']],
                $this->settingRepository
            );

            $purchase = $this->purchaseRepo->create($data);
            $this->updateBalance($request, $user);
            $this->updatePartnerBalance($request['partner_id'], $request['remaining_amount']);
            $this->calcProjectDebt(
                $request,
                [
                    'total_after_discount' => $purchase->total_after_discount,
                    'paid_amount' => $purchase->paid_amount,
                ]
            );
            $this->handleImages($request, $purchase);
            $this->handleCategoryItems($request, $purchase);
            $this->createTransaction($purchase, $user, $request['paid_amount'], 'add', "{$user->name} added new purchase {$purchase->number}");

            DB::commit();
            $this->officeDueService->createOrUpdate($request['project_id']);
            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function getById(string $id)
    {
        $purchase = $this->purchaseRepo->findById($id);
        if (!$purchase) {
            throw new NotFoundHttpException;
        }
        return $purchase;
    }

    public function update(string $id, array $request)
    {
        $user = auth()->user();
        $purchase = $this->getById($id);
        $oldPartnerId = $purchase->partner_id;
        $oldTotalAfterDiscount = $purchase->total_after_discount;
        $oldPaidAmount = $purchase->paid_amount;
        $purchaseCreator = $purchase->transactions()->creator()->first()->userable;
        if (!$purchaseCreator) {
            throw new NotFoundHttpException('Purchase creator not found');
        }
        DB::beginTransaction();
        try {
            $this->prepareRequestForUpdate($request, $purchase);
            $request['project_id'] = $purchase->project_id;
            $data = PurchaseData::from($request)->all();
            $this->assertSufficientBalance(
                'purchase',
                $request['paid_amount'],
                $purchase->paid_amount,
                ['user_id' => $purchaseCreator->id, 'project_id' => $request['project_id']],
                $this->settingRepository
            );
            $this->updateBalance($request, $purchaseCreator, $purchase);
            $this->updatePartnerBalance($request['partner_id'], $purchase->remaining_amount - $request['remaining_amount']);
            $purchase->update($data);
            $this->updateProjectDebtOnUpdate($request, $purchase, $oldPartnerId, $oldTotalAfterDiscount, $oldPaidAmount);
            $this->handleImages($request, $purchase);
            $this->handleCategoryItems($request, $purchase, $purchase->type);
            $this->createTransaction($purchase, $user, $request['paid_amount'], 'update', "{$user->name} updated purchase {$purchase->number}");
            DB::commit();
            $this->officeDueService->createOrUpdate($purchase->project_id);
            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function delete(string $id)
    {
        $purchase = $this->getById($id);
        $projectId = $purchase->project_id;
        DB::beginTransaction();
        try {
            $project = $this->projectRepo->findById($purchase->project_id);
            $purchaseCreator = $purchase->transactions()->creator()->first()->userable;
            $purchaseCreator->increment('balance', $purchase->paid_amount);
            $project->increment('balance', $purchase->paid_amount);
            $debt = $purchase->project->asDebtor()->where('creditor_id', $purchase->partner_id)->first();
            $debt->update([
                'total_invoices' => $debt->total_invoices - $purchase->total_after_discount,
                'total_debt' => ($debt->total_invoices - $purchase->total_after_discount) - $debt->total_payments,
            ]);
            $purchase->delete();
            DB::commit();
            $this->officeDueService->createOrUpdate($projectId);
            return success(__('Deleted Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return error($e->getMessage());
        }
    }

    public function restore(string $id)
    {
        $purchase = $this->purchaseRepo->findTrashedById($id);
        $projectId = $purchase->project_id;
        if (!$purchase || $purchase->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        DB::beginTransaction();
        try {
            $project = $this->projectRepo->findById($purchase->project_id);
            $purchaseCreator = $purchase->transactions()->creator()->first()->userable;
            $purchaseCreator->decrement('balance', $purchase->paid_amount);
            $project->decrement('balance', $purchase->paid_amount);
            $debt = $purchase->project->asDebtor()->where('creditor_id', $purchase->partner_id)->first();
            $debt->update([
                'total_invoices' => $debt->total_invoices + $purchase->total_after_discount,
                'total_debt' => ($debt->total_invoices + $purchase->total_after_discount) - $debt->total_payments,
            ]);
            $purchase->restore();
            DB::commit();
            $this->officeDueService->createOrUpdate($projectId);
            return success(__('Restored Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return error($e->getMessage());
        }
    }

    public function forceDelete(string $id)
    {
        $purchase = $this->purchaseRepo->findTrashedById($id);
        if (!$purchase || $purchase->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $purchase->update([
            'permanent_deleted' => true,
            'deleted_at' => now(),
        ]);
        return success(__('Deleted Successfully'));
    }

    public function deleteMedia(string $purchaseId, string $mediaId)
    {
        $purchase = $this->getById($purchaseId);
        $media = $purchase->media()->find($mediaId);
        if (!$media) {
            throw new NotFoundHttpException;
        }
        $media->delete();
        return success(__('Deleted Successfully'));
    }

    public function getTotal()
    {
        return $this->purchaseRepo->getPurchasesTotal();
    }

    public function pay(array $request, string $id)
    {
        $purchase = $this->getById($id);
        DB::beginTransaction();
        try {
            $debt = $purchase->project->asDebtor()
                ->where(function ($query) use ($purchase) {
                    $query->where('creditor_id', $purchase->partner_id);
                })
                ->first();
            $user = auth()->user();
            if ((int) $debt->total_debt === 0) {
                return error(__('There is no debt to pay.'), 422);
            }
            $amountToPay = $request['amount'];
            if ($amountToPay > $debt->total_debt) {
                return error(__('The amount must be equal to or less than the total debt.'), 422);
            }
            $paidAmount = $debt->total_payments + $amountToPay;
            $remainingDebt = $debt->total_debt - $amountToPay;
            $data = DebtPaymentData::from([
                'debt_id' => $debt->id,
                'user_id' => $user->id,
                'total_debt' => $debt->total_debt,
                'remaining_debt' => $remainingDebt,
                'paid_amount' => $amountToPay,
            ])->all();
            $this->debtPaymentRepo->create($data);
            $debt->update([
                'total_payments' => $paidAmount,
                'total_debt' => $remainingDebt,
            ]);
            $purchase->project->decrement('balance', $amountToPay);
            $user->decrement('balance', $amountToPay);
            $this->createTransaction($purchase, $user, $amountToPay, 'pay', "{$user->name} paid {$amountToPay} to {$purchase->project->name}");
            DB::commit();
            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            logger($e);
            DB::rollBack();
            return error(__('An error occurred: ') . $e->getMessage(), 500);
        }
    }

    public function ddl(?array $columns = ['id', 'name', 'partner_id'])
    {
        return $this->purchaseRepo->getDdl($columns);
    }

    protected function prepareRequestForCreate(array &$request, $user): void
    {
        $request['number'] = generateInvoiceNumber();
        $request['created_by'] = $user->name;
        if ($request['type'] === 'detailed') {
            $calc = $this->calcPurchaseItems($request['categories']);
            $request['total_before_discount'] = $calc['overall_total'];
            $request['_calc'] = $calc;
        } else {
            $request['total_before_discount'] = $request['total_before_discount'];
        }
        $this->applyDiscount($request);
        if (
            $request['status'] === 'partial' &&
            $request['paid_amount'] >= $request['total_after_discount'] &&
            !($request['paid_amount'] == 0 && $request['total_after_discount'] == 0)
        ) {
            throw new \Exception(__('Paid amount must be less than total amount'));
        }
        $this->calculatePaymentStatus($request);
        if ($request['remaining_amount'] == 0) {
            $request['status'] = 'paid';
        }
    }

    protected function prepareRequestForUpdate(array &$request, $purchase): void
    {
        if ($purchase->type === 'detailed') {
            $calc = $this->calcPurchaseItems($request['categories']);
            $request['total_before_discount'] = $calc['overall_total'];
            $request['_calc'] = $calc;
        } else {
            $request['total_before_discount'] = $request['total_before_discount'];
        }
        $this->applyDiscount($request);
        if (
            $request['status'] === 'partial' &&
            $request['paid_amount'] >= $request['total_after_discount'] &&
            !($request['paid_amount'] == 0 && $request['total_after_discount'] == 0)
        ) {
            throw new \Exception(__('Paid amount must be less than total amount'));
        }
        $this->calculatePaymentStatus($request);
        if ($request['remaining_amount'] == 0) {
            $request['status'] = 'paid';
        }
    }

    protected function applyDiscount(array &$request): void
    {
        $overallTotal = $request['total_before_discount'];
        $request['discount_amount'] = $request['discount_amount'] ?? 0;
        if (isset($request['discount_type']) && $request['discount_type'] === 'percentage') {
            $request['total_after_discount'] = $overallTotal - ($overallTotal * ($request['discount_amount'] / 100));
        } else {
            $request['total_after_discount'] = $overallTotal - $request['discount_amount'];
        }

        if ($request['total_after_discount'] < 0) {
            $request['total_after_discount'] = 0;
        }
    }

    protected function calcPurchaseItems(array $items): array
    {
        $categories = collect($items);
        $categories->transform(function ($category) {
            $category['items'] = collect($category['items'])->transform(function ($item) {
                $item['total'] = $item['price'] * $item['qty'];
                return $item;
            });
            $category['total'] = $category['items']->sum('total');
            return $category;
        });
        $overallTotal = $categories->sum('total');
        return [
            'categories' => $categories->toArray(),
            'overall_total' => $overallTotal,
        ];
    }

    protected function calculatePaymentStatus(array &$request): void
    {
        switch ($request['status']) {
            case 'paid':
                $request['paid_amount'] = $request['total_after_discount'];
                $request['remaining_amount'] = 0;
                break;
            case 'partial':
                $request['remaining_amount'] = $request['total_after_discount'] - ($request['paid_amount'] ?? 0);
                break;
            default:
                $request['paid_amount'] = 0;
                $request['remaining_amount'] = $request['total_after_discount'];
                break;
        }
    }

    protected function createCategoryItems(array $calc, $purchase): void
    {
        foreach ($calc['categories'] as $category) {
            $invoiceCategory = $purchase->invoiceCategory()->updateOrCreate(
                ['item_id' => $category['id']],
                ['item_id' => $category['id']]
            );
            $invoiceCategory->items()->delete();
            $invoiceCategory->items()->createMany($category['items']);
        }
    }

    protected function updateBalance(array $request, $user, $purchase = null): void
    {
        $project = $this->projectRepo->findById($request['project_id']);
        $purchasePaidAmount = $purchase->paid_amount ?? 0;
        $user->decrement('balance', ($request['paid_amount'] - $purchasePaidAmount));
        $project->decrement('balance', ($request['paid_amount'] - $purchasePaidAmount));
    }

    protected function updatePartnerBalance($partnerId, $amount): void
    {
        $partner = $this->partnerRepository->findById($partnerId);
        $partner->update(['balance' => $partner->balance + $amount]);
    }

    protected function calcProjectDebt(array $request, array $purchaseAmounts, ?int $oldPartnerId = null, array $oldPurchaseAmounts = []): void
    {
        $project = $this->projectRepo->findById($request['project_id']);
        $debt = $project->asDebtor()->where('creditor_id', $request['partner_id'])->firstOrCreate([
            'creditor_id' => $request['partner_id'],
            'creditor_type' => Partner::class,
            'type' => 'purchase',
        ]);
        if ($oldPartnerId) {
            $oldDebt = $project->asDebtor()->where(function ($query) use ($oldPartnerId) {
                $query->where('type', 'purchase')
                    ->where('creditor_id', $oldPartnerId);
            })->first();
            if ($oldDebt) {
                $oldDebt->update([
                    'total_invoices' => $oldDebt->total_invoices - $oldPurchaseAmounts['total_after_discount'],
                    'total_payments' => $oldDebt->total_payments - $oldPurchaseAmounts['paid_amount'],
                    'total_debt' => $oldDebt->total_debt - ($oldPurchaseAmounts['total_after_discount'] - $oldPurchaseAmounts['paid_amount']),
                ]);
            }
        }
        $debt->update([
            'total_invoices' => $debt->total_invoices + $purchaseAmounts['total_after_discount'],
            'total_payments' => $debt->total_payments + $purchaseAmounts['paid_amount'],
            'total_debt' => $debt->total_debt + ($purchaseAmounts['total_after_discount'] - $purchaseAmounts['paid_amount']),
        ]);
    }

    protected function updateProjectDebtOnUpdate(array $request, $purchase, $oldPartnerId, $oldTotalAfterDiscount, $oldPaidAmount): void
    {
        if ($oldPartnerId != $request['partner_id']) {
            $this->calcProjectDebt(
                $request,
                ['total_after_discount' => $purchase->total_after_discount, 'paid_amount' => $purchase->paid_amount],
                $oldPartnerId,
                ['total_after_discount' => $oldTotalAfterDiscount, 'paid_amount' => $oldPaidAmount],
            );
        } else {
            $this->calcProjectDebt(
                $request,
                ['total_after_discount' => $purchase->total_after_discount, 'paid_amount' => $purchase->paid_amount]
            );
        }
    }

    protected function handleImages(array $request, $purchase): void
    {
        if (!empty($request['images'])) {
            foreach (request()->file('images') as $image) {
                $purchase->addMedia($image)
                    ->toMediaCollection('purchases');
            }
        }
    }

    protected function handleCategoryItems(array $request, $purchase, $type = null): void
    {
        $type = $type ?? $request['type'];
        if ($type === 'detailed') {
            $this->createCategoryItems($request['_calc'], $purchase);
        } else {
            $purchase->invoiceCategory()->create([
                'item_id' => $request['item_id'],
            ]);
        }
    }

    protected function createTransaction($purchase, $user, $amount, $type, $message): void
    {
        $purchase->transactions()->create([
            'amount' => $amount,
            'is_creator' => $type === 'add',
            'userable_id' => $user->id,
            'userable_type' => $user->getMorphClass(),
            'type' => $type,
            'message' => $message,
        ]);
    }
}
