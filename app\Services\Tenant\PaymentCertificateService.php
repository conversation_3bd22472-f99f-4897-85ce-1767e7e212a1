<?php

namespace App\Services\Tenant;

use Exception;
use App\Services\Tenant\Traits\HasSufficientBalanceCheck;
use App\Models\Tenant\Partner;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\DebtPaymentData;
use App\DTO\Tenant\PaymentCertificationData;
use App\Repositories\Tenant\PartnerRepository;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\DebtPaymentRepository;
use App\Repositories\Tenant\TransactionRepository;
use App\Repositories\Tenant\PaymentCertificateRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;


class PaymentCertificateService
{
    use HasSufficientBalanceCheck;
    public function __construct(
        protected PaymentCertificateRepository $paymentCertificateRepository,
        protected TransactionRepository $transactionRepository,
        protected ContractorPaymentService $contractorPaymentService,
        protected PartnerRepository $partnerRepository,
        protected ProjectRepository $projectRepository,
        protected DebtPaymentRepository $debtPaymentRepository,
        protected SettingRepository $settingRepository
    ) {}

    public function get()
    {
        $filterable = request()->only([
            'search',
            'contractor',
            'date_from',
            'date_to',
        ]);
        return request()->type === 'archive'
            ? $this->paymentCertificateRepository->getTrashedWithPaginate()
            : $this->paymentCertificateRepository->getPaginated($filterable);
    }

    public function getById(string $id)
    {
        $certificate = $this->paymentCertificateRepository->findById($id);
        if (! $certificate) {
            throw new NotFoundHttpException;
        }
        return $certificate;
    }

    public function create(array $request)
    {
        DB::beginTransaction();
        try {
            $user = request()->user();
            $data = PaymentCertificationData::from($request)->all();
            $data['created_by'] = $user->name;
            $contractor = $this->partnerRepository->findById($request['contractor_id']);
            if (! $contractor) {
                throw new NotFoundHttpException('Contractor not found');
            }
            $certificate = $this->paymentCertificateRepository->create($data);
            $total = $this->createOrUpdateCertificateItems($certificate, $request['items'], $data['item_id'] ?? null);
            $certificate->update(['total' => $total]);
            $this->assertSufficientBalance(
                'payment_certificate',
                $total,
                null,
                ['user_id' => $user->id, 'project_id' => $certificate->project_id],
                $this->settingRepository
            );
            $this->updateProjectDebt($request, ['total' => $total]);
            $this->handleCertificateImages($certificate, $request['images'] ?? []);
            $this->updateBalances($user, $contractor, $total, $certificate->project_id ?? null);
            DB::commit();
            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();
        $certificate = $this->paymentCertificateRepository->findById($id);
        if (! $certificate) {
            throw new NotFoundHttpException;
        }
        $user = request()->user();
        $oldContractorId = $certificate->contractor_id;
        $oldCertificateTotal = $certificate->total;
        $contractorId = $request['contractor_id'] ?? $certificate->contractor_id;
        $request['contractor_id'] = $contractorId;
        try {
            $data = PaymentCertificationData::from($request)->all();
            $data['total'] = collect($request['items'])->sum(fn($item) => $item['price'] * $item['qty']);
            $this->assertSufficientBalance(
                'payment_certificate',
                $data['total'],
                $oldCertificateTotal,
                ['user_id' => $user->id, 'project_id' => $certificate->project_id],
                $this->settingRepository
            );
            $contractor = $this->partnerRepository->findById($contractorId);
            if (! $contractor) {
                return error('Contractor not found', 404);
            }
            if ($oldContractorId != $contractorId) {
                $oldContractor = $this->partnerRepository->getwithoutglobalscopes($oldContractorId);
                if (! $oldContractor) {
                    return error('Previous contractor not found', 404);
                }
                $this->updateProjectDebt($request, ['total' => $data['total']], $oldContractorId, ['total' => $oldCertificateTotal]);
                $oldContractor->update(['balance' => $oldContractor->balance - $oldCertificateTotal]);
                $contractor->update(['balance' => $contractor->balance + $data['total']]);
            } else {
                $amountDifference = $data['total'] - $oldCertificateTotal;
                if ($amountDifference != 0) {
                    $contractor->update(['balance' => $contractor->balance + $amountDifference]);
                }
                $this->updateProjectDebt($request, ['total' => $data['total']]);
            }
            $this->paymentCertificateRepository->update($certificate, $data);
            $certificate->transactions()->create([
                'amount' => $data['total'],
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'update',
                'message' => "{$user->name} updated payment certificate {$certificate->title}",
            ]);
            $certificate->certificationItems()->delete();
            $this->createOrUpdateCertificateItems($certificate, $request['items'], $data['item_id'] ?? null);
            $this->handleCertificateImages($certificate, $request['images'] ?? []);
            DB::commit();
            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function delete(string $id)
    {
        $certificate = $this->getById($id);
        $debt = $certificate->project->asDebtor()->where('creditor_id', $certificate->contractor_id)->first();
        $debt->update([
            'total_invoices' => $debt->total_invoices - $certificate->total,
            'total_debt' => ($debt->total_invoices - $certificate->total) - $debt->total_payments,
        ]);
        $certificate->delete();
        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $certificate = $this->paymentCertificateRepository->findTrashedById($id);
        if (! $certificate || $certificate->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $debt = $certificate->project->asDebtor()->where('creditor_id', $certificate->contractor_id)->first();
        $debt->update([
            'total_invoices' => $debt->total_invoices + $certificate->total,
            'total_debt' => ($debt->total_invoices + $certificate->total) - $debt->total_payments,
        ]);
        $certificate->restore();
        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $record = $this->paymentCertificateRepository->findTrashedById($id);
        if (! $record || $record->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $record->update([
            'permanent_deleted' => true,
            'deleted_at' => now(),
        ]);
        return success(__('Deleted Successfully'));
    }

    public function pay(string $id, array $request)
    {
        $record = $this->getById($id);
        DB::beginTransaction();
        try {
            $debt = $record->project->asDebtor()->where(function ($query) use ($record) {
                $query->where('type', 'certification')
                    ->where('creditor_id', $record->contractor_id);
            })->first();
            $user = request()->user();
            if ((int) $debt->total_debt === 0) {
                return error(__('There is no debt to pay.'), 422);
            }
            $amountToPay = $request['amount'];
            if ($amountToPay > $debt->total_debt) {
                return error(__('The amount must be equal to or less than the total debt.'), 422);
            }
            $this->assertSufficientBalance(
                'payment_certificate',
                $amountToPay,
                null,
                ['user_id' => $user->id, 'project_id' => $record->project->id],
                $this->settingRepository
            );
            $paidAmount = $debt->total_payments + $amountToPay;
            $remainingDebt = $debt->total_debt - $amountToPay;
            $data = DebtPaymentData::from([
                'debt_id' => $debt->id,
                'user_id' => $user->id,
                'total_debt' => $debt->total_debt,
                'remaining_debt' => $remainingDebt,
                'paid_amount' => $amountToPay,
                'notes' => $request['notes'] ?? null,
            ])->all();
            $this->debtPaymentRepository->create($data);
            $debt->update([
                'total_payments' => $paidAmount,
                'total_debt' => $remainingDebt,
            ]);
            $this->contractorPaymentService->create([
                'date' => now(),
                'name' => $record->title,
                'contractor_id' => $record->contractor_id,
                'project_id' => $record->project_id,
                'item_id' => $record->item_id,
                'amount' => $amountToPay,
                'created_by' => $user->id,
                'notes' => $data['notes'],
            ]);
            DB::commit();
            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return error(__('An error occurred: ') . $e->getMessage(), 500);
        }
    }

    public function getTotalCertificate()
    {
        return $this->paymentCertificateRepository->totalCertificates(request()->project);
    }

    // --- Internal Helpers ---

    private function createOrUpdateCertificateItems($certificate, array $items, $defaultItemId = null)
    {
        $total = 0;
        foreach ($items as $item) {
            $itemTotal = $item['price'] * $item['qty'];
            $certificate->certificationItems()->create([
                'item_id' => $item['item_id'] ?? $defaultItemId,
                'unit_id' => $item['unit_id'],
                'item_name' => $item['item_name'],
                'qty' => $item['qty'],
                'price' => $item['price'],
                'total' => $itemTotal,
            ]);
            $total += $itemTotal;
        }
        return $total;
    }

    // ...existing code...

    private function updateProjectDebt(array $request, array $certificateAmounts, ?int $oldContractorId = null, array $oldCertificateAmounts = [])
    {
        $project = $this->projectRepository->findById($request['project_id']);
        $debt = $project->asDebtor()->where('creditor_id', $request['contractor_id'])->firstOrCreate([
            'creditor_id' => $request['contractor_id'],
            'creditor_type' => Partner::class,
            'type' => 'certification',
        ]);
        if ($oldContractorId) {
            $oldDebt = $project->asDebtor()->where(function ($query) use ($oldContractorId) {
                $query->where('type', 'certification')
                    ->where('creditor_id', $oldContractorId);
            })->first();
            if ($oldDebt) {
                $oldDebt->update([
                    'total_invoices' => $oldDebt->total_invoices - $oldCertificateAmounts['total'],
                    'total_debt' => $oldDebt->total_debt - $oldCertificateAmounts['total'],
                ]);
            }
        }
        $debt->update([
            'total_invoices' => $debt->total_invoices + $certificateAmounts['total'],
            'total_debt' => $debt->total_debt + $certificateAmounts['total'],
        ]);
    }

    private function handleCertificateImages($certificate, $images)
    {
        if (!empty($images)) {
            foreach (request()->file('images') as $image) {
                $certificate->addMedia($image)
                    ->toMediaCollection('payment-certificates');
            }
        }
    }

    private function updateBalances($user, $contractor, $amountAdjustment, $projectId = null)
    {
        $user->update(['balance' => $user->balance - $amountAdjustment]);
        $contractor->update(['balance' => $contractor->balance + $amountAdjustment]);
        if ($projectId) {
            $project = $this->projectRepository->findById($projectId);
            if (! $project) {
                throw new NotFoundHttpException('Project not found');
            }
            $project->update(['balance' => $project->balance - $amountAdjustment]);
        }
    }
}
