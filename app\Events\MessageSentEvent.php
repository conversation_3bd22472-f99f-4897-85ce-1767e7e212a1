<?php

namespace App\Events;

use App\Http\Resources\Tenant\MessageResource;
use App\Models\Tenant\Message;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSentEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private $recipients;

    public function __construct(private Message $message)
    {
        $this->dontBroadcastToCurrentUser();

        // Get all users in the chat room except the sender
        $this->recipients = $this->message->room->users()
            ->where('users.id', '!=', $this->message->sender_id)
            ->pluck('users.id')
            ->toArray();
    }

    public function broadcastOn(): array
    {
        $tenantId = tenant()->id;

        $channels = [
            new PrivateChannel("chat-{$tenantId}-{$this->message->room_id}"),
        ];

        // Broadcast to individual users
        foreach ($this->recipients as $userId) {
            $channels[] = new PrivateChannel("user-{$tenantId}-{$userId}");
        }

        return $channels;
    }

    public function broadcastAs()
    {
        return 'message.sent';
    }

    public function broadcastWith()
    {
        return (new MessageResource($this->message))->resolve();
    }
}
