<?php

namespace App\Events;

use App\Models\Tenant\Message;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageNotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private $recipients;

    public function __construct(private Message $message)
    {
        $this->dontBroadcastToCurrentUser();

        // Get all users in the chat room except the sender
        $this->recipients = $this->message->room->users()
            ->where('users.id', '!=', $this->message->sender_id)
            ->pluck('users.id')
            ->toArray();
    }

    public function broadcastOn(): array
    {
        $tenantId = tenant()->id;

        $channels = [];
        foreach ($this->recipients as $userId) {
            $channels[] = new PrivateChannel("user-{$tenantId}-{$userId}");
        }

        return $channels;
    }

    public function broadcastAs()
    {
        return 'new-message-notification';
    }

    public function broadcastWith()
    {
        return [
            'message' => $this->message->body,
            'sender' => $this->message->sender->name,
            'chat_id' => $this->message->room_id,
        ];
    }
}
