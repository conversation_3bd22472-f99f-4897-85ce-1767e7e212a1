<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Localization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->header('Accept-Language', 'en');
    
        // Normalize locale and set default if needed
        if ($locale === 'en-US,en;q=0.9') {
            $locale = 'en';
        }
    
        app()->setLocale($locale);
    
        return $next($request);
    }
    
}
