<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_certificates', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->date('date');
            $table->foreignId('project_id')->constrained('projects')->cascadeOnDelete();
            $table->foreignId('contractor_id')->constrained('partners')->cascadeOnDelete();
            $table->double('total')->default(0);
            $table->string('created_by');
            $table->boolean('permanent_deleted')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_certificates');
    }
};
