<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Office;

class OfficeRepository
{
    public function __construct(public Office $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where(function ($q) use ($filters) {
                $q->where('name_ar', 'like', "%{$filters['search']}%")
                    ->orWhere('name_en', 'like', "%{$filters['search']}%");
            });
        })->when(isset($filters['status']), function ($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function create($data)
    {
        return $this->model->create($data);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function restore($id)
    {
        return $this->model->onlyTrashed()->find($id)->restore();
    }

    public function forceDelete($id)
    {
        return $this->model->onlyTrashed()->find($id)->forceDelete();
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function count(array $fillters = [])
    {
        return $this->model->when(isset($fillters['start_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '>=', $fillters['start_date']);
        })->when(isset($fillters['end_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '<=', $fillters['end_date']);
        })->count();
    }

    public function all()
    {
        return $this->model->all();
    }

    public function getWhere(array $data)
    {
        return $this->model->where($data)->get();
    }
}
