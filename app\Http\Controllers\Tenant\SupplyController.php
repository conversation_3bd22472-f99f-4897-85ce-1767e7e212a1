<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateSupplyRequest;
use App\Http\Requests\Tenant\Admin\UpdateSupplyRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\SupplyDetailsResource;
use App\Http\Resources\Tenant\SupplyResource;
use App\Services\Tenant\SupplyService;

class SupplyController extends Controller
{
    public function __construct(protected SupplyService $supplyService) {}

    public function index()
    {
        $supplies = $this->supplyService->get();

        return success(new BaseCollection($supplies, SupplyResource::class));
    }

    public function store(CreateSupplyRequest $request)
    {
        return $this->supplyService->create($request->validated());
    }

    public function show(string $id)
    {
        $supply = $this->supplyService->getById($id);

        return success(new SupplyDetailsResource($supply));
    }

    public function update(UpdateSupplyRequest $request, string $id)
    {
        return $this->supplyService->update($id, $request->all());
    }

    public function destroy(string $id)
    {
        return $this->supplyService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->supplyService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->supplyService->forceDelete($id);
    }
}
