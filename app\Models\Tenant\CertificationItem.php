<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CertificationItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_certificate_id',
        'item_id',
        'unit_id',
        'qty',
        'price',
        'total',
        'item_name'
    ];

    /**
     * Get the payment certificate that owns the certification item.
     */
    public function paymentCertificate()
    {
        return $this->belongsTo(PaymentCertificate::class);
    }

    /**
     * Get the item associated with the certification item.
     */
    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    /**
     * Get the unit associated with the certification item.
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
