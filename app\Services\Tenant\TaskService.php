<?php

namespace App\Services\Tenant;

use Exception;
use App\DTO\Tenant\TaskData;
use App\DTO\Tenant\TaskStatusData;
use Illuminate\Support\Facades\DB;

use App\Repositories\Tenant\TaskRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TaskService
{
    protected $taskRepository;
    protected $taskHistoryService;

    public function __construct(
        TaskRepository $taskRepository,
        TaskHistoryService $taskHistoryService
    ) {
        $this->taskRepository = $taskRepository;
        $this->taskHistoryService = $taskHistoryService;
    }

    public function index()
    {
        $filters = request()->only([
            'search',
            'project',
            'user',
            'date_from',
            'date_to'
        ]);

        return $this->taskRepository->index($filters);
    }

    public function getArchived()
    {
        return  $this->taskRepository->getTrashedWithPaginate();
    }

    public function show($id)
    {
        return $this->taskRepository->findById($id);
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $data = TaskData::from($request)->all();

            $task = $this->taskRepository->create($data);

            // Log task creation
            $this->taskHistoryService->logAction(
                $task->id,
                'created',
                'Task created with status: ' . $task->status
            );

            if (!empty($request['images'])) {
                foreach (request()->file('images') as $image) {
                    $task->addMedia($image)
                        ->toMediaCollection('tasks');
                }
            }

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        try {
            $task = $this->taskRepository->findById($id);
            $oldStatus = $task->status;

            // Store original values before update
            $originalValues = $task->getAttributes();

            $data = TaskData::from($request)->all();

            $this->taskRepository->update($task, $data);

            // Get updated values after update
            $updatedValues = $task->getAttributes();

            // Track which fields have changed (excluding status which is handled separately)
            $changedFields = [];
            foreach (array_keys($data) as $field) {
                if ($field !== 'status' && isset($originalValues[$field]) && $originalValues[$field] !== $updatedValues[$field]) {
                    $changedFields[] = $field;
                }
            }

            // Log status change if status was updated
            if (isset($request['status']) && $oldStatus !== $request['status']) {
                $this->taskHistoryService->logAction(
                    $task->id,
                    'status_changed',
                    "Status changed from {$oldStatus} to {$request['status']}"
                );
            }

            // Log other field changes if any fields were updated
            if (!empty($changedFields)) {
                $this->taskHistoryService->logAction(
                    $task->id,
                    'fileds_updated',
                    "Fields updated"
                );
            }

            if (!empty($request['images'])) {
                $task->clearMediaCollection('tasks');
                foreach (request()->file('images') as $image) {
                    $task->addMedia($image)
                        ->toMediaCollection('tasks');
                }
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function delete($id)
    {
        try {
            $task = $this->taskRepository->findById($id);

            $this->taskRepository->delete($task);

            // Log deletion
            $this->taskHistoryService->logAction(
                $task->id,
                'deleted',
                'Task was deleted'
            );

            return success(__('Deleted Successfully'));
        } catch (NotFoundHttpException $e) {
            return error($e->getMessage(), 404);
        } catch (Exception $e) {
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function restore($id)
    {
        try {
            $task = $this->taskRepository->findTrashedById($id);

            if (!$task || $task->permanent_deleted) {
                throw new NotFoundHttpException(__('Task not found'));
            }

            $this->taskRepository->restore($task);

            return success(__('Restored Successfully'));
        } catch (NotFoundHttpException $e) {
            return error($e->getMessage(), 404);
        } catch (Exception $e) {
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function forceDelete($id)
    {
        try {
            $task = $this->taskRepository->findTrashedById($id);

            if (!$task || $task->permanent_deleted) {
                throw new NotFoundHttpException(__('Task not found'));
            }

            $this->taskRepository->forceDelete($task);

            return success(__('Deleted Successfully'));
        } catch (NotFoundHttpException $e) {
            return error($e->getMessage(), 404);
        } catch (Exception $e) {
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }

    public function getUserTasks()
    {
        $filters = request()->only([
            'project_id',
            'date'
        ]);

        return $this->taskRepository->getUserTasks($filters);
    }

    public function updateStatus(string $id, array $validated)
    {
        DB::beginTransaction();

        try {
            $task = $this->taskRepository->findById($id);

            if (!$task) {
                throw new Exception(__('Task not found'));
            }

            $dto = TaskStatusData::fromRequest($validated);
            $oldStatus = $task->status;

            $this->taskRepository->updateStatus($task, $dto->status);

            // Log status change
            $this->taskHistoryService->logAction(
                $task->id,
                'status_changed',
                "Status changed from {$oldStatus} to {$dto->status}"
            );

            DB::commit();

            return success(__('Status Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }
}
