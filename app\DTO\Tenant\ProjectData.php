<?php

namespace App\DTO\Tenant;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class ProjectData extends Data
{
    public function __construct(
        public string $name,
        public ?string $description,
        public string $status,
        public string $type,
        public string $size,
        public int $area,
        public int $payment_method_id,
        public int|Optional $package_id,
        public int|Optional $office_ratio,
        public int|Optional $site_engineer_ratio,
        public float|Optional $amount_per_meter,
        public float|Optional $total,
        public float $withdraw_limit,
        public string $start_date,
        public ?string $end_date,
    ) {}
}
