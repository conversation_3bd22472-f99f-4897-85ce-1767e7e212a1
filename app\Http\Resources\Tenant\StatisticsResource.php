<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'financial_section' => [
                'total_project_balance' => $this['total_project_balance'],
                'total_purchases' => $this['total_purchases'],
                'total_expenses' => $this['total_expenses'],
                'total_debts' => $this['total_debts'],
                'total_profits' => $this['total_profits'],
            ],
            'users_section' => [
                'clients_count' => $this['clients_count'],
                'engineers_count' => $this['engineers_count'],
                'accountants_count' => $this['accountants_count'],
                'project_managers_count' => $this['project_managers_count'],
                'admins_count' => $this['admins_count'],
            ],
            'projects_section' => $this->when(request()->routeIs('statistics.index'), [
                [
                    'new_projects_count' => $this['new_projects_count'] ?? null,
                    'in_progress_projects_count' => $this['in_progress_projects_count'] ?? null,
                    'completed_projects_count' => $this['completed_projects_count'] ?? null,
                    'deferred_projects_count' => $this['deferred_projects_count'] ?? null,
                ],
            ]),

            'charts_section' => [
                'project_payments' => $this['project_payments_chart']->map(function ($item) {
                    return [
                        'project_name' => $item->project_name, // or $item->project->name if you want to include the project details
                        'total' => $item->total_amount,
                    ];
                }),
             
            ],
            'latest_histories' => $this->when(request()->routeIs('statistics.project'), $this['latest_histories'] ?? null),
        ];
    }
}
