<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:30'],
            'email' => [
                'sometimes',
                'email',
                Rule::unique('clients', 'email')->ignore($this->client),
            ],
            'phone' => ['sometimes', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('clients', ignoreId: $this->client)],
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'address' => ['nullable', 'string', 'max:100'],
            'status' => ['sometimes', Rule::in(['Active', 'Disabled'])],
            'referred_by' => [
                'nullable',
                Rule::in(['Social Media', 'Friend Recommendation', 'Street Ads', 'Another Customer']),
            ],
            'details' => ['nullable', 'string', 'max:255'],
            'project_ids' => ['nullable', 'array'], // Allow updates for project_ids
            'project_ids.*' => ['integer', 'exists:projects,id'], // Validate each project_id exists
        ];
    }
}
