<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProjectPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => 'sometimes|date',
            'title' => 'sometimes|min:3|max:30',
            'client_id' => 'sometimes|exists:clients,id',
            'amount' => ['sometimes', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'numeric', 'min:1'],
            'notes' => 'sometimes|min:3|max:100',
        ];
    }
}
