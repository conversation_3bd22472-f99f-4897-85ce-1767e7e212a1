<?php

namespace App\Services\Tenant;

class TransactionMessageService
{
    /**
     * Generate translatable transaction messages for Spatie Translatable
     */
    public static function generateMessages(string $type, array $params): array
    {
        return [
            'en' => self::getEnglishMessage($type, $params),
            'ar' => self::getArabicMessage($type, $params),
        ];
    }

    private static function getEnglishMessage(string $type, array $params): string
    {
        return match ($type) {
            'balance_added' => "{$params['user']} added {$params['amount']} to {$params['target']}",
            'balance_updated' => "{$params['user']} updated {$params['amount']} to {$params['target']}",
            'balance_transferred' => "Balance transferred from {$params['from']} to {$params['to']}",
            'balance_withdraw' => "{$params['user']} withdrew {$params['amount']} from {$params['target']}",
            'balance_deposit' => "{$params['user']} deposited {$params['amount']} to {$params['target']}",
            'payment_created' => "{$params['user']} created new payment {$params['reference']}",
            'payment_updated' => "{$params['user']} updated payment {$params['reference']}",
            'refund_created' => "{$params['user']} created new refund {$params['reference']}",
            'refund_updated' => "{$params['user']} updated refund {$params['reference']}",
            'refund_paid' => "{$params['user']} paid {$params['amount']} to {$params['target']}",
            'purchase_created' => "{$params['user']} created new purchase {$params['reference']}",
            'purchase_updated' => "{$params['user']} updated purchase {$params['reference']}",
            'contractor_payment_added' => "{$params['user']} added {$params['amount']} to {$params['contractor']}",
            'contractor_payment_updated' => "{$params['user']} updated {$params['old_amount']} to {$params['new_amount']}",
            'certificate_updated' => "{$params['user']} updated payment certificate {$params['title']}",
            'project_payment_added' => "Balance Added to {$params['user']}",
            default => "{$params['user']} performed {$type} action",
        };
    }

    private static function getArabicMessage(string $type, array $params): string
    {
        return match ($type) {
            'balance_added' => "{$params['user']} أضاف {$params['amount']} إلى {$params['target']}",
            'balance_updated' => "{$params['user']} حدث {$params['amount']} إلى {$params['target']}",
            'balance_transferred' => "تم تحويل الرصيد من {$params['from']} إلى {$params['to']}",
            'balance_withdraw' => "{$params['user']} سحب {$params['amount']} من {$params['target']}",
            'balance_deposit' => "{$params['user']} أودع {$params['amount']} إلى {$params['target']}",
            'payment_created' => "{$params['user']} أنشأ دفعة جديدة {$params['reference']}",
            'payment_updated' => "{$params['user']} حدث الدفعة {$params['reference']}",
            'refund_created' => "{$params['user']} أنشأ مرتجع جديد {$params['reference']}",
            'refund_updated' => "{$params['user']} حدث المرتجع {$params['reference']}",
            'refund_paid' => "{$params['user']} دفع {$params['amount']} إلى {$params['target']}",
            'purchase_created' => "{$params['user']} أنشأ مشترى جديد {$params['reference']}",
            'purchase_updated' => "{$params['user']} حدث المشترى {$params['reference']}",
            'contractor_payment_added' => "{$params['user']} أضاف {$params['amount']} إلى {$params['contractor']}",
            'contractor_payment_updated' => "{$params['user']} حدث {$params['old_amount']} إلى {$params['new_amount']}",
            'certificate_updated' => "{$params['user']} حدث شهادة الدفع {$params['title']}",
            'project_payment_added' => "تم إضافة رصيد إلى {$params['user']}",
            default => "{$params['user']} قام بإجراء {$type}",
        };
    }

    /**
     * Helper method for balance operations
     */
    public static function balanceOperation(string $operation, string $userName, float $amount, string $targetName): array
    {
        return self::generateMessages("balance_{$operation}", [
            'user' => $userName,
            'amount' => $amount,
            'target' => $targetName,
        ]);
    }

    /**
     * Helper method for transfer operations
     */
    public static function transferOperation(string $fromUser, string $toUser, float $amount): array
    {
        return self::generateMessages('balance_transferred', [
            'from' => $fromUser,
            'to' => $toUser,
            'amount' => $amount,
        ]);
    }

    /**
     * Helper method for payment operations
     */
    public static function paymentOperation(string $operation, string $userName, string $reference, ?float $amount = null): array
    {
        $params = [
            'user' => $userName,
            'reference' => $reference,
        ];

        if ($amount !== null) {
            $params['amount'] = $amount;
        }

        return self::generateMessages("payment_{$operation}", $params);
    }

    /**
     * Helper method for refund operations
     */
    public static function refundOperation(string $operation, string $userName, string $reference, array $extraParams = []): array
    {
        $params = array_merge([
            'user' => $userName,
            'reference' => $reference,
        ], $extraParams);

        return self::generateMessages("refund_{$operation}", $params);
    }

    /**
     * Helper method for contractor payment operations
     */
    public static function contractorPaymentOperation(string $operation, string $userName, array $params): array
    {
        $params['user'] = $userName;
        return self::generateMessages("contractor_payment_{$operation}", $params);
    }
}
