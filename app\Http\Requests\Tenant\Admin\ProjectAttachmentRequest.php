<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\ValidAttachmentType;
use Illuminate\Foundation\Http\FormRequest;

class ProjectAttachmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $type = $this->input('type');

        $attachmentsRules = [
            'file',
            'max:5120',
            new ValidAttachmentType($type),
        ];

        if ($this->isMethod('post')) {
            $attachmentsRules[] = 'required';
        }

        return [
            'name' => 'required|max:30',
            'description' => 'nullable|max:255',
            'attachments' => [$this->isMethod('post') ? 'required' : 'nullable', 'array', 'min:1', 'max:10'],
            'attachments.*' => $attachmentsRules,
            'item_id' => 'required|exists:items,id',
            'type' => 'required|in:design,implementation,panel',
            'project_id' => 'required|exists:projects,id',
        ];
    }
}
