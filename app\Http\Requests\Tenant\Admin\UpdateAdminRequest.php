<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\IgnoreSuperAdminRole;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Contracts\Validation\ValidationRule;

class UpdateAdminRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = request()->route('admin');

        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name' => ['sometimes', 'string', 'max:30'],
            'email' => 'required|email|unique:admins,email,' . $id,
            'phone' => ['sometimes', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('admins', ignoreId: $id)],
            'address' => 'sometimes|string|min:3|max:100',
            'status' => 'sometimes|in:Active,Disabled',
            'role_id' => ['required', 'exists:roles,id', new IgnoreSuperAdminRole],
            'permissions' => 'sometimes|array',
            'permissions.*' => 'exists:permissions,id',
            'password' => [
                'sometimes',
                'string',
                Password::min(8)->mixedCase()->numbers()->symbols(),
                'confirmed',
            ],
        ];
    }
}
