<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreatePurchaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'project_id' => ['required', 'exists:projects,id'],
            'type' => ['required', 'in:detailed,printed'],
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'date' => ['required', 'date'],
            'partner_id' => [
                'required',
                Rule::exists('partners', 'id')
                    ->where('type', 'supplier')
                    ->where('permanent_deleted', false)
                    ->whereNull('deleted_at')
            ],
            'categories' => ['required_if:type,detailed', 'array'],
            'categories.*.id' => ['required_if:type,detailed', 'exists:items,id'],
            'categories.*.items' => ['required_if:type,detailed', 'array', 'min:1'],
            'categories.*.items.*.name' => ['required_if:type,detailed', 'string', 'min:3', 'max:30'],
            'categories.*.items.*.unit_id' => ['required_if:type,detailed', 'exists:units,id'],
            'categories.*.items.*.price' => ['required_if:type,detailed', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'min:1'],
            'categories.*.items.*.qty' => ['required_if:type,detailed', 'integer', 'min:1', 'min_digits:1', 'max_digits:10'],
            'item_id' => ['required_if:type,printed', 'exists:items,id'],
            'notes' => ['sometimes', 'string', 'max:100'],
            'images' => ['required_if:type,printed', 'array', 'min:1', 'max:5'],
            'images.*' => ['required_if:type,printed', 'mimes:png,jpg,jpeg', 'max:5120'],
            'total_before_discount' => ['required_if:type,printed', 'numeric', 'min:1', 'regex:/^\d{1,10}(\.\d{1,2})?$/'],
            'discount_type' => ['nullable', 'in:fixed,percentage'],
            'discount_amount' => [
                'required_with:discount_type',
                'numeric',
                'min:1',
                Rule::when($this->discount_type === 'percentage', 'max:100'),
                Rule::when($this->discount_type !== 'percentage', [
                    'regex:/^\d{1,10}(\.\d{1,2})?$/',
                ]),
            ],
            'status' => ['required', 'in:unpaid,partial,paid'],
            'paid_amount' => ['required_if:status,partial', 'numeric', Rule::when($this->discount_type === 'percentage' && $this->discount_amount == 100, 'min:0', ['min:1', 'regex:/^\d{1,10}(\.\d{1,2})?$/'])],
        ];
    }

    public function messages()
    {
        return [
            'discount_amount.required_with' => __('validation.required'),
            'paid_amount.required_if' => __('validation.required'),
            'categories.*.id.required_if' => __('validation.required'),
            'categories.*.items.required_if' => __('validation.required'),
            'categories.*.items.*.price.required_if' => __('validation.required'),
            'categories.*.items.*.qty.required_if' => __('validation.required'),
            'categories.*.items.*.name.required_if' => __('validation.required'),
            'categories.*.items.*.unit_id.required_if' => __('validation.required'),
            'item_id.required_if' => __('validation.required'),
            'total_before_discount.required_if' => __('validation.required'),
            'discount_amount.required_with' => __('validation.required'),

        ];
    }
}
