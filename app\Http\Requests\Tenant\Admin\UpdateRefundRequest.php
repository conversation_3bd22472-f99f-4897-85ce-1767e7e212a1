<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Repositories\Tenant\RefundRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UpdateRefundRequest extends FormRequest
{
    public function __construct(protected RefundRepository $refundRepo) {}

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Set to true if any user can access this route
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $refund = $this->refundRepo->findById($this->route('refunde'));

        if (! $refund) {
            throw new NotFoundHttpException;
        }

        return [
            'purchase_id' => ['required_if:type,invoice', Rule::exists('purchases', 'id')->where('type', 'detailed')],
            'name' => 'required|string|min:3|max:30',
            'date' => 'required|date',
            'partner_id' => [
                'required_if:type,detailed',
                Rule::exists('partners', 'id')
                    ->where('type', 'supplier')
                    ->where('permanent_deleted', false)
                    ->whereNull('deleted_at')
            ],
            'discount_type' => 'nullable|in:value,percentage',
            'discount_value' => [
                'required_with:discount_type',
                'numeric',
                'min:1',
                Rule::when($this->discount_type === 'percentage', 'max:100'),
                Rule::when($this->discount_type !== 'percentage', [
                    'regex:/^\d{1,10}(\.\d{1,2})?$/',
                ]),
            ],
            'status' => 'sometimes|in:paid,partial,unpaid',
            'paid_amount' => 'required_if:status,partial|numeric|min:1|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'notes' => 'nullable|string',
            'created_by' => 'nullable|exists:users,id',
            'item_ids' => Rule::when($refund->type === 'invoice', ['required', 'array']),
            'item_ids.*' => 'exists:purchase_items,id',
            'qty_refunded' => Rule::when($refund->type === 'invoice', ['required', 'array']),
            'qty_refunded.*' => 'numeric|min:1|min_digits:1|max_digits:10',
            'categories' => Rule::when($refund->type === 'detailed', ['required', 'array']),
            'categories.*.id' => Rule::when($refund->type === 'detailed', ['required', 'exists:items,id']),
            'categories.*.items' => Rule::when($refund->type === 'detailed', ['required', 'array', 'min:1']),
            'categories.*.items.*.name' => Rule::when($refund->type === 'detailed', ['required', 'string', 'min:3', 'max:30']),
            'categories.*.items.*.unit_id' => Rule::when($refund->type === 'detailed', ['required', 'exists:units,id']),
            'categories.*.items.*.price' => Rule::when($refund->type === 'detailed', ['required', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'min:1']),
            'categories.*.items.*.qty_refunded' => Rule::when($refund->type === 'detailed', ['required', 'integer', 'min:1', 'min_digits:1', 'max_digits:10']),
            'images' => ['nullable', 'array', 'min:1', 'max:10'],
            'images.*' => ['nullable', 'mimes:png,jpg,jpeg', 'max:5120'],
        ];
    }
}
