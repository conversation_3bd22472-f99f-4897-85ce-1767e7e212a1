<?php

namespace App\DTO\Tenant;

use PhpParser\Node\Expr\Cast\Double;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class OfficeDueData extends Data
{
    public function __construct(
        public int $project_id,
        public float $project_expenses,
        public float|optional $office_ratio,
        public float|optional $total_profits,
        public float|optional $due_profits,
        public float|optional $office_total,


    ) {}
}
