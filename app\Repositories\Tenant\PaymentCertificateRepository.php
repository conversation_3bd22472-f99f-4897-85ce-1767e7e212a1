<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\PaymentCertificate;
use Carbon\Carbon;

class PaymentCertificateRepository
{
    public function __construct(protected PaymentCertificate $model)
    {
        //
    }

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model
            ->where('project_id', request()->project)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->where(function ($q) use ($filters) {
                    $q->where('title', 'like', "%{$filters['search']}%")
                        ->orWhere('created_by', 'like', "%{$filters['search']}%");
                });
            })
            ->when(isset($filters['contractor']), function ($query) use ($filters) {
                $query->whereHas('contractor', function ($q) use ($filters) {
                    $q->where('name', 'like', "%{$filters['contractor']}%");
                });
            })
            ->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $from = isset($filters['date_from']) ? Carbon::parse($filters['date_from'])->startOfDay() : null;
                $to = isset($filters['date_to']) ? Carbon::parse($filters['date_to'])->endOfDay() : null;

                if ($from && $to) {
                    $query->whereBetween('date', [$from, $to]);
                } elseif ($from) {
                    $query->where('date', '>=', $from);
                } elseif ($to) {
                    $query->where('date', '<=', $to);
                }
            })
            ->orderByDesc('id')
            ->paginate(request()->per_page ?: $limit);
    }

    public function update($certificate, array $data)
    {

        $certificate->update($data);
    }

    public function all()
    {
        return $this->model->all();
    }

    public function findById($id)
    {
        return $this->model->with('media')->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->where('permanent_deleted', false)->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function getContractorCertificatrions($contractor_id, $project_id)
    {
        $data = $this->model->where(['contractor_id' => $contractor_id, 'project_id' => $project_id])->get();

        return $data;
    }

    public function totalCertificates($project_id)
    {
        return $this->model->where('project_id', $project_id)->sum('total');
    }
}
