<?php

namespace App\Http\Requests\Admin\Auth;

use App\Rules\PasswordNotContainPersonalInfo;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'new_password' => ['required', 'string', Password::min(8)->max(20)->mixedCase()->numbers()->symbols()->uncompromised(), 'confirmed', new PasswordNotContainPersonalInfo([$this->email])],
            'token' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'new_password.max' => __('validation.max.numeric'),
        ];
    }
}
