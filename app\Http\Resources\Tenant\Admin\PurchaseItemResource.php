<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'unit' => [
                'id' => $this->unit->id,
                'name' => $this->unit->name,
            ],
            'price' => $this->price,
            'qty' => $this->qty,
            'total' => $this->total,
        ];
    }
}
