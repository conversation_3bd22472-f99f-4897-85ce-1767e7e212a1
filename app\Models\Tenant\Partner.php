<?php

namespace App\Models\Tenant;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Partner extends Model implements HasMedia
{
    use HasFactory, SoftDeletes , InteractsWithMedia;

    protected $fillable = [
        'name',
        'mobile',
        'email',
        'address',
        'details',
        'national_id',
        'status',
        'balance',
        'deleted_at',
        'image',
        'type',
        'permanent_deleted',
    ];

    public function getImageAttribute()
    {
        return $this->getMedia('partners')->first();
    }

    /**
     * Relationship: A Partner can have many items.
     */
    public function items()
    {
        return $this->belongsToMany(Item::class, 'partner_items', 'partner_id', 'item_id')
            ->withTimestamps();
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function balance()
    {
        return $this->morphOne(UserBalance::class, 'userable');
    }

    public function asCreditor()
    {
        return $this->morphMany(Debt::class, 'creditor');
    }

    public function asDebtor()
    {
        return $this->morphMany(Debt::class, 'debtor');
    }

    public function PaymentCertificate()
    {
        return $this->hasMany(PaymentCertificate::class, 'contractor_id');
    }

    
   
}
