<?php

namespace App\Services\Tenant;

use App\DTO\SubscriptionData;
use App\Repositories\PlanRepository;

class SubscriptionService
{
    public function __construct(protected PlanRepository $planRepo)
    {
        //
    }

    public function getSubscription()
    {
        return tenant()->subscription;
    }

    public function update(array $request)
    {
        $subscription = $this->getSubscription();

        if ($request['total'] < 0) {
            return error('Total cannot be less than the remaining amount.');
        }

        if ($request['type'] === 'renew') {
            if ((int) now()->diffInMonths($subscription->end_date) > 0) {
                return error('Sorry, you cannot renew at the moment. Please wait until the last month before the end date.', 422);
            }

            $subscriptionData = [
                'duration' => $request['duration'],
                'plan_price' => $request['plan_price'],
                'total' => $request['total'],
                'status' => 'Active',
                'start_date' => now()->gt($subscription->end_date)
                    ? now()->format('Y-m-d')
                    : $subscription->end_date->addDay()->format('Y-m-d'),
                'end_date' => now()->gt($subscription->end_date)
                    ? now()->addMonths((int) $request['duration'])->format('Y-m-d')
                    : $subscription->end_date->addMonths((int) $request['duration'])->format('Y-m-d'),
            ];
        } else {
            $plan = $this->planRepo->findByIdOnCenteral($request['plan_id']);

            $subscriptionData = [
                'plan_id' => $request['plan_id'],
                'duration' => $request['duration'],
                'plan_price' => $request['plan_price'],
                'total' => $request['total'],
                'status' => 'Active',
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonths((int) $request['duration'])->format('Y-m-d'),
                'number_of_projects' => $plan->number_of_projects,
                'number_of_site_engineers' => $plan->number_of_site_engineers,
                'number_of_office_engineers' => $plan->number_of_office_engineers,
                'number_of_accountants' => $plan->number_of_accountants,
                'number_of_project_managers' => $plan->number_of_project_managers,
                'storage' => $plan->storage,
                'storage_type' => $plan->storage_type,
                'has_domain' => $plan->has_domain,
                'has_free_website' => $plan->has_free_website,
                'has_chat_availability' => $plan->has_chat_availability,
                'is_unlimited_projects' => $plan->is_unlimited_projects,
                'is_unlimited_site_engineers' => $plan->is_unlimited_site_engineers,
                'is_unlimited_office_engineers' => $plan->is_unlimited_office_engineers,
                'is_unlimited_accountants' => $plan->is_unlimited_accountants,
                'is_unlimited_project_managers' => $plan->is_unlimited_project_managers,
                'is_unlimited_admins' => $plan->is_unlimited_admins,
            ];
        }

        $data = SubscriptionData::from($subscriptionData)->all();

        $subscription->update($data);

        return success(__('Updated Successfully'));
    }
}
