<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class ProjectAttachment extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = ['project_id', 'type', 'description', 'name', 'item_id', 'permanent_deleted', 'creator'];



    public function getImageAttribute()
    {
        return $this->getFirstMedia('project-attachments');
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function getFirstHistoryAttribute()
    {
        return $this->histories()->first();
    }

    public function item()
    {
        return $this->belongsTo(Item::class, 'item_id', 'id');
    }
}
