<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ProjectPaymentData;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\TransactionRepository;
use App\Services\Tenant\TransactionMessageService;
use App\Repositories\Tenant\ProjectPaymentRepository;
use App\Repositories\Tenant\UserRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\Tenant\Traits\HasSufficientBalanceCheck;

class ProjectPaymentService
{
    use HasSufficientBalanceCheck;
    public function __construct(
        protected ProjectPaymentRepository $ProjectPaymentRepository,
        protected UserRepository $UserRepository,
        protected ProjectRepository $ProjectRepository,
        protected TransactionRepository $transactionRepository,
        protected OfficeDueService $officeDueService
    ) {}

    public function get()
    {

        $filterable = request()->only([
            'search',
            'date_from',
            'date_to',
        ]);
        if (request()->type === 'archive') {
            return $this->ProjectPaymentRepository->getTrashedWithPaginate();
        } else {
            return $this->ProjectPaymentRepository->getPaginated($filterable);
        }
    }

    public function getById(string $id)
    {
        $payment = $this->ProjectPaymentRepository->findById($id);

        if (! $payment) {
            throw new NotFoundHttpException;
        }

        return $payment;
    }

    public function store(array $request)
    {
        DB::beginTransaction();

        try {
            $user = auth()->user();

            $data = ProjectPaymentData::from($request)->all();

            $data['created_by'] = $user->name;
            $data['number'] = generateInvoiceNumber();

            // Check sufficient balance for project payment
            $this->assertSufficientBalance(
                'project_payment',
                $request['amount'],
                null,
                ['user_id' => $user->id, 'project_id' => $data['project_id']],
                null
            );
            $payment = $this->ProjectPaymentRepository->create($data);
            $this->UserRepository->updateBalance($user, $request['amount'], '+');
            $this->ProjectRepository->updateBalance($data['project_id'], $request['amount'], '+');

            $message = TransactionMessageService::generateMessages('project_payment_added', ['user' => $user->name]);
            $this->transactionRepository->create($payment, $user, $request['amount'], 1, $message, 'add');
            $this->officeDueService->createOrUpdate($data['project_id']);

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        $payment = $this->ProjectPaymentRepository->findById($id);

        if (! $payment) {
            throw new NotFoundHttpException;
        }

        try {

            $data = ProjectPaymentData::from($request)->all();

            $data = ProjectPaymentData::from($request)->all();
            $oldAmount = $payment->amount; // Store original amount
            $newAmount = $request['amount'];

            // Check sufficient balance for project payment update
            $this->assertSufficientBalance(
                'project_payment',
                $newAmount,
                $oldAmount,
                ['user_id' => $payment->Creator?->userable?->id, 'project_id' => $payment->project_id],
                null
            );

            // Update the payment
            $this->ProjectPaymentRepository->update($payment, $data);

            // Update the transaction
            $message = TransactionMessageService::balanceOperation('updated', $payment->Creator?->userable->name, $newAmount, $payment->Creator?->userable->name);
            $this->transactionRepository->create($payment, $payment->Creator?->userable, $newAmount, 0, $message, 'update');
            $this->officeDueService->createOrUpdate($payment->project_id);

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            error($e->getMessage());
        }
    }

    public function handleBalanceUpdate($userable, $project_id, $oldAmount, $newAmount)
    {
        $difference = $newAmount - $oldAmount;
        if (! $userable) {
            return error('User not found', 404);
        }

        if ($difference === 0) {
            return; // No change needed
        }

        if ($difference > 0) {
            // Increase balance for user and project
            $this->UserRepository->updateBalance($userable, $difference, '+');
            $this->ProjectRepository->updateBalance($project_id, $difference, '+');
        } else {

            // Decrease balance for user and project
            $this->UserRepository->updateBalance($userable, abs($difference), '-');
            $this->ProjectRepository->updateBalance($project_id, abs($difference), '-');
        }
    }
    public function delete(string $id)
    {
        $record = $this->getById($id);

        $this->UserRepository->updateBalance($record->Creator?->userable, $record->amount, '-');
        $this->ProjectRepository->updateBalance($record->project_id, $record->amount, '-');
        $message = TransactionMessageService::balanceOperation('updated', $record->Creator?->userable->name, $record->amount, $record->Creator?->userable->name);
        $this->transactionRepository->create($record, $record->Creator?->userable, $record->amount, 0, $message, 'update');

        $record->delete();

        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $record = $this->ProjectPaymentRepository->findTrashedById($id);

        if (! $record || $record->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $this->UserRepository->updateBalance($record->Creator?->userable, $record->amount, '+');
        $this->ProjectRepository->updateBalance($record->project_id, $record->amount, '+');
        $message = TransactionMessageService::balanceOperation('added', $record->Creator?->userable->name, $record->amount, $record->Creator?->userable->name);
        $this->transactionRepository->create($record, $record->Creator?->userable, $record->amount, 0, $message, 'add');

        $record->restore();

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $record = $this->ProjectPaymentRepository->findTrashedById($id);

        if (! $record || $record->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        $record->update([
            'permanent_deleted' => true,
            'deleted_at' => now(),
        ]);

        return success(__('Deleted Successfully'));
    }

    public function getTotalPayment()
    {
        return $this->ProjectPaymentRepository->totalpayments(request()->project);
    }
}
