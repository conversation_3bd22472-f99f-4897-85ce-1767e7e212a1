<?php

namespace App\DTO;

use <PERSON><PERSON>\LaravelData\Data;
use Spatie\LaravelData\Optional;

class OfficeData extends Data
{
    public function __construct(
        public string $name_ar,
        public string $name_en,
        public string $email,
        public string $phone,
        public string|Optional $password,
        public string|Optional $address,
        public array|Optional $location,
        public string|Optional $work_hour_from,
        public string|Optional $work_hour_to,
        public string|Optional $work_day_from,
        public string|Optional $work_day_to,
        public string|null $description,
        public string $status,
        public string|Optional $apperance_color
    ) {}
}
