<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectAttachmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'description' => $this->description,
            'createor' => $this->creator,
            'item' => [
                'id' => $this->item->id,
                'name' => $this->item->name,

            ],
            'created_at' => $this->created_at->toDateTimeString(),
            'attachments' => MediaResource::collection($this->media),
        ];
    }
}
