<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\MediaResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfficeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'image' => new MediaResource($this->image),
            'name' => $this->name,
            'name_ar' => $this->name_ar,
            'name_en' => $this->name_en,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'location' => $this->location,
            'work_hour_from' => $this->work_hour_from ? Carbon::parse($this->work_hour_from)->format('H:i') : null,
            'work_hour_to' => $this->work_hour_from ? Carbon::parse($this->work_hour_to)->format('H:i') : null,
            'work_day_from' => $this->work_day_from,
            'work_day_to' => $this->work_day_to,
            'description' => $this->description,
            'status' => $this->status,
            'apperance_color' => $this->apperance_color,
            'subscription' => new SubscriptionResource($this->subscription),
            'domains' => DomainResource::collection($this->domains),
        ];
    }
}
