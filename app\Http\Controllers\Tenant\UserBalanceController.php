<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\UserBalanceService;
use App\Http\Requests\Tenant\Admin\ChangeBalanceRequest;
use App\Http\Resources\Tenant\Admin\UserBalanceResource;

class UserBalanceController extends Controller
{
    public function __construct(protected UserBalanceService $UserBalanceService)
    {
        //
    }

    public function index()
    {
        $balances = $this->UserBalanceService->index();
        return success(new BaseCollection($balances, UserBalanceResource::class));
    }

    public function changeBalance(ChangeBalanceRequest $request , $id)
    {
        return $this->UserBalanceService->changeBalance($request->validated() , $id);
    }
}