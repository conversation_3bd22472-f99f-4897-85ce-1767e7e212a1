<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class InvoiceCategory extends Model
{
    protected $fillable = ['item_id'];

    public function invoiceable()
    {
        return $this->morphTo();
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function items()
    {
        return $this->hasMany(PurchaseItem::class);
    }

    public function Refunditems()
    {
        return $this->hasMany(RefundItem::class);
    }

    public function supplyItems()
    {
        return $this->hasMany(SupplyItem::class);
    }
}
