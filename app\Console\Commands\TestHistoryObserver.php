<?php

namespace App\Console\Commands;

use App\Models\Admin;
use App\Models\History;
use App\Models\Plan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

class TestHistoryObserver extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:history-observer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the HistoryObserver with correct translations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing HistoryObserver with translations...');

        // Create a test admin user for authentication
        $admin = Admin::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => 'password',
            'phone' => '123456789',
            'address' => 'Test Address',
            'status' => 'active',
            'role_id' => null,
        ]);

        // Set the authenticated user
        Auth::setUser($admin);

        $this->info('Created test admin: ' . $admin->name);

        // Create a plan which should trigger the HistoryObserver
        $plan = Plan::create([
            'name_ar' => 'خطة تجريبية',
            'name_en' => 'Test Plan',
            'price' => 100,
            'number_of_projects' => 5,
            'number_of_site_engineers' => 2,
            'number_of_office_engineers' => 2,
            'number_of_accountants' => 1,
            'number_of_project_managers' => 1,
            'number_of_admins' => 1,
            'storage' => 1000,
            'storage_type' => 'MB',
            'status' => 'active',
        ]);

        $this->info('Created test plan: ' . $plan->name);

        // Get the latest history record
        $history = History::latest()->first();

        if ($history) {
            $this->info('History record created successfully!');
            $this->info('Causer: ' . $history->causer_name);
            $this->info('Model: ' . $history->model);
            $this->info('English Message: ' . $history->message_en);
            $this->info('Arabic Message: ' . $history->message_ar);
        } else {
            $this->error('No history record was created!');
        }

        // Clean up
        $plan->delete();
        $admin->delete();

        $this->info('Test completed and cleaned up.');
    }
}
