<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Transaction;
use App\Models\Tenant\User;
use Illuminate\Support\Facades\Schema;

class TransactionRepository
{
    public function __construct(public Transaction $model) {}

    public function getPaginated(string $projectId, array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with(['userable', 'transactionable'])
            ->where(function ($query) use ($projectId) {
                $query->whereHasMorph(
                    'transactionable',
                    '*', // Apply to all related models dynamically
                    function ($q) use ($projectId) {
                        if (Schema::hasColumn($q->getModel()->getTable(), 'project_id')) {
                            $q->where('project_id', $projectId)->withTrashed();
                        }
                    }
                )->where('transactionable_type', '!=', User::class); // Exclude User transactions
            })
            ->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $dates = [
                    $filters['date_from'],
                    $filters['date_to'] ?? null,
                ];

                return $query->whereBetween('created_at', $dates);
            })->orderByDesc('id')
            ->paginate($limit);
    }

    public function create($transactionable, $userable, float $amount, bool $is_creator = true, $message = null, $type = null, array $messageData = []): Transaction
    {
        $userable->balance()->firstOrCreate();

        $data = [
            'transactionable_id' => $transactionable->id,
            'transactionable_type' => get_class($transactionable),
            'userable_id' => $userable->id,
            'userable_type' => get_class($userable),
            'amount' => $amount,
            'is_creator' => $is_creator,
            'type' => $type,
        ];

        // Handle message data - if messageData is provided, use it; otherwise use legacy message
        if (!empty($messageData)) {
            $data = array_merge($data, $messageData);
        } else {
            $data['message'] = $message;
        }

        return $this->model->create($data);
    }
}
