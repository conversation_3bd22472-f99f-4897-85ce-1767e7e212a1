<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $debt = $this->project->asDebtor()->where(['creditor_id'=> $this->partner_id ,  'type' => 'purchase'])->first();

        return [
            'id' => $this->id,
            'type' => $this->type,
            'name' => $this->name,
            'number' => $this->number,
            'partner' => $this->partner->name,
            'total_after_discount' => $this->total_after_discount,
            'status' => $this->status,
            'paid_amount' => $this->paid_amount,
            'remaining_amount' => $this->remaining_amount,
            'date' => $this->date->format('Y-m-d'),
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
            'created_by' => $this->created_by,
            'debt' => [
                'total_invoices' => $debt?->total_invoices,
                'total_payments' => $debt?->total_payments,
                'total_debt' => $debt?->total_debt
            ]
        ];
    }
}
