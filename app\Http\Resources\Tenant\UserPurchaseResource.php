<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserPurchaseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $purchase = $this->transactionable; // Get the associated purchase from the transaction

        return [
            'id' => $purchase->id,
            'number' => $purchase->number,
            'name' => $purchase->name,
            'partner' => $purchase->partner->name,
            'project' => $purchase->project->name,
            'date' => $purchase->date->format('Y-m-d'),
            'total' => $purchase->total_after_discount,
            'status' => $purchase->status,
        ];
    }
}
