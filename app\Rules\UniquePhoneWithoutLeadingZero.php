<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UniquePhoneWithoutLeadingZero implements ValidationRule
{
    public function __construct(
        protected string $table,
        protected ?string $column = 'phone',
        protected ?int $ignoreId = null
    ) {}

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $normalizedPhone = ltrim($value, '0');

        $exists = DB::table($this->table)
            ->when($this->ignoreId, fn ($q) => $q->where('id', '!=', $this->ignoreId))
            ->whereRaw("TRIM(LEADING '0' FROM {$this->column}) = ?", [$normalizedPhone])
            ->exists();

        if ($exists) {
            $fail(__('validation.unique', ['attribute' => __('validation.attributes.phone')]));
        }
    }
}
