<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Validation\Rule;
use App\Rules\IgnoreSuperAdminRole;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniquePhoneWithoutLeadingZero;
use App\Rules\PasswordNotContainPersonalInfo;
use Illuminate\Contracts\Validation\ValidationRule;

class CreateAdminRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg', 'max:5120'],
            'name' => 'required|string|min:3|max:30',
            'email' => 'required|email|unique:admins,email',
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('admins')],
            'details' => ['sometimes', 'string', 'max:255'],
            'status' => 'sometimes|in:Active,Disabled',
            'role_id' => ['required', 'exists:roles,id', new IgnoreSuperAdminRole],
            'password_type' => 'required|in:Manual,Auto',
            'password' => [
                'required',
                'string',
                Password::min(8)->mixedCase()->numbers()->symbols(),
                Rule::when(request('password_type') === 'Manual', 'confirmed'),
                new PasswordNotContainPersonalInfo([
                    request('email'),
                    request('phone'),
                    request('name'),
                ]),
            ],
            'permissions' => 'sometimes|array',
            'permissions.*' => 'exists:permissions,id',
        ];
    }
}
