<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CanCreateAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $subscription = tenant()->subscription;

        $name = __('Admin');

        if (! $subscription->is_unlimited_admins && $subscription->number_of_admins <= 0) {
            return error(__('You have reached your subscription limit, you cannot add a new :name', ['name' => $name]), 400);
        }

        return $next($request);
    }
}
