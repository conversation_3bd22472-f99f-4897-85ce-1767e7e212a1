<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\User;
use App\Models\Tenant\Project;
use App\Models\Tenant\Setting;
use Illuminate\Support\Facades\Storage;

class SettingRepository
{
    public function __construct(public Setting $model) {}

    public function getAll($type)
    {
        return $this->model->where('type', $type)->get();
    }

    public function create(array $data, string $type)
    {
        foreach ($data as $key => $value) {
            $this->model->create(['key' => $key, 'value' => $value, 'type' => $type]);
        }
    }

    public function update(array $data)
    {
        $type = $data['type']; // Ensure type is fixed for all updates

        foreach ($data as $key => $value) {
            if ($key === 'type') continue; // Skip 'type' key in loop

            // Retrieve or create setting with the fixed type
            $query = $this->model->firstOrCreate(
                ['key' => $key, 'type' => $type], // Ensure type is considered in lookup
                ['key' => $key, 'value' => $value, 'type' => $type]
            );

            // Handle logo separately
            if ($key === 'logo' && request()->hasFile('logo')) {
                $existingLogo = $query->value;
                if ($existingLogo) {
                    Storage::delete($existingLogo);
                }

                $value = request()->file('logo')->store('settings', 'public');
            }

            // Update value while ensuring type remains unchanged
            $query->update(['value' => $value]);
        }
    }


    public function balanceCheck($amount, $user_id = null, $project_id = null)
    {
        // Retrieve withdrawal settings
        $setting = collect($this->getAll('withdraw'))->pluck('value', 'key');

        // Check user if user_id is provided
        if ($user_id) {
            $user = User::find($user_id);

            if (!$user) {
                return [
                    'status' => false,
                    'message' => __('User not found.')
                ];
            }
            
            // Check if user has unlimited spending
            if (count($setting) > 0 && $setting['user_unlimitid'] == '1') {
                return ['status' => true, 'message' => __('Withdrawal approved (User Unlimited Spending).')];
            }

            // Check if user withdrawals are allowed
            if ( count($setting) > 0 && $setting['user_allow_withdraw'] == '0') {
                return [
                    'status' => false,
                    'message' => __('User withdrawals are not allowed.')
                ];
            }

            // Check user balance and limits
            $userBalance = $user->balance;
            $userOverdraftLimit = (int) ($setting['user_overdraft_limit'] ?? 0);
            $newUserBalance = $userBalance - $amount;

            if ($newUserBalance < -$userOverdraftLimit) {
                return [
                    'status' => false,
                    'message' => __('User insufficient balance. Maximum allowed overdraft is :limit', ['limit' => $userOverdraftLimit])
                ];
            }
        }

        // Check project if project_id is provided
        if ($project_id) {
            $project = Project::find($project_id);
            if (!$project) {
                return [
                    'status' => false,
                    'message' => __('Project not found.')
                ];
            }

            // Check if project has unlimited spending
            if (count($setting) > 0 &&$setting['project_unlimitid'] == '1') {
                return ['status' => true, 'message' => __('Withdrawal approved (Project Unlimited Spending).')];
            }

            // Check project withdrawal permission
            if (count($setting) > 0 &&$setting['project_allow_withdraw'] == '0') {
                return [
                    'status' => false,
                    'message' => __('Project withdrawals are not allowed.')
                ];
            }

            // Check project balance and limits
            $projectBalance = $project->balance;
            $projectOverdraftLimit = (int) ($setting['project_overdraft_limit'] ?? 0);
            $newProjectBalance = $projectBalance - $amount;

            if ($newProjectBalance < -$projectOverdraftLimit) {
                return [
                    'status' => false,
                    'message' => __('Project insufficient balance. Maximum allowed overdraft is :limit', ['limit' => $projectOverdraftLimit])
                ];
            }
        }

        // If no specific checks were performed (neither user nor project provided)
        if (!$user_id && !$project_id) {
            return [
                'status' => false,
                'message' => __('Either user or project must be specified for balance check.')
            ];
        }

        return ['status' => true, 'message' => __('Withdrawal approved.')];
    }
}

