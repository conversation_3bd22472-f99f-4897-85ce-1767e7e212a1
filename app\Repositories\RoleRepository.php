<?php

namespace App\Repositories;

use App\Models\Role;

class RoleRepository
{
    public function __construct(public Role $model) {}

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function getPaginated($filters = [], int $limit = 10)
    {
        return $this->model->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where('name', 'like', "%{$filters['search']}%");
        })->when(isset($filters['status']), function ($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function getWhere(array $data)
    {
        return $this->model->where($data)->orderByDesc('id')->get();
    }
}
