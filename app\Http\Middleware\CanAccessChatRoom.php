<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CanAccessChatRoom
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        $roomId = $request->room;

        // Check if tenant has chat availability first
        if (! tenant()->subscription->has_chat_availability) {
            return error('Your subscription does not have chat availability.', 403);
        }

        // Check if room exists for the user
        if ($roomId && ! $user->rooms()->where('room_id', $roomId)->exists()) {
            return error('Chat not found', 404);
        }

        return $next($request);
    }
}
