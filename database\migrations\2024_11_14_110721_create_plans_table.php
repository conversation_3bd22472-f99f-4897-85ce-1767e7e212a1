<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->double('price');
            $table->integer('number_of_projects')->default(0);
            $table->integer('number_of_site_engineers')->default(0);
            $table->integer('number_of_office_engineers')->default(0);
            $table->integer('number_of_accountants')->default(0);
            $table->integer('number_of_project_managers')->default(0);
            $table->integer('number_of_admins')->default(0);
            $table->integer('storage')->default(0);
            $table->enum('storage_type', ['MB', 'GB', 'TB'])->default('MB');
            $table->enum('status', ['Active', 'Disabled'])->default('Active');
            $table->boolean('has_domain')->default(false);
            $table->boolean('has_free_website')->default(false);
            $table->boolean('has_chat_availability')->default(false);

            // unlimited
            $table->boolean('is_unlimited_projects')->default(false);
            $table->boolean('is_unlimited_site_engineers')->default(false);
            $table->boolean('is_unlimited_office_engineers')->default(false);
            $table->boolean('is_unlimited_accountants')->default(false);
            $table->boolean('is_unlimited_project_managers')->default(false);
            $table->boolean('is_unlimited_admins')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
