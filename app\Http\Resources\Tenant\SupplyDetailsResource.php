<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplyDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'name' => $this->name,
            'date' => $this->date,
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
            'partner' => $this->partner ? [
                'id' => $this->partner->id,
                'name' => $this->partner->name,
            ] : null,
            'notes' => $this->notes,
            'categories' => SupplyCategoryResource::collection($this->invoiceCategory),
            'total' => $this->total,
        ];
    }
}
