<?php

namespace App\Services;

use App\DTO\GroupNotificationData;
use App\Models\Office;
use App\Notifications\GroupNotification;
use App\Repositories\AdminRepository;
use App\Repositories\GroupNotificationRepository;
use App\Repositories\OfficeRepository;
use App\Repositories\Tenant\UserRepository as TenantUserRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class GroupNotificationService
{
    public function __construct(
        public GroupNotificationRepository $groupNotificationRepo,
        public OfficeRepository $officeRepo,
        public AdminRepository $adminRepo,
        public TenantUserRepository $tenantUserRepo
    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'date_from', 'date_to']);

        return $this->groupNotificationRepo->getPaginated($filterable);
    }

    public function getById($id)
    {
        $notification = $this->groupNotificationRepo->findById($id);

        if (! $notification) {
            throw new NotFoundHttpException;
        }

        return $notification;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $request['sender_name'] = auth()->user()->name;

            $data = GroupNotificationData::from($request)->all();

            $notification = $this->groupNotificationRepo->create($data);
            // Handle users in the receivers array
            if (! empty($request['receivers']['offices'])) {
                foreach ($request['receivers']['offices'] as $officeId) {
                    $office = $this->officeRepo->findById($officeId);

                    if ($office) {
                        if ($office->tenant) {
                            tenancy()->initialize($office->tenant->id);
                            $admin = $this->tenantUserRepo->getSuperAdmin();
                            if ($admin) {
                                $admin->notify(new GroupNotification($request)); // Send notification to the admin
                            }
                            tenancy()->end();
                        }

                        $office->receivers()->create([
                            'group_notification_id' => $notification->id,
                        ]);
                    }
                }
            }

            // Handle admins in the receivers array
            if (! empty($request['receivers']['admins'])) {
                foreach ($request['receivers']['admins'] as $adminId) {
                    $admin = $this->adminRepo->findAdmin($adminId);

                    if ($admin) {
                        $admin->notify(new GroupNotification($request)); // Send notification to the admin

                        $admin->receivers()->create([
                            'group_notification_id' => $notification->id,
                        ]);
                    }
                }
            }

            DB::commit();

            return success(__('Notification Sent Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function delete($id)
    {
        $notification = $this->getById($id);

        return $notification->delete();
    }

    public function resend($id)
    {
        $notification = $this->getById($id);

        if ($notification->notification_receivers->isEmpty()) {
            return error(__('No receivers found for this notification.'), 422);
        }

        $clone = $notification->replicate();

        $clone->save();

        $data = [
            'title' => $notification->title,
            'body' => $notification->body,
            'sender_name' => $notification->sender_name,
            'receivers' => $notification->notification_receivers->map(function ($receiver) {
                return [
                    'type' => $receiver->receiverable_type,
                    'id' => $receiver->receiverable_id,
                ];
            })->toArray(),
        ];

        // Resend notifications to all receivers
        foreach ($notification->notification_receivers as $receiver) {
            $receiverModel = $receiver->receiverable;

            if ($receiverModel) {
                // Check if the receiver is an office
                if ($receiverModel instanceof Office) {
                    $office = $this->officeRepo->findById($receiverModel->id);

                    if ($office && $office->tenant) {
                        // Initialize tenancy
                        tenancy()->initialize($office->tenant->id);

                        // Notify the super admin of the tenant
                        $admin = $this->tenantUserRepo->getSuperAdmin();
                        if ($admin) {
                            $admin->notify(new GroupNotification($data));
                        }

                        // End tenancy
                        tenancy()->end();
                    }

                    // Record the receiver
                    $office->receivers()->create([
                        'group_notification_id' => $clone->id,
                    ]);
                } else {
                    // Notify other receiver types
                    $receiverModel->notify(new GroupNotification($data));
                    $receiverModel->receivers()->create([
                        'group_notification_id' => $clone->id,
                    ]);
                }
            }
        }

        return success(__('Notification Resent Successfully'));
    }
}
