<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\ProfileService;
use App\Http\Requests\Tenant\UpdateProfileRequest;
use App\Http\Requests\Tenant\UpdateProfilePasswordRequest;

class ProfileController extends Controller
{
    public function __construct(public ProfileService $profileService) {}

    public function me()
    {
        return $this->profileService->get();
    }

    public function update(UpdateProfileRequest $request)
    {
        return $this->profileService->update($request->validated());
    }

    public function updatePassword(UpdateProfilePasswordRequest $request)
    {
        return $this->profileService->updatePassword($request->validated());
    }
}
