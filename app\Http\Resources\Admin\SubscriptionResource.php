<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $remainingMonths = now()->lessThan($this->end_date)
            ? now()->diffInMonths($this->end_date)
            : 0;

        $remainingAmount = ($this->status === 'Ended' || $remainingMonths === 0)
            ? 0
            : $remainingMonths * $this->plan_price;

        return [
            'id' => $this->id,
            'office' => $this->office->name,
            'plan' => $this->plan->name,
            'duration' => $this->duration,
            'duration_left' => $remainingMonths,
            'plan_price' => $this->plan_price,
            'updated_plan_price' => $this->plan->price,
            'total' => $this->total,
            'status' => $this->status,
            'remaining_amount' => $remainingAmount,
            'start_date' => $this->start_date->format('Y-m-d'),
            'end_date' => $this->end_date->format('Y-m-d'),
            'has_domain' => (bool) $this->has_domain,
        ];
    }
}
