<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Refund;
use Carbon\Carbon;

class RefundRepository
{
    public function __construct(public Refund $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->where('project_id', request()->project)->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where('name', 'like', "%{$filters['search']}%")
                ->orWhere('number', 'like', "%{$filters['search']}%")
                ->orWhere('created_by', 'like', "%{$filters['search']}%");
        })->when(isset($filters['partner']), function ($query) use ($filters) {
            return $query->where('partner_id', $filters['partner']);
        })->when(isset($filters['status']), function ($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })->when(isset($filters['type']), function ($query) use ($filters) {
            return $query->where('type', $filters['type']);
        })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
            // Handle case when only date_to is provided
            if (isset($filters['date_to']) && !isset($filters['date_from'])) {
                return $query->where('created_at', '<=', Carbon::parse($filters['date_to'])->endOfDay());
            }

            // Handle case when only date_from is provided
            if (isset($filters['date_from']) && !isset($filters['date_to'])) {
                return $query->where('created_at', '>=', Carbon::parse($filters['date_from'])->startOfDay());
            }

            // Handle case when both dates are provided
            $dates = [
                Carbon::parse($filters['date_from'])->startOfDay(),
                Carbon::parse($filters['date_to'])->endOfDay(),
            ];

            return $query->whereBetween('created_at', $dates);
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->where('permanent_deleted', false)->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findById(string $id)
    {
        return $this->model->with(['supplier', 'media', 'invoiceCategory'])->find($id);
    }

    public function getRefundsTotal()
    {
        return $this->model->where('project_id', request()->project)->sum('total_after_discount');
    }

    public function totalRefundes()
    {
        return $this->model->where('project_id', request()->project_id)->sum('paid_amount');
    }
}
