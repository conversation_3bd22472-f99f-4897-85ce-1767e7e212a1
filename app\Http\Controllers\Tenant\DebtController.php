<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\DebtResource;
use App\Services\Tenant\DebtService;

class DebtController extends Controller
{
    public function __construct(protected DebtService $debtService)
    {
        //
    }

    public function purchasesDebts(string $projectId)
    {
        $debts = $this->debtService->getPurchaseDebts($projectId);

        $totalDebts = $this->debtService->getTotalDebts($projectId, 'purchase');

        return success(new BaseCollection($debts, DebtResource::class, [
            'total_debts' => $totalDebts,
        ]));
    }

    public function refundsDebts(string $projectId)
    {
        $debts = $this->debtService->getRefundDebts($projectId);

        $totalDebts = $this->debtService->getTotalDebts($projectId, 'refund');

        return success(new BaseCollection($debts, DebtResource::class, [
            'total_debts' => $totalDebts,
        ]));
    }

    public function contractorDebts(string $projectId)
    {
        $debts = $this->debtService->getContractorDebts($projectId);

        $totalDebts = $this->debtService->getTotalDebts($projectId, 'certification');

        return success(new BaseCollection($debts, DebtResource::class, [
            'total_debts' => $totalDebts,
        ]));
    }
}
