<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'type' => $this->type,
            'status' => $this->status,
            'phone' => $this->phone,
            'address' => $this->address,
            'national_id' => $this->national_id,
            'balance' => $this->balance,
            'details' => $this->details,
            'image' => MediaResource::make($this->image),
            'projects' => UserProjectsResource::collection($this->projects),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
