<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\MessageData;
use App\Events\MessageReadEvent;
use App\Repositories\Tenant\ChatRepository;

class ChatService
{
    public function __construct(public ChatRepository $chatRepo) {}

    public function get(string $roomId)
    {
        $messages = $this->chatRepo->get($roomId);

        return $messages;
    }

    public function create(array $request)
    {
        $user = auth()->user();

        if (request()->hasFile('file')) {
            $request['file_path'] = request()->file('file')->store('files', 'public');
        }

        $request['room_id'] = request()->route('room');

        $data = MessageData::from($request)->all();

        $message = $user->messages()->create($data);

        if (request()->hasFile('file')) {
            $message->addMedia(request()->file('file'))
                ->toMediaCollection('messages');
        }

        $message->readers()->attach($user->id, ['read_at' => now()]);

        return $message;
    }

    public function markAsRead(string $roomId): bool
    {
        $user = auth()->user();

        $lastMessage = $this->chatRepo->getLastMessage($roomId, $user->id);

        if (! $lastMessage) {
            return false;
        }

        $lastMessage->readers()->syncWithoutDetaching([
            $user->id => ['read_at' => now()],
        ]);

        event(new MessageReadEvent(
            roomId: $roomId,
            userId: $user->id,
            message: $lastMessage,
        ));

        return true;
    }
}
