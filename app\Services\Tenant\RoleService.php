<?php

namespace App\Services\Tenant;

use Exception;
use App\DTO\RoleData;
use Illuminate\Support\Facades\DB;
use App\Repositories\PermissionRepository;
use App\Repositories\Tenant\RoleRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class RoleService
{
    public function __construct(public RoleRepository $roleRepo, public PermissionRepository $permissionRepo) {}

    public function get()
    {
        $filterable = request()->only(['search', 'status']);

        return $this->roleRepo->getPaginated($filterable);
    }

    public function getById($id)
    {
        $role = $this->roleRepo->findById($id);

        if (! $role) {
            throw new NotFoundHttpException;
        }

        return $role;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $request['guard_name'] = 'admin';

            $data = RoleData::from($request)->all();

            $role = $this->roleRepo->create($data);

            $permissions = $this->permissionRepo->getWhereIn($request['permissions']);

            $role->syncPermissions($permissions);

            DB::commit();

            return $role;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'Role creation failed. Please check your input.');
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        try {
            $data = RoleData::from($request)->all();

            $role = $this->getById($id);

            $role->update($data);

            $role->admins()->update(['status' => $request['status']]);

            $permissions = $this->permissionRepo->getWhereIn($request['permissions']);

            $role->syncPermissions($permissions);

            DB::commit();

            return $role;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'Role update failed. Please check your input.');
        }
    }

    public function delete($id)
    {
        $role = $this->getById($id);

        if ($role->admins->isNotEmpty()) {
            return error(__('You cannot delete this role.'), 400);
        }

        $role->delete();

        return success(__('Deleted Successfully'));
    }

    public function getActiveRoles()
    {
        return $this->roleRepo->getWhere([['status', 'active'], ['is_built_in', false]]);
    }
}
