<?php

namespace App\Models\Tenant;

use Spa<PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Client extends Model implements HasMedia 
{
    use SoftDeletes , InteractsWithMedia;

    protected $fillable = [
        'image',
        'name',
        'email',
        'details',
        'status',
        'phone',
        'referred_by',
    ];

    /**
     * Get the projects associated with the client.
     */
    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'client_project')
            ->withTimestamps();
    }

    

    public function getImageAttribute()
    {
        return $this->getMedia('clients')->first();
    }
}
