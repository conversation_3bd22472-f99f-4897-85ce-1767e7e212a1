<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Partner;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PartnerRepository
{
    public function __construct(public Partner $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->where('type', request('user_type'))->when(isset($filters['search']), function ($query) use ($filters) {
            $query->where('name', 'like', '%' . $filters['search'] . '%');
        })->when(isset($filters['status']), function ($query) use ($filters) {
            $query->where('status', $filters['status']);
        })->when(isset($filters['item']), function ($query) use ($filters) {
            $query->whereHas('items', function ($query) use ($filters) {
                $query->where('items.id', $filters['item']);
            });
        })->when(isset($filters['project']), function ($query) {
            // TODO: Implement project filter
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getTrashed($limit = 10)
    {
        return $this->model->where(['type'=> request('user_type') , 'permanent_deleted'=>0])->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function findOnlyTrashedById($id)
    {
        return $this->model->onlyTrashed()->where('permanent_deleted', 0)->find($id);
    }

    public function getAll(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }

    public function getwithoutglobalscopes($id)
    {
        return $this->model->where('id', $id)->withoutGlobalScopes()->first();
    }

    public function updateBalance($user, $balance, $operator)
    {
        $partner = $this->getwithoutglobalscopes($user);
        if (! $partner) {
            throw new NotFoundHttpException('partner not found.');
        }

        $partner->balance = $operator === '+' ? $partner->balance + $balance : $partner->balance - $balance;

        $partner->save();

        return $partner;
    }

    public function getDdl(string $type, array $columns = ['*'])
    {
        return match ($type) {
            'all' => $this->model->all($columns),
            default => $this->model->where('type', $type)->get($columns),
        };
    }
}
