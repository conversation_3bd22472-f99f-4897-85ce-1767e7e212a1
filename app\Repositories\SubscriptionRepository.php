<?php

namespace App\Repositories;

use App\Models\Subscription;

class SubscriptionRepository
{
    public function __construct(public Subscription $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->with(['plan', 'office'])->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->whereHas('plan', function ($query) use ($filters) {
                $query->where('name_ar', 'like', "%{$filters['search']}%")
                    ->orWhere('name_en', 'like', "%{$filters['search']}%");
            })->orWhereHas('office', function ($query) use ($filters) {
                $query->where('name_ar', 'like', "%{$filters['search']}%")
                    ->orWhere('name_en', 'like', "%{$filters['search']}%");
            });
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function findById(string $id)
    {
        return $this->model->with(['plan', 'office'])->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function claculateProfit(array $filters = [])
    {
        return $this->model->when(isset($filters['start_date']), function ($query) use ($filters) {
            return $query->whereDate('created_at', '>=', $filters['start_date']);
        })->when(isset($filters['end_date']), function ($query) use ($filters) {
            return $query->whereDate('created_at', '<=', $filters['end_date']);
        })->sum('total');
    }

    public function getGroupedSubscriptions()
    {
        return $this->model->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total) as total')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at), MONTH(created_at)')
            ->get();
    }

    public function getGroupedByPlan()
    {
        return $this->model->with('plan')
            ->selectRaw('plan_id, COUNT(*) as count, COUNT(DISTINCT office_id) as office_count')
            ->groupBy('plan_id')
            ->get();
    }

    public function getCountWhere(array $data)
    {
        return $this->model->where([$data])->count();
    }
}
