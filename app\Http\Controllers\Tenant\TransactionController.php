<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\TransactionResource;
use App\Services\Tenant\TransactionService;

class TransactionController extends Controller
{
    public function __construct(protected TransactionService $transactionService)
    {
        //
    }

    public function index(string $projectId)
    {
        $transactions = $this->transactionService->get($projectId);

        return success(new BaseCollection($transactions, TransactionResource::class));
    }
}
