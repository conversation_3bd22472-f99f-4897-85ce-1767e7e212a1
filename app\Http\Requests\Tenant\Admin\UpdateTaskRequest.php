<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTaskRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['sometimes', 'string', 'min:3', 'max:100'],
            'description' => ['sometimes', 'string', 'min:3', 'max:255'],
            'project_id' => ['sometimes', 'exists:projects,id'],
            'user_id' => ['sometimes', 'exists:users,id'],
            'status' => ['sometimes', Rule::in(['new', 'in_progress', 'finished', 'cancelled'])],
            'priority' => ['sometimes', Rule::in(['low', 'medium', 'high'])],
            'date_from' => ['sometimes', 'date'],
            'date_to' => ['sometimes', 'date', 'after:date_from'],
            'images' => ['nullable', 'array', 'min:1', 'max:10'],
            'images.*' => ['nullable', 'mimes:doc,docx,pdf,xlsx,jpg,jpeg,png,gif,bmp,svg,webp', 'max:5120'],

        ];
    }
}