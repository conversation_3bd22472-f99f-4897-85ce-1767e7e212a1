<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class DomainResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $isDefaultDomain = Str::contains($this->domain, config('app.domain'));

        return [
            'name' => $isDefaultDomain ? Str::replace('.'.config('app.domain'), '', $this->domain) : $this->domain,
            'isDefault' => $isDefaultDomain,
        ];
    }
}
