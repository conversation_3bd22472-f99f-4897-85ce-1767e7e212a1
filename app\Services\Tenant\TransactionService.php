<?php

namespace App\Services\Tenant;

use App\Repositories\Tenant\TransactionRepository;

class TransactionService
{
    public function __construct(protected TransactionRepository $transactionRepo)
    {
        //
    }

    public function get(string $projectId)
    {
        $filterable = request()->only(['date_from', 'date_to']);

        return $this->transactionRepo->getPaginated($projectId, $filterable);
    }
}
