<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('office_dues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->cascadeOnDelete();
            $table->double('project_expenses')->default(0.0);
            $table->integer('office_ratio')->nullable();
            $table->integer('office_total')->nullable();

            $table->double('total_profits')->default(0.0);
            $table->double('recieved_profits')->default(0.0);
            $table->double('due_profits')->default(0.0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('office_dues');
    }
};
