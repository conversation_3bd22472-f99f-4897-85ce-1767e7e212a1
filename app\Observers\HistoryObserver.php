<?php

namespace App\Observers;

use App\Models\History;
use App\Models\Tenant\History as TenantHistory;

class HistoryObserver
{
    public function created($model): void
    {
        $this->logAction('created', $model);
    }

    public function updated($model): void
    {
        $this->logAction('updated', $model);
    }

    public function deleted($model): void
    {
        $this->logAction('deleted', $model);
    }

    public function restored($model): void
    {
        $this->logAction('restored', $model);
    }

    public function forceDeleted($model): void
    {
        $this->logAction('force_deleted', $model);
    }

    private function logAction(string $action, $model): void
    {
        $causerName = auth()->user()->name ?? 'System';
        $modelBaseName = class_basename($model);

        [$modelNameEn, $modelNameAr] = $this->getModelNames($model, $modelBaseName);

        $messageEn = $this->formatMessage($action, $causerName, $modelNameEn, 'en');
        $messageAr = $this->formatMessage($action, $causerName, $modelNameAr, 'ar');

        $this->createHistoryRecord($causerName, $modelBaseName, $messageEn, $messageAr);
    }

    private function getModelNames($model, string $modelBaseName): array
    {
        // Use model name as first option, then translation, then basename
        if ($model->name) {
            return [$model->name, $model->name];
        }

        // Get translated model names
        $modelNameEn = __("model.$modelBaseName", [], 'en');
        $modelNameAr = __("model.$modelBaseName", [], 'ar');

        // If translation not found, use basename
        if ($modelNameEn === "model.$modelBaseName") {
            $modelNameEn = $modelBaseName;
        }
        if ($modelNameAr === "model.$modelBaseName") {
            $modelNameAr = $modelBaseName;
        }

        return [$modelNameEn, $modelNameAr];
    }

    private function formatMessage(string $action, string $causerName, string $modelName, string $locale): string
    {
        $messages = $this->getActionMessages($locale);

        if (isset($messages[$action])) {
            return $messages[$action]($causerName, $modelName);
        }

        return $this->getDefaultMessage($causerName, $modelName, $locale);
    }

    private function getActionMessages(string $locale): array
    {
        if ($locale === 'ar') {
            return [
                'created' => fn($causer, $model) => "$causer قام بإنشاء $model.",
                'updated' => fn($causer, $model) => "$causer قام بتحديث $model.",
                'deleted' => fn($causer, $model) => "$causer قام بحذف $model.",
                'restored' => fn($causer, $model) => "$causer قام باستعادة $model.",
                'force_deleted' => fn($causer, $model) => "$causer قام بحذف $model نهائيًا.",
            ];
        }

        return [
            'created' => fn($causer, $model) => "$causer has created $model.",
            'updated' => fn($causer, $model) => "$causer has updated $model.",
            'deleted' => fn($causer, $model) => "$causer has deleted $model.",
            'restored' => fn($causer, $model) => "$causer has restored $model.",
            'force_deleted' => fn($causer, $model) => "$causer has permanently deleted $model.",
        ];
    }

    private function getDefaultMessage(string $causerName, string $modelName, string $locale): string
    {
        return $locale === 'ar'
            ? "$causerName قام بإجراء على $modelName."
            : "$causerName has performed an action on $modelName.";
    }

    private function createHistoryRecord(string $causerName, string $modelBaseName, string $messageEn, string $messageAr): void
    {
        $data = [
            'causer_name' => $causerName,
            'model' => $modelBaseName,
            'message_ar' => $messageAr,
            'message_en' => $messageEn,
        ];

        if ($this->isCentralDomain()) {
            History::create($data);
        } else {
            TenantHistory::create(array_merge($data, [
                'project_id' => request()->project_id ?? null,
            ]));
        }
    }

    private function isCentralDomain(): bool
    {
        return in_array(request()->getHost(), config('tenancy.central_domains', []));
    }
}
