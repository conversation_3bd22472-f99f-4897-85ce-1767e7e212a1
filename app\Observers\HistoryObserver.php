<?php

namespace App\Observers;

use App\Models\History;
use App\Models\Tenant\History as TenantHistory;

class HistoryObserver
{
    public function created($model)
    {
        $this->logAction('created', $model);
    }

    public function updated($model)
    {
        $this->logAction('updated', $model);
    }

    public function deleted($model)
    {
        $this->logAction('deleted', $model);
    }

    public function restored($model)
    {
        $this->logAction('restored', $model);
    }

    public function forceDeleted($model)
    {
        $this->logAction('force_deleted', $model);
    }

    private function logAction(string $action, $model)
    {
        $causerName = auth()->user()->name ?? 'System';
        $modelName = $model->name ?? class_basename($model);

        // Format the action message
        $englishMessages = [
            'created' => "$causerName has created a new $modelName.",
            'updated' => "$causerName has updated an existing $modelName.",
            'deleted' => "$causerName has deleted a $modelName.",
            'restored' => "$causerName has restored a $modelName.",
            'force_deleted' => "$causerName has permanently deleted a $modelName.",
        ];

        $arabicMessages = [
            'created' => "$causerName قام بإنشاء $modelName جديد.",
            'updated' => "$causerName قام بتحديث $modelName موجود.",
            'deleted' => "$causerName قام بحذف $modelName.",
            'restored' => "$causerName قام باستعادة $modelName.",
            'force_deleted' => "$causerName قام بحذف $modelName نهائيًا.",
        ];

        $messageAr = $arabicMessages[$action] ?? "$causerName قام بإجراء على $modelName.";
        $messageEn = $englishMessages[$action] ?? "$causerName has performed an action on $modelName.";

        // Check if we are in the central domain
        if ($this->isCentralDomain()) {
            History::create([
                'causer_name' => $causerName,
                'model' => $modelName,
                'message_ar' => $messageAr,
                'message_en' => $messageEn,
            ]);
        } else {
            TenantHistory::create([
                'causer_name' => $causerName,
                'model' => $modelName,
                'message_ar' => $messageAr,
                'message_en' => $messageEn,
                'project_id' => request()->project_id ?? null,
            ]);
        }
    }

    /**
     * Determine if we are in the central domain.
     */
    private function isCentralDomain(): bool
    {
        return in_array(request()->getHost(), config('tenancy.central_domains', []));
    }
}
