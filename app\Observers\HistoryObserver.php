<?php

namespace App\Observers;

use App\Models\History;
use App\Models\Tenant\History as TenantHistory;

class HistoryObserver
{
    public function created($model)
    {
        $this->logAction('created', $model);
    }

    public function updated($model)
    {
        $this->logAction('updated', $model);
    }

    public function deleted($model)
    {
        $this->logAction('deleted', $model);
    }

    public function restored($model)
    {
        $this->logAction('restored', $model);
    }

    public function forceDeleted($model)
    {
        $this->logAction('force_deleted', $model);
    }

    private function logAction(string $action, $model)
    {
        $causerName = auth()->user()->name ?? 'System';
        $modelBaseName = class_basename($model);

        // Use model name as first option, then translation, then basename
        if ($model->name) {
            $modelNameEn = $model->name;
            $modelNameAr = $model->name;
        } else {
            // Get translated model names
            $modelNameEn = __("model.$modelBaseName", [], 'en');
            $modelNameAr = __("model.$modelBaseName", [], 'ar');

            // If translation not found, use basename
            if ($modelNameEn === "model.$modelBaseName") {
                $modelNameEn = $modelBaseName;
            }
            if ($modelNameAr === "model.$modelBaseName") {
                $modelNameAr = $modelBaseName;
            }
        }

        // Format the action message
        $englishMessages = [
            'created' => "$causerName has created $modelNameEn.",
            'updated' => "$causerName has updated an $modelNameEn.",
            'deleted' => "$causerName has deleted a $modelNameEn.",
            'restored' => "$causerName has restored a $modelNameEn.",
            'force_deleted' => "$causerName has permanently deleted a $modelNameEn.",
        ];

        $arabicMessages = [
            'created' => "$causerName قام بإنشاء $modelNameAr.",
            'updated' => "$causerName قام بتحديث $modelNameAr.",
            'deleted' => "$causerName قام بحذف $modelNameAr.",
            'restored' => "$causerName قام باستعادة $modelNameAr.",
            'force_deleted' => "$causerName قام بحذف $modelNameAr نهائيًا.",
        ];

        $messageAr = $arabicMessages[$action] ?? "$causerName قام بإجراء على $modelNameAr.";
        $messageEn = $englishMessages[$action] ?? "$causerName has performed an action on $modelNameEn.";

        // Check if we are in the central domain
        if ($this->isCentralDomain()) {
            History::create([
                'causer_name' => $causerName,
                'model' => $modelBaseName,
                'message_ar' => $messageAr,
                'message_en' => $messageEn,
            ]);
        } else {
            TenantHistory::create([
                'causer_name' => $causerName,
                'model' => $modelBaseName,
                'message_ar' => $messageAr,
                'message_en' => $messageEn,
                'project_id' => request()->project_id ?? null,
            ]);
        }
    }

    /**
     * Determine if we are in the central domain.
     */
    private function isCentralDomain(): bool
    {
        return in_array(request()->getHost(), config('tenancy.central_domains', []));
    }
}
