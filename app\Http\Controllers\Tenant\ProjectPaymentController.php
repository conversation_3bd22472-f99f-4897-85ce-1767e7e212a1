<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateProjectPaymentRequest;
use App\Http\Requests\Tenant\Admin\UpdateProjectPaymentRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\ProjectPaymentResource;
use App\Services\Tenant\ProjectPaymentService;

class ProjectPaymentController extends Controller
{
    public function __construct(public ProjectPaymentService $projectPaymentService) {}

    public function index()
    {
        $payments = $this->projectPaymentService->get();

        $totalPayments = $this->projectPaymentService->getTotalPayment();

        return success(new BaseCollection($payments, ProjectPaymentResource::class, [
            'total_payments' => $totalPayments,
        ]));
    }

    public function show(string $id)
    {
        $payment = $this->projectPaymentService->getById($id);

        return success(new ProjectPaymentResource($payment));
    }

    public function store(CreateProjectPaymentRequest $request)
    {
        return $this->projectPaymentService->store($request->validated());
    }

    public function update(UpdateProjectPaymentRequest $request, string $id)
    {
        return $this->projectPaymentService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->projectPaymentService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->projectPaymentService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->projectPaymentService->forceDelete($id);
    }
}
