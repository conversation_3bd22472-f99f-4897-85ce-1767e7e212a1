<?php

namespace App\Services\Tenant\Traits;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Repositories\Tenant\SettingRepository;

/**
 * Trait for reusable, extensible balance checking logic across services.
 */
trait HasSufficientBalanceCheck
{
    /**
     * Safely update an amount field with balance validation and balance update logic.
     *
     * @param float|int $oldAmount The previous amount
     * @param float|int $newAmount The new amount to set
     * @param callable $balanceUpdater Callback to update balances, signature: function(float|int $difference): void
     * @param string $context The context for balance check (e.g. 'purchase', 'refund', etc.)
     * @param array $entities Entity IDs for balance check (e.g. ['user_id' => ..., 'project_id' => ...])
     * @param SettingRepository|null $settingRepository
     * @throws Exception if balance is insufficient
     * @return bool True if update was performed, false if skipped (amount unchanged)
     */
    public function updateAmountWithBalanceCheck(
        float|int $oldAmount,
        float|int $newAmount,
        callable $balanceUpdater,
        string $context,
        array $entities = [],
        ?SettingRepository $settingRepository = null
    ): bool {
        if (floatval($oldAmount) === floatval($newAmount)) {
            // No change, skip balance check and update
            return false;
        }
        // Run balance check for the difference
        $this->assertSufficientBalance(
            $context,
            $newAmount,
            $oldAmount,
            $entities,
            $settingRepository
        );
        // Update balances (difference = new - old)
        $difference = $newAmount - $oldAmount;
        $balanceUpdater($difference);
        return true;
    }

    /**
     * Centralized, extensible balance check for all financial operations.
     *
     * @param string $context E.g. 'purchase', 'refund', 'contractor_payment', 'payment_certificate', 'project_payment'
     * @param float|int $newAmount The new amount to check
     * @param float|int|null $oldAmount The previous amount (if any)
     * @param array $entities Array of relevant entity IDs (e.g., ['user_id' => 1, 'project_id' => 2, ...])
     * @param SettingRepository|null $settingRepository Optional, for static usage
     * @throws Exception if balance is insufficient
     */
    public function assertSufficientBalance(
        string $context,
        float|int $newAmount,
        float|int|null $oldAmount = null,
        array $entities = [],
        ?SettingRepository $settingRepository = null
    ) {
        // Only check if the amount has changed
        if ($oldAmount !== null && floatval($newAmount) === floatval($oldAmount)) {
            return;
        }

        // Use provided SettingRepository or resolve from container
        $settings = $settingRepository ?? app(SettingRepository::class);

        // Context-based logic (easy to extend)
        switch ($context) {
            case 'purchase':
            case 'refund':
            case 'payment_certificate':
            case 'project_payment':
            case 'contractor_payment':
                // All these use the same balanceCheck signature
                $userId = $entities['user_id'] ?? null;
                $projectId = $entities['project_id'] ?? null;
                if (!$userId || !$projectId) {
                    throw new Exception('user_id and project_id are required for balance check');
                }
                $balanceCheck = $settings->balanceCheck($newAmount, $userId, $projectId);
                break;
            default:
                throw new Exception('Unknown balance check context: ' . $context);
        }

        if (!$balanceCheck['status']) {
            DB::rollBack();
            throw new Exception($balanceCheck['message']);
        }
    }
}
