<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\PartnerData;
use App\Repositories\Tenant\PartnerRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PartnerService
{
    public function __construct(public PartnerRepository $repository, protected DebtPaymentService $debtPaymentService) {}

    public function get()
    {
        $filterable = ['search', 'status', 'item'];

        if (request()->type === 'archive') {
            return $this->repository->getTrashed();
        } else {
            return $this->repository->getPaginated(request()->only($filterable));
        }
    }

    private function handlePartnerImage($partner)
    {
        if (request()->hasFile('image')) {
            $partner->clearMediaCollection('partners');
            $partner->addMedia(request()->file('image'))
                ->toMediaCollection('partners');
        }
    }

    public function create(array $request)
    {
        if (request()->hasFile('image')) {
            $request['image'] = request()->file('image')->store('offices', 'public');
        }

        $request['status'] = 'active';

        $data = PartnerData::from($request)->all();

        $partner = $this->repository->create($data);
        $this->handlePartnerImage($partner);

        $partner->items()->sync($request['items']);

        return success(__('Created Successfully'));
    }

    public function getById($id)
    {
        $partner = $this->repository->findById($id);

        if (! $partner) {
            throw new NotFoundHttpException('Partner not found');
        }

        return $partner;
    }

    public function update($id, array $request)
    {
        $partner = $this->getById($id);

        $data = PartnerData::from($request)->all();

        $partner->update($data);

        $this->handlePartnerImage($partner);

        $partner->items()->sync($request['items']);

        return success(__('Updated Successfully'));
    }

    public function delete($id)
    {
        $partner = $this->getById($id);

        $partner->delete();

        return success(__('Deleted Successfully'));
    }

    public function restore($id)
    {
        $partner = $this->repository->findTrashedById($id);

        if (! $partner || $partner->permanent_deleted) {
            throw new NotFoundHttpException('Partner not found');
        }

        $partner->restore();

        return success(__('Restored Successfully'));
    }

    public function forceDelete($id)
    {
        DB::beginTransaction();

        $user = $this->repository->findOnlyTrashedById($id);

        if (! $user || $user->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        try {
            $uniqueFields = ['email', 'mobile', 'national_id'];
            $updates = ['deleted_at' => now(), 'permanent_deleted' => true];

            foreach ($uniqueFields as $field) {
                if (! empty($user->$field)) {
                    $updates[$field] = 'deleted_' . $user->id . '_' . $user->$field;
                }
            }

            $user->update($updates);

            DB::commit();

            return success(__('Deleted Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function ddl(string $type, ?array $columns = ['id', 'name'])
    {
        return $this->repository->getDdl($type, $columns);
    }
}
