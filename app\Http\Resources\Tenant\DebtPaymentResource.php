<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DebtPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'supplier' => $this->debt->type === 'purchase' ? $this->debt->creditor->name : $this->debt->debtor->name,
            'paid_amount' => $this->paid_amount,
            'remaining_debt' => $this->remaining_debt,
            'notes' => $this->notes,
            'created_by' => $this->user->name,
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
        ];
    }
}
