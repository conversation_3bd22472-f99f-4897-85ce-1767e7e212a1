<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Message;

class ChatRepository
{
    public function __construct(public Message $message) {}

    public function get(string $roomId, int $limit = 10)
    {
        $perPage = filter_var(request('per_page'), FILTER_VALIDATE_INT) ?: $limit;

        return $this->message->where('room_id', $roomId)
            ->orderByDesc('id')
            ->paginate($perPage);
    }

    public function getlastMessage(string $roomId)
    {
        return $this->message->where('room_id', $roomId)
            ->latest('id')
            ->first();
    }
}
