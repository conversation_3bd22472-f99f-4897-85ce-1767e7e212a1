<?php

namespace App\Services\Tenant;

use Exception;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\OfficeDueTransactionData;
use App\Repositories\Tenant\OfficeDueTransactionRepository;
use App\Repositories\Tenant\ProjectRepository;

class OfficeDueTransactionService
{
    public function __construct(protected OfficeDueTransactionRepository $OfficeDueTransactionRepository, protected ProjectRepository $projectRep)
    {
        //
    }

    public function index(){

        return $this->OfficeDueTransactionRepository->getPaginated();
        
    }

    public function create(array $request)
    {
        DB::beginTransaction(); 
    
        try {
            $data = OfficeDueTransactionData::from($request)->all();
        
            $project = $this->projectRep->findById($data['project_id']);
            if (!$project) {
                return error(__('Project not found')); 
            }
        
            if (!$project->officeDue) {
                return error(__('Office due record not found')); 
            }
    
            $dueProfits = $project->officeDue->total_profits - $project->officeDueTransactions()->sum('amount');
    
            if ($data['amount'] > $dueProfits) {
                return error(__('The amount cannot be greater than the due profits.') , 422);
            }
    
            $receivedProfits = $project->officeDueTransactions()->sum('amount') + $data['amount'];
            $dueProfits -= $data['amount'];
    
            $data['withdraw_by'] = auth()->user()->name; 
            $data['remaining'] = $dueProfits;
            $this->OfficeDueTransactionRepository->create($data);
    
            $project->officeDue()->update([
                'recieved_profits' => $receivedProfits,
                'due_profits' => $dueProfits
            ]);
    
            DB::commit(); 
    
            return success(__('Created Successfully'));
    
        } catch (\Exception $e) {
            DB::rollBack(); 
            logger()->error('OfficeDueTransaction Error: ' . $e->getMessage()); 
            return error(__('An error occurred while processing the transaction. Please try again.'));
        }
    }
    
    
    
}
