<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateOfficeRequest;
use App\Http\Requests\Admin\UpdateOfficePasswordRequest;
use App\Http\Requests\Admin\UpdateOfficeRequest;
use App\Http\Resources\Admin\OfficeResource;
use App\Http\Resources\BaseCollection;
use App\Services\OfficeService;

class OfficeController extends Controller
{
    public function __construct(public OfficeService $officeService) {}

    public function index()
    {
        $offices = $this->officeService->get();

        return success(new BaseCollection($offices, OfficeResource::class));
    }

    public function store(CreateOfficeRequest $request)
    {
        return $this->officeService->create($request->validated());
    }

    public function show(string $id)
    {
        $office = $this->officeService->getById($id);

        return success(new OfficeResource($office));
    }

    public function update(UpdateOfficeRequest $request, string $id)
    {
        return $this->officeService->update($request->validated(), $id);
    }

    public function destroy(string $id)
    {
        $this->officeService->delete($id);

        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $this->officeService->restore($id);

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $this->officeService->forceDelete($id);

        return success(__('Deleted Successfully'));
    }

    public function updatePassword(UpdateOfficePasswordRequest $request, string $id)
    {
        $this->officeService->updatePassword($request->password, $id);

        return success(__('Updated Successfully'));
    }

    public function deleteDomain(string $id)
    {
        return $this->officeService->deleteDomain($id);
    }
}
