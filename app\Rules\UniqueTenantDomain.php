<?php

namespace App\Rules;

use App\Models\Office;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Stancl\Tenancy\Database\Models\Domain;

class UniqueTenantDomain implements ValidationRule
{
    public function __construct(protected string $officeId)
    {
        //
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $tenant = Office::find($this->officeId)->tenant->id;

        $exists = Domain::where('domain', $value)
            ->where('tenant_id', '!=', $tenant)
            ->exists();

        if ($exists) {
            $fail(__('The domain :value is already taken', ['value' => $value]));
        }
    }
}
