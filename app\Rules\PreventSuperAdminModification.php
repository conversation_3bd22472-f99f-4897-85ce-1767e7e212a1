<?php

namespace App\Rules;

use Closure;
use App\Models\Tenant\User;
use Illuminate\Contracts\Validation\ValidationRule;

class PreventSuperAdminModification implements ValidationRule
{
    protected $userId;

    public function __construct($userId = null)
    {
        $this->userId = $userId;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = User::withoutGlobalScope('excludeSuperAdmin')
            ->where('id', $this->userId)
            ->first();

        if ($user && $user->type === 'admin' && $user->role?->name === 'Super Admin') {
            $fail(__('You cannot modify a Super Admin user.'));
        }
    }
}