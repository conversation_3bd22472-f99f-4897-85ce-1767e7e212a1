<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class PaymentCertificate extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'title',
        'date',
        'contractor_id',
        'item_id',
        'unit_id',
        'qty',
        'price',
        'total',
        'created_by',
        'permanent_deleted',
        'project_id',
        'paid_amount',
        'notes'
    ];

    /**
     * Get the contractor associated with the payment certificate.
     */
    public function contractor()
    {
        return $this->belongsTo(Partner::class, 'contractor_id')->withTrashed();
    }

    public function getCreatorAttribute()
    {
        return $this->transactions()->where('is_creator', 1)->first();
    }

    /**
     * Get the item associated with the payment certificate.
     */
    public function item()
    {
        return $this->belongsTo(Item::class, 'item_id');
    }

    /**
     * Get the unit associated with the payment certificate.
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function getImageAttribute()
    {
        return $this->getFirstMedia('payment-certificates');
    }

    public function transactions(): MorphMany
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function certificationItems()
    {
        return $this->hasMany(CertificationItem::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}

