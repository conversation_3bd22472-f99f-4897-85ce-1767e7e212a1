<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['residential', 'commercial', 'industrial', 'infrastructure'])->default('residential');
            $table->enum('size', ['small', 'medium', 'large'])->default('small');
            $table->integer('area');

            $table->foreignId('payment_method_id')->constrained('payment_methods')->cascadeOnDelete();
            $table->foreignId('package_id')->nullable()->constrained('packages')->cascadeOnDelete();
            $table->integer('office_ratio')->default(0);
            $table->integer('site_engineer_ratio')->default(0);
            $table->double('amount_per_meter')->default(0);
            $table->double('total')->default(0);
            $table->double('withdraw_limit')->default(0);

            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->enum('status', ['new', 'in_progress', 'completed', 'deferred'])->default('new');
            $table->double('balance')->default(0);
            $table->longText('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
