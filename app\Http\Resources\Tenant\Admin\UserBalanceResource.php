<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserBalanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => [
                'id' => $this->userable->id,
                'name' => $this->userable->name,
                'type' => $this->userable->type ?? null,
                'balance' => $this->userable->balance ?? 0, 
                'deleted_at' => $this->userable->deleted_at ?? null,
            ],
            'created_at' => $this->created_at,
        ];
    }
}
