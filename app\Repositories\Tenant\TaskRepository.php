<?php

namespace App\Repositories\Tenant;

use Carbon\Carbon;
use App\Models\Tenant\Task;
use App\Models\Tenant\Project;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TaskRepository
{
    public function __construct(public Task $model) {}

    public function index(array $filters = [])
    {
        $projectId = $filters['project'] ?? Project::orderBy('id')->value('id');

        return $this->model
            ->with(['project', 'user'])
            ->where('project_id', $projectId)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                return $query->where('title', 'like', "%{$filters['search']}%");
            })
            ->when(isset($filters['user']), function ($query) use ($filters) {
                return $query->where('user_id', $filters['user']);
            })
            ->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $dates = [
                    Carbon::parse($filters['date_from'])->startOfDay(),
                    Carbon::parse($filters['date_to'] ?? now())->endOfDay(),
                ];
                return $query->whereBetween('date_from', $dates);
            })
            ->orderByDesc('id')
            ->get()
            ->groupBy('status');
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->where('project_id', request()->project)->where('permenant_delete', false)->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update($task, array $data)
    {
        return $task->update($data);
    }

    public function findById($id)
    {
        $task = $this->model->find($id);

        if (!$task) {
            throw new NotFoundHttpException(__('Task not found'));
        }

        return $task->load(['activities', 'media']);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function delete($task)
    {
        return $task->delete();
    }

    public function restore($task)
    {
        return $task->restore();
    }

    public function forceDelete($task)
    {
        return $task->update([
            'permenant_delete' => 1,
        ]);
    }

    public function getUserTasks(array $filters = [])
    {
        return $this->model
            ->with(['project', 'user'])
            ->where('user_id', auth()->id())
            ->when(isset($filters['project']), function ($query) use ($filters) {
                return $query->where('project_id', $filters['project']);
            })
            ->when(isset($filters['date']), function ($query) use ($filters) {
                $date = Carbon::parse($filters['date'])->format('Y-m-d');
                return $query->whereDate('date_from', $date);
            })
            ->orderByDesc('id')
            ->get()
            ->groupBy('status');
    }

    public function updateStatus($task, string $status)
    {
        return $task->update(['status' => $status]);
    }
}
