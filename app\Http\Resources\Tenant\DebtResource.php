<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DebtResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'partner' => $this->type === 'purchase' || $this->type === 'certification' ? $this->creditor->name : $this->debtor->name,
            'total_invoices' => $this->total_invoices,
            'total_payments' => $this->total_payments,
            'total_debt' => $this->total_debt,
            'is_deleted' => in_array($this->type, ['purchase', 'certification'])
                ? optional($this->creditor)->trashed() ?? false
                : optional($this->debtor)->trashed() ?? false,
        ];
    }
}
