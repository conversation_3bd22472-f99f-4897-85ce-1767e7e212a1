<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::guard('admin')->check()) {
            $route = explode('.', Route::currentRouteName());
            $group = $route[count($route) - 2] ?? null;
            $permission = $route[count($route) - 1] ?? null;

            if (! $group || ! $permission) {
                return error('Unauthorized', 403);
            }

            $user = $request->user();

            // If user is a Super Admin, they have unrestricted access
            if ($this->isSuperAdmin($user)) {
                // Super Admin bypasses permission check
                return $next($request);
            }

            // Check if user has the required permission
            if ($user->status === 'Disabled' || ! $this->hasPermission($user, $group, $permission)) {
                return error('Unauthorized', 403);
            }
        }

        return $next($request);
    }

    protected function isSuperAdmin($user): bool
    {
        return $user->role && $user->role->name === 'Super Admin';
    }

    protected function hasPermission($user, $group, $permission): bool
    {
        return $user->permissions()->where(function ($query) use ($group, $permission) {
            $query->where('group', $group)
                ->where('name', $permission);
        })->exists();
    }
}
