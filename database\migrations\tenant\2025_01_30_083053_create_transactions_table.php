<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->morphs('transactionable'); // Polymorphic relationship (transaction_type, transaction_id)
            $table->morphs('userable'); // Polymorphic relationship for the user (user_type, user_id)
            $table->decimal('amount', 15, 2); // Amount column
            $table->boolean('is_creator')->default(false); // Boolean column
            $table->enum('type', ['add', 'update', 'transfer', 'pay'])->default('Add');
            $table->text('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
