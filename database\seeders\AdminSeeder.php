<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'admin',
        ]);

        Admin::create([
            'name' => 'Geexar',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'password' => 'P@ssw0rd123123!',
            'status' => 'Active',
            'role_id' => $superAdminRole->id,
        ]);
    }
}
