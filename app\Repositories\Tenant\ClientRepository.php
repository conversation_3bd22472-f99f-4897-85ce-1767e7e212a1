<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Client;

class ClientRepository
{
    public function __construct(public Client $model) {}

    public function getPaginated(int $limit = 10)
    {
        return $this->model

            ->when(request('search'), function ($query) {
                return $query->where('name', 'like', '%'.request('search').'%');
            })

            ->when(request('status'), function ($query) {
                return $query->where('status', request('status'));
            })
            ->when(request('project_name'), function ($query) {
                return $query->whereHas('projects', function ($subQuery) {
                    $subQuery->where('name', 'like', '%'.request('project_name').'%');
                });
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function findOnlyTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(Client $client, array $data)
    {

        $client->update($data);
    }

    public function delete(Client $client)
    {
        $client->delete();
    }

    public function forceDelete(Client $client)
    {
        $client->forceDelete();
    }

    public function getArchived(int $limit = 10, array $filters = [])
    {
        return $this->model
            ->onlyTrashed()

            ->orderByDesc('id')->paginate($limit);
    }

    public function restore(Client $client)
    {
        $client->restore();
    }

    public function getAll(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }
}
