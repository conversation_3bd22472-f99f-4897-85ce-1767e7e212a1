<?php

namespace App\Http\Controllers\Tenant;

use App\Events\MessageNotificationEvent;
use App\Events\MessageSentEvent;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\StoreMessageRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\MessageResource;
use App\Services\Tenant\ChatService;

class ChatController extends Controller
{
    public function __construct(public ChatService $chatService) {}

    public function index(string $roomId)
    {
        $messages = $this->chatService->get($roomId);

        return success(new BaseCollection($messages, MessageResource::class));
    }

    public function store(StoreMessageRequest $request)
    {
        $message = $this->chatService->create($request->validated());

        // Broadcast the message event (to chat channel)
        event(new MessageSentEvent($message));

        // Send a real-time notification to users
        event(new MessageNotificationEvent($message));

        return success(new MessageResource($message));
    }

    public function read(string $roomId)
    {
        return success($this->chatService->markAsRead($roomId));
    }
}
