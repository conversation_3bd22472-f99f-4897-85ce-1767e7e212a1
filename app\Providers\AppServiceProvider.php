<?php

namespace App\Providers;

use App\Models\Admin;
use App\Models\Office;
use App\Models\Plan;
use App\Models\Role;
use App\Models\Subscription;

use App\Observers\HistoryObserver;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->environment('local') && class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Role::addGlobalScope('excludeSuperAdmin', function ($query) {
            $query->where('name', '!=', 'Super Admin');
        });

        Gate::before(function ($user, $ability) {
            return $user->role && $user->role->name === 'Super Admin' ? true : null;
        });

        Office::observe(HistoryObserver::class);
        Admin::observe(HistoryObserver::class);
        Plan::observe(HistoryObserver::class);
        Subscription::observe(HistoryObserver::class);
    }
}
