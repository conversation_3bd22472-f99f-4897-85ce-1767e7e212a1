<?php

namespace App\Repositories;

use App\Models\GroupNotification;
use Carbon\Carbon;

class GroupNotificationRepository
{
    public function __construct(public GroupNotification $model) {}

    public function getPaginated(array $filters, int $limit = 10)
    {
        return $this->model->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where('title', 'like', "%{$filters['search']}%")
                ->orWhere('sender_name', 'like', "%{$filters['search']}%");
        })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
            $dates = [
                Carbon::parse($filters['date_from'])->startOfDay(),
                Carbon::parse($filters['date_to'] ?? null)->endOfDay(),
            ];

            return $query->whereBetween('created_at', $dates);
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $request)
    {
        return $this->model->create($request);
    }
}
