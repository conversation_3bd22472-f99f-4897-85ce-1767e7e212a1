<?php

namespace App\Providers;

use App\Models\Tenant\Project;
use App\Models\Tenant\ProjectAttachment;
use App\Models\Tenant\User;
use App\Models\Tenant\Partner;
use App\Models\Tenant\Purchase;
use App\Models\Tenant\Refund;
use App\Models\Tenant\ContractorPayment;
use App\Models\Tenant\PaymentCertificate;
use App\Models\Tenant\Supply;
use App\Models\Tenant\Task;
use App\Observers\HistoryObserver;
use Illuminate\Support\ServiceProvider;
use Stancl\Tenancy\Events\TenancyInitialized;

class TenantServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers when tenancy is initialized
        $this->app['events']->listen(TenancyInitialized::class, function () {
            $this->registerTenantObservers();
        });
    }

    /**
     * Register observers for tenant models
     */
    protected function registerTenantObservers(): void
    {
        // Register HistoryObserver for tenant models
        Project::observe(HistoryObserver::class);
        ProjectAttachment::observe(HistoryObserver::class);
        User::observe(HistoryObserver::class);
        Partner::observe(HistoryObserver::class);
        Purchase::observe(HistoryObserver::class);
        Refund::observe(HistoryObserver::class);
        ContractorPayment::observe(HistoryObserver::class);
        PaymentCertificate::observe(HistoryObserver::class);
        Supply::observe(HistoryObserver::class);
        Task::observe(HistoryObserver::class);
    }
}
