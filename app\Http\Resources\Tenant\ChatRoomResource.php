<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChatRoomResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $lastMessage = $this->messages->last();
        return [
            'id' => $this->id,
            'name' => $this->name,
            'image' => new MediaResource($this->project->image),
            'last_message' => $lastMessage ? [
                'sender_name' => $lastMessage->sender->name,
                'body' => $lastMessage->type == 'text' ? $lastMessage->body : tenant_asset($lastMessage->image),
                'created_at' => $lastMessage->created_at->diffForHumans(),
            ] : null,
        ];
    }
}
