<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateTaskHistoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'task_id' => ['required', 'exists:tasks,id'],
            'message' => ['required', 'string', 'min:3', 'max:255'],
            'images' => ['nullable', 'array', 'min:1', 'max:10'],
            'images.*' => ['nullable', 'mimes:doc,docx,pdf,xlsx,jpg,jpeg,png,gif,bmp,svg,webp', 'max:5120'],

        ];
    }

    public function messages(): array
    {
        return [
            'task_id.exists' => 'The selected task does not exist.',
            'message.required' => 'A comment message is required.',
            'message.min' => 'The comment must be at least 3 characters.',
            'message.max' => 'The comment cannot exceed 255 characters.',
        ];
    }
}