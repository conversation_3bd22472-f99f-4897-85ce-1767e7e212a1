<?php

namespace App\DTO\Tenant;

use Spa<PERSON>\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;

class TaskHistoryData extends Data
{
    public function __construct(
        #[Required]
        #[Exists('tasks', 'id')]
        public int $task_id,

        #[Required]
        #[Min(3)]
        #[Max(255)]
        public string $message,
    ) {}
}
