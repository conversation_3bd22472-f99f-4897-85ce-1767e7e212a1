<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Stancl\Tenancy\Contracts\TenantCouldNotBeIdentifiedException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        using: function () {
            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware(['api', 'localization'])
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware(['api', 'localization'])
                ->prefix('api/admin')
                ->group(base_path('routes/admin.php'));
        },
        channels: __DIR__ . '/../routes/channels.php',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'permission' => \App\Http\Middleware\CheckPermission::class,
            'localization' => \App\Http\Middleware\Localization::class,
            'can_access_chat_room' => \App\Http\Middleware\CanAccessChatRoom::class,
            'can_create_user' => \App\Http\Middleware\CanCreateUser::class,
            'can_create_admin' => \App\Http\Middleware\CanCreateAdmin::class,
            'can_create_project' => \App\Http\Middleware\CanCreateProject::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(function (NotFoundHttpException $e, $request) {
            return error($e->getMessage() ? $e->getMessage() : __('resource not found'), 404);
        });
        $exceptions->renderable(function (UnprocessableEntityHttpException $e, $request) {
            return error($e->getMessage() ? $e->getMessage() : __('resource not found'), $e->getStatusCode());
        });
        $exceptions->renderable(function (TenantCouldNotBeIdentifiedException $e, $request) {
            return error(__('We could not find the tenant for this domain. Please check the link or contact support.'), 404);
        });
    })->create();
