<?php

namespace App\Http\Requests\Admin;

use App\Rules\IgnoreSuperAdminRole;
use App\Rules\PasswordNotContainPersonalInfo;
use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class CreateAdminRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name' => 'required|string|min:3|max:30',
            'email' => 'required|email|unique:admins,email',
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('admins')],
            'address' => 'sometimes|string|min:3|max:100',
            'role_id' => ['required', 'exists:roles,id', new IgnoreSuperAdminRole],
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
            'password' => [
                'required',
                'string',
                Password::min(8)->mixedCase()->numbers()->symbols(),
                Rule::when(request('password_type') === 'Manual', 'confirmed'),
                new PasswordNotContainPersonalInfo([
                    request('email'),
                    request('phone'),
                    request('name'),
                ]),
            ],
        ];
    }
}
