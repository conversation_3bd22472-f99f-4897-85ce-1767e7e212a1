<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
</head>

<body>

    <p>firebase</p>

    <script type="module">
        import {
            initializeApp
        } from 'https://www.gstatic.com/firebasejs/10.14.0/firebase-app.js';
        import {
            getMessaging,
            getToken,
            onMessage
        } from 'https://www.gstatic.com/firebasejs/10.14.0/firebase-messaging.js';

        const firebaseConfig = {
            apiKey: "AIzaSyBSmzkPYClvTO8GhKTKfUs7tb6PuBtRK34",
            authDomain: "engsaas-de75d.firebaseapp.com",
            projectId: "engsaas-de75d",
            storageBucket: "engsaas-de75d.firebasestorage.app",
            messagingSenderId: "547001759178",
            appId: "1:547001759178:web:4612bedc0dd96bd6668b33"
        };

        // Initialize Core Firebase
        const app = initializeApp(firebaseConfig);

        // Initialize Firebase Messaging
        const messaging = getMessaging(app);

        // Request Notification Permission
        Notification.requestPermission().then((permission) => {
            if (permission === 'granted') {
                console.log('Notification permission granted.');
            } else {
                console.log('Unable to get permission to notify.');
            }
        });

        // Get Registeration Token
        getToken(messaging, {
            vapidKey: 'BGhzg-qcJ9pr0Et6t6nl4E5ia2WrrE3RbItTbX97uY5d-2lOajCX5qkYdngyCkSj9fRpa-bmCj_KHCRP6fx7YPM'
        }).then((currentToken) => {
            if (currentToken) {
                console.log('FCM Token:', currentToken);
            } else {
                console.log('No registration token available. Request permission to generate one.');
            }
        }).catch((err) => {
            console.log('An error occurred while retrieving token.', err);
        });


        // Handle Incoming messages
        onMessage(messaging, (payload) => {
            console.log('Message received. ', payload);
        });
    </script>
</body>

</html>
