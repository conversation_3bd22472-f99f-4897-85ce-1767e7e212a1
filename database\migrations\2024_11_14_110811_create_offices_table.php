<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offices', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->string('phone')->unique();
            $table->string('email')->unique();
            $table->string('password');
            $table->string('address')->nullable();
            $table->string('location')->nullable();
            $table->time('work_hour_from')->nullable();
            $table->time('work_hour_to')->nullable();
            $table->string('work_day_from')->nullable();
            $table->string('work_day_to')->nullable();
            $table->longText('description')->nullable();
            $table->enum('status', ['Active', 'Disabled'])->default('Active');
            $table->string('apperance_color')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offices');
    }
};
