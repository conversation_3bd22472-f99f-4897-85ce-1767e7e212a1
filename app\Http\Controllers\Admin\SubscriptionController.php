<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSubscriptionRequest;
use App\Http\Resources\Admin\SubscriptionResource;
use App\Http\Resources\BaseCollection;
use App\Services\SubscriptionService;

class SubscriptionController extends Controller
{
    public function __construct(public SubscriptionService $subscriptionService) {}

    public function index()
    {
        $subscriptions = $this->subscriptionService->get();

        return success(new BaseCollection($subscriptions, SubscriptionResource::class));
    }

    public function show(string $id)
    {
        $subscription = $this->subscriptionService->getById($id);

        return success(new SubscriptionResource($subscription));
    }

    public function update(UpdateSubscriptionRequest $request, string $id)
    {
        return $this->subscriptionService->update($request->validated(), $id);
    }

    public function deactivate(string $id)
    {
        $this->subscriptionService->deactivate($id);

        return success(__('Deactivated Successfully'));
    }
}
