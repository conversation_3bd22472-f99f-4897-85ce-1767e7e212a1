<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refunds', function (Blueprint $table) {
            $table->id();
            $table->string('number')->unique();
            $table->foreignId('project_id')->constrained('projects')->cascadeOnDelete();
            $table->enum('type', ['invoice', 'detailed']);

            $table->foreignId('purchase_id')->nullable()->constrained('purchases')->cascadeOnDelete();
            $table->string('name');
            $table->date('date');
            $table->foreignId('supplier_id')->nullable()->constrained('partners')->cascadeOnDelete();
            $table->double('total_before_discount')->default(0);
            $table->enum('discount_type', ['value', 'percentage'])->nullable();
            $table->integer('discount_value')->default(0);
            $table->double('total_after_discount')->default(0);
            $table->enum('status', ['paid', 'partial', 'unpaid'])->default('unpaid');
            $table->double('paid_amount')->default(0);
            $table->double('remaining_amount')->default(0);
            $table->text('notes')->nullable();
            $table->string('created_by')->nullable();
            $table->boolean('permanent_deleted')->default(false);
            $table->softDeletes();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refunds');
    }
};
