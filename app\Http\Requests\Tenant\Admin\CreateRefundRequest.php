<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateRefundRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Set to true if any user can access this route
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'project_id' => 'required|exists:projects,id',
            'type' => 'required|in:invoice,detailed',
            'purchase_id' => ['required_if:type,invoice', Rule::exists('purchases', 'id')->where('type', 'detailed')],
            'name' => 'required|string|min:3|max:30',
            'date' => 'required|date',
            'partner_id' => [
                'required_if:type,detailed',
                Rule::exists('partners', 'id')
                    ->where('type', 'supplier')
                    ->where('permanent_deleted', false)
                    ->whereNull('deleted_at')
            ],
            'discount_type' => 'nullable|in:value,percentage',
            'discount_value' => [
                'required_with:discount_type',
                'numeric',
                'min:1',
                Rule::when($this->discount_type === 'percentage', 'max:100'),
                Rule::when($this->discount_type !== 'percentage', [
                    'regex:/^\d{1,10}(\.\d{1,2})?$/',
                ]),
            ],
            'status' => 'sometimes|in:paid,partial,unpaid',
            'paid_amount' => 'required_if:status,partial|numeric|min:1|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'notes' => 'nullable|string',
            'created_by' => 'nullable|exists:users,id',
            'item_ids' => 'required_if:type,invoice|array',
            'item_ids.*' => 'exists:purchase_items,id',
            'qty_refunded' => 'required_if:type,invoice|array',
            'qty_refunded.*' => 'integer|min:1|min_digits:1|max_digits:10',
            'categories' => ['required_if:type,detailed', 'array'],
            'categories.*.id' => ['required_if:type,detailed', 'exists:items,id'],
            'categories.*.items' => ['required_if:type,detailed', 'array', 'min:1'],
            'categories.*.items.*.name' => ['required_if:type,detailed', 'string', 'min:3', 'max:30'],
            'categories.*.items.*.unit_id' => ['required_if:type,detailed', 'exists:units,id'],
            'categories.*.items.*.price' => ['required_if:type,detailed', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'min:1'],
            'categories.*.items.*.qty_refunded' => ['required_if:type,detailed', 'integer', 'min:1', 'min_digits:1', 'max_digits:10'],
            'images' => ['nullable', 'array', 'min:1', 'max:10'],
            'images.*' => ['nullable', 'mimes:png,jpg,jpeg', 'max:5120'],
        ];
    }
}
