<?php

namespace App\DTO\Tenant;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class ContractorPaymentData extends Data
{
    public function __construct(
        public string $date,
        public string $name,
        public int $contractor_id,
        public int|Optional $project_id,
        public int $item_id,
        public float $amount,
        public ?string $notes,
        public ?string $created_by,
        public ?string $archive_by
    ) {}
}
