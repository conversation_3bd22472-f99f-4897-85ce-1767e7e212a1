<?php

namespace App\Repositories\Tenant;

use <PERSON>tie\Permission\Models\Role;

class RoleRepository
{
    public function __construct(public Role $model) {}

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function getPaginated($filters = [], int $limit = 10)
    {
        return $this->model->where(function ($query) use ($filters) {
            if (isset($filters['search'])) {
                $query->where('name', 'like', '%' . $filters['search'] . '%');
            }
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function getWhere(array $data)
    {
        return $this->model->where($data)->get();
    }

    public function findRoleByName($name)
    {
        return $this->model->where('name', $name)->first();
    }
}
