<?php

namespace App\Models\Tenant;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;

class Task extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'project_id',
        'user_id',
        'title',
        'status',
        'priority',
        'description',
        'date_from',
        'date_to',
        'permenant_delete'
    ];

    // Relationships
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }



    public function getImageAttribute()
    {
        return $this->getFirstMedia('tasks');
    }

    public function activities()
    {
        return $this->hasMany(TaskHistory::class);
    }
}
