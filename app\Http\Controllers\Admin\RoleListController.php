<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\RoleResource;
use App\Services\RoleService;

class RoleListController extends Controller
{
    public function __construct(public RoleService $roleService) {}

    public function __invoke()
    {
        $roles = $this->roleService->getActiveRoles();

        return success(RoleResource::collection($roles));
    }
}
