<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\UpdateSettingRequest;
use App\Http\Resources\Tenant\Admin\SettingResource;
use App\Services\Tenant\SettingService;

class GeneralSettingController extends Controller
{
    public function __construct(public SettingService $settingService) {}

    public function index($type)
    {
        $settings = $this->settingService->all($type);

        return success(SettingResource::collection($settings));
    }

    public function update(UpdateSettingRequest $request)
    {
        return $this->settingService->update($request->all());
    }
}
