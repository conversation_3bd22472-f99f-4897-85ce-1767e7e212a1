<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSupplyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => ['required', 'date'],
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'partner_id' => ['required', 'exists:partners,id'],
            'categories' => ['required', 'array'],
            'categories.*.id' => ['required', 'exists:items,id'],
            'categories.*.items' => ['required', 'array', 'min:1'],
            'categories.*.items.*.name' => ['required', 'string', 'min:3', 'max:30'],
            'categories.*.items.*.unit_id' => ['required', 'exists:units,id'],
            'categories.*.items.*.price' => ['sometimes', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/'],
            'categories.*.items.*.qty' => ['required', 'integer', 'min:1', 'min_digits:1', 'max_digits:10', 'regex:/^\d{1,10}(\.\d{1,2})?$/'],
            'notes' => ['sometimes', 'string', 'min:3', 'max:100'],
        ];
    }

    public function messages()
    {
        return [
            'categories.*.id.required' => __('validation.required'),
            'categories.*.items.required' => __('validation.required'),
            'categories.*.items.*.qty.required' => __('validation.required'),
            'categories.*.items.*.name.required' => __('validation.required'),
            'categories.*.items.*.unit_id.required' => __('validation.required'),
        ];
    }
}
