<?php

namespace App\Services\Tenant;

use Exception;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\UserBalanceData;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\UserBalanceRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UserBalanceService
{
    public function __construct(protected UserBalanceRepository $UserBalanceRepository, protected SettingRepository $settingRepository)
    {
        //
    }

    public function index()
    {
        return $this->UserBalanceRepository->all();
    }

    public function changeBalance(array $request, $id)
    {
        DB::beginTransaction();

        try {
            $data = UserBalanceData::from($request)->all();
            $record = $this->UserBalanceRepository->findById($id);

            if (!$record) {
                throw new NotFoundHttpException(__('Record not found.'));
            }

            $user = $record->userable;
            $authUser = auth()->user();
            $amount = $data['amount'];
            $type = $data['type'];

            $balanceCheckUserId = ($type === 'withdraw') ? $user->id : $authUser->id;
            $balanceCheck = $this->settingRepository->balanceCheck($amount, $balanceCheckUserId);

            if (!$balanceCheck['status']) {
                throw new \InvalidArgumentException($balanceCheck['message']);
            }

            if ($type === 'add') {
                $user->balance += $amount;
            } elseif ($type === 'withdraw') {
                if ($user->balance < $amount) {
                    throw new \InvalidArgumentException(__('Insufficient balance.'));
                }
                $user->balance -= $amount;
            } else {
                throw new \InvalidArgumentException(__('Invalid balance change type.'));
            }

            $user->save();
            DB::commit();

            return success(__('Updated Successfully'));
        } catch (NotFoundHttpException $e) {
            DB::rollBack();
            throw $e;
        } catch (\InvalidArgumentException $e) {
            DB::rollBack();
            return error($e->getMessage(), 422);
        } catch (Exception $e) {
            DB::rollBack();
            logger()->error('Balance update error: ' . $e->getMessage());
            return error(__('Balance update failed. Please check your input.'), 422);
        }
    }
}
