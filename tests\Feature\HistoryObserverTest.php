<?php

namespace Tests\Feature;

use App\Observers\HistoryObserver;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HistoryObserverTest extends TestCase
{
    use RefreshDatabase;

    public function test_history_observer_translation_logic()
    {
        // Test the translation logic directly
        $modelBaseName = 'Office';

        $modelNameEn = __("model.$modelBaseName", [], 'en');
        $modelNameAr = __("model.$modelBaseName", [], 'ar');

        $this->assertEquals('Office', $modelNameEn);
        $this->assertEquals('المكاتب', $modelNameAr);

        // Test fallback for non-existent model
        $nonExistentModel = 'NonExistentModel';
        $fallbackEn = __("model.$nonExistentModel", [], 'en');
        $fallbackAr = __("model.$nonExistentModel", [], 'ar');

        $this->assertEquals("model.$nonExistentModel", $fallbackEn);
        $this->assertEquals("model.$nonExistentModel", $fallbackAr);
    }

    public function test_translation_keys_exist()
    {
        // Test that our translation keys exist
        $this->assertEquals('Office', __('model.Office', [], 'en'));
        $this->assertEquals('المكاتب', __('model.Office', [], 'ar'));

        $this->assertEquals('Plan', __('model.Plan', [], 'en'));
        $this->assertEquals('الخطط', __('model.Plan', [], 'ar'));

        $this->assertEquals('User', __('model.User', [], 'en'));
        $this->assertEquals('المستخدمين', __('model.User', [], 'ar'));
    }
}
