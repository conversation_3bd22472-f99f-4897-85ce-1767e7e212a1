<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\PermissionListResource;
use App\Repositories\PermissionRepository;

class PermissionController extends Controller
{
    public function __construct(public PermissionRepository $permissionRepo) {}

    public function __invoke()
    {
        $permissions = $this->permissionRepo->getGroupedPermissions();

        $permissions = $permissions->map(function ($group) {
            return PermissionListResource::collection($group);
        });

        return success($permissions);
    }
}
