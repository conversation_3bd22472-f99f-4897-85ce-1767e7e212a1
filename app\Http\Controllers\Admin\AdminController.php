<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateAdminRequest;
use App\Http\Requests\Admin\UpdateAdminPasswordRequest;
use App\Http\Requests\Admin\UpdateAdminRequest;
use App\Http\Resources\AdminResource;
use App\Http\Resources\BaseCollection;
use App\Services\AdminService;

class AdminController extends Controller
{
    public function __construct(public AdminService $adminService) {}

    public function index()
    {
        $admins = $this->adminService->get();

        return success(new BaseCollection($admins, AdminResource::class));
    }

    public function store(CreateAdminRequest $request)
    {
        return $this->adminService->create($request->validated());
    }

    public function show(string $id)
    {
        $admin = $this->adminService->getById($id);

        return success(new AdminResource($admin));
    }

    public function update(UpdateAdminRequest $request, string $id)
    {
        return $this->adminService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        $this->adminService->delete($id);

        return success(__('Deleted Successfully'));
    }

    public function updatePassword(UpdateAdminPasswordRequest $request, string $id)
    {
        $this->adminService->updatePassword($id, $request->password);

        return success(__('Updated Successfully'));
    }
}
