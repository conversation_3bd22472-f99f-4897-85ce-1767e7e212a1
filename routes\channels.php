<?php

use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('chat-{tenantId}-{chatId}', function ($user, $tenantId, $chatId) {
    tenancy()->initialize($tenantId);
    $isAuthorized = $user->rooms->contains($chatId);
    tenancy()->end();

    return $isAuthorized;
}, ['guards' => ['web', 'sanctum']]);

Broadcast::channel('user-{tenantId}-{userId}', function ($user, $tenantId, $userId) {
    tenancy()->initialize($tenantId);
    $isAuthorized = (int) $user->id === (int) $userId;
    tenancy()->end();

    return $isAuthorized;
}, ['guards' => ['web', 'sanctum']]);
