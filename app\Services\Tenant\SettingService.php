<?php

namespace App\Services\Tenant;

use App\Repositories\Tenant\SettingRepository;

class SettingService
{
    public function __construct(public SettingRepository $repository) {}

    public function all($type)
    {
        return $this->repository->getAll($type);
    }

    public function update(array $request)
    {
        $this->repository->update($request);

        return success(__('Updated Successfully'));
    }
}
