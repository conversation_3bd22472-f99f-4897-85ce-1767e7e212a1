<?php

namespace App\DTO\Tenant;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class PurchaseData extends Data
{
    public function __construct(
        public int $project_id,
        public string|Optional $type,
        public string $name,
        public string|Optional $number,
        public string $date,
        public int $partner_id,
        public string $status,
        public float $total_before_discount,
        public float $total_after_discount,
        public float|Optional $paid_amount,
        public float|Optional $remaining_amount,
        public ?string $discount_type,
        public float $discount_amount,
        public ?string $notes,
        public string|Optional $created_by
    ) {}
}
