<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class PurchaseItem extends Model
{
    protected $fillable = [
        'invoice_category_id',
        'name',
        'unit_id',
        'qty',
        'price',
        'total',
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
    
    public function invoiceCategory()
    {
        return $this->belongsTo(InvoiceCategory::class);
    }
    
    public function purchase()
    {
        return $this->belongsToThrough(
            Purchase::class,
            InvoiceCategory::class,
            'id', // Local key on `invoice_categories` table
            'invoiceable_id', // Foreign key on `invoice_categories` table
            'invoice_category_id', // Foreign key on `purchase_items` table
            'id' // Local key on `purchases` table
        )->where('invoiceable_type', Purchase::class);
    }
}
