<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_en',
        'name_ar',
        'status',

    ];

    public function getNameAttribute()
    {
        return $this->{'name_'.app()->getLocale()};
    }

    /**
     * Relationship: An Item can belong to many partners.
     */
    public function partners()
    {
        return $this->belongsToMany(Partner::class, 'partner_items', 'item_id', 'partner_id')
            ->withTimestamps();
    }
}
