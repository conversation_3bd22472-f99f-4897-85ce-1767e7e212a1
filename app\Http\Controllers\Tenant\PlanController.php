<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\PlanService;
use App\Http\Resources\Tenant\PlanResource;

class PlanController extends Controller
{
    public function __construct(public PlanService $planService) {}

    public function ddl()
    {
        $plans = $this->planService->ddl();
        return success(PlanResource::collection($plans));
    }
}
