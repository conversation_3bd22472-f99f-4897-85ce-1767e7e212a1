<?php

namespace Database\Seeders\tenant;

use App\Models\Tenant\Task;
use App\Models\Tenant\User;
use App\Models\Tenant\Project;
use Illuminate\Database\Seeder;

class TaskSeeder extends Seeder
{
    public function run(): void
    {
        $projects = Project::all();
        $users = User::all();

        foreach ($projects as $project) {
            Task::insert([
                [
                    'project_id' => $project->id,
                    'user_id' => $users->random()->id,
                    'title' => 'Initial Project Setup',
                    'status' => 'new',
                    'priority' => 'high',
                    'description' => 'Set up initial project requirements and documentation',
                    'date_from' => now(),
                    'date_to' => now()->addDays(5),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'project_id' => $project->id,
                    'user_id' => $users->random()->id,
                    'title' => 'Design Review',
                    'status' => 'in_progress',
                    'priority' => 'medium',
                    'description' => 'Review and approve architectural designs',
                    'date_from' => now()->addDays(2),
                    'date_to' => now()->addDays(7),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'project_id' => $project->id,
                    'user_id' => $users->random()->id,
                    'title' => 'Material Procurement',
                    'status' => 'finished',
                    'priority' => 'high',
                    'description' => 'Order and verify delivery of construction materials',
                    'date_from' => now()->subDays(5),
                    'date_to' => now()->subDays(1),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'project_id' => $project->id,
                    'user_id' => $users->random()->id,
                    'title' => 'Site Inspection',
                    'status' => 'in_progress',
                    'priority' => 'medium',
                    'description' => 'Conduct weekly site inspection and report',
                    'date_from' => now(),
                    'date_to' => now()->addDays(1),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'project_id' => $project->id,
                    'user_id' => $users->random()->id,
                    'title' => 'Safety Compliance Check',
                    'status' => 'new',
                    'priority' => 'high',
                    'description' => 'Verify all safety measures are in place',
                    'date_from' => now()->addDays(1),
                    'date_to' => now()->addDays(3),
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);
        }
    }
}