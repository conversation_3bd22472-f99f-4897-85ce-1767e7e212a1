<?php

namespace App\DTO\Tenant;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Optional;

class UserData extends Data
{
    public function __construct(
        public string|Optional $name,
        public string|Optional $email,
        public string|Optional $phone,
        public string|Optional $password,
        public ?string $address,
        public string|Optional $status,
        public string|Optional $type,
        public ?string $national_id,
        public ?string $details,
        public bool|Optional $can_access_all_projects,
        public array|Optional $project_ids, // Add project_ids as an array
        public int|Optional $role_id
    ) {}
}
