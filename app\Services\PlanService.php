<?php

namespace App\Services;

use App\DTO\PlanData;
use App\Repositories\PlanRepository;
use App\Repositories\SubscriptionRepository;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PlanService
{
    public function __construct(
        public PlanRepository $planRepo,
        public SubscriptionRepository $subscriptionRepo
    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'status']);

        if (request()->type === 'archive') {
            return $this->planRepo->getTrashedWithPaginate();
        } else {
            return $this->planRepo->getPaginated($filterable, 12);
        }
    }

    public function getById($id)
    {
        $plan = $this->planRepo->findById($id);

        if (! $plan) {
            throw new NotFoundHttpException;
        }

        return $plan;
    }

    public function create(array $request)
    {
        $data = PlanData::from($request)->all();

        $plan = $this->planRepo->create($data);

        if (request()->hasFile('image')) {

            $plan->addMedia(request()->image)
                ->toMediaCollection('plans');
        }

        return success(__('Created Successfully'));
    }

    public function update($id, array $request)
    {
        $plan = $this->getById($id);

        $data = PlanData::from($request)->all();

        $plan->update($data);

        if (request()->hasFile('image')) {
            // Remove the old image(s)
            $plan->clearMediaCollection('plans');

            // Add the new image
            $plan->addMedia(request()->file('image'))
                ->toMediaCollection('plans');
        }

        return success(__('Updated Successfully'));
    }

    public function delete($id)
    {
        $plan = $this->getById($id);

        $subscriptions = $plan->subscriptions->where('status', 'Active');

        if ($subscriptions->isNotEmpty()) {
            return error(__('You cannot delete a plan that has active subscriptions.'), 400);
        }

        $plan->delete();

        return success(__('Deleted Successfully'));
    }

    public function restore($id)
    {
        $plan = $this->planRepo->findTrashedById($id);

        if (! $plan) {
            throw new NotFoundHttpException;
        }

        return $plan->restore();
    }

    public function forceDelete($id)
    {
        $plan = $this->planRepo->findTrashedById($id);

        if (! $plan) {
            throw new NotFoundHttpException;
        }

        return $plan->forceDelete();
    }

    public function all()
    {
        return $this->planRepo->getAll();
    }

    public function ddl()
    {

        if (request()->has('subscriptionId')) {
            $subscriptionId = request()->subscriptionId;

            if (! $subscriptionId) {
                throw new NotFoundHttpException;
            }

            $subscription = $this->subscriptionRepo->findById(request()->subscriptionId);

            if (! $subscription) {
                throw new NotFoundHttpException;
            }

            return $this->planRepo->getWhere([['status', 'Active'], ['id', '!=', $subscription->plan_id]]);
        } else {

            return $this->planRepo->getWhere(['status' => 'Active']);
        }
    }

    public function count()
    {
        $filterable = request()->only(['start_date', 'end_date']);

        return $this->planRepo->count($filterable);
    }
}
