<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContractorPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'contractor' => [
                'id' => $this->contractor->id,
                'name' => $this->contractor->name,
            ],
            'item' => [
                'id' => $this->item->id,
                'name' => $this->item->name,
            ],
            'amount' => $this->amount,
            'date' => $this->date,
            'notes' => $this->notes,
            'created_by' => $this->created_by,
            'archive_by' => $this->archived_by,
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
        ];
    }
}
