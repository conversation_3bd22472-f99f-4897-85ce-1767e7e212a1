<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\MaxImagesRule;
use App\Models\Tenant\Purchase;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UpdatePurchaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // find the purchase
        $purchase = Purchase::find($this->route('purchase'));

        if (! $purchase) {
            throw new NotFoundHttpException;
        }

        return [
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'date' => ['required', 'date'],
            'partner_id' => [
                'required',
                Rule::exists('partners', 'id')
                    ->where('type', 'supplier')
                    ->where('permanent_deleted', false)
                    ->whereNull('deleted_at')
            ],
            'categories' => Rule::when($purchase->type === 'detailed', ['required', 'array']),
            'categories.*.id' => Rule::when($purchase->type === 'detailed', ['required', 'exists:items,id']),
            'categories.*.items' => Rule::when($purchase->type === 'detailed', ['required', 'array', 'min:1']),
            'categories.*.items.*.name' => Rule::when($purchase->type === 'detailed', ['required', 'string', 'min:3', 'max:30']),
            'categories.*.items.*.unit_id' => Rule::when($purchase->type === 'detailed', ['required', 'exists:units,id']),
            'categories.*.items.*.price' => Rule::when($purchase->type === 'detailed', ['required', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'min:1']),
            'categories.*.items.*.qty' => Rule::when($purchase->type === 'detailed', ['required', 'integer', 'min:1', 'min_digits:1', 'max_digits:10']),
            'item_id' => Rule::when($purchase->type === 'printed', ['required', 'exists:items,id']),
            'notes' => ['sometimes', 'string', 'max:100'],
            'images' => [
                'nullable',
                'array',
                'min:1',
                new MaxImagesRule($purchase->id),
            ],
            'images.*' => ['nullable', 'mimes:png,jpg,jpeg', 'max:5120'],
            'total_before_discount' => Rule::when($purchase->type === 'printed', ['required', 'numeric', 'min:1']),
            'discount_type' => ['nullable', 'in:fixed,percentage'],
            'discount_amount' => [
                'required_with:discount_type',
                'numeric',
                'min:1',
                Rule::when($this->discount_type === 'percentage', 'max:100'),
                Rule::when($this->discount_type !== 'percentage', [
                    'regex:/^\d{1,10}(\.\d{1,2})?$/',
                ]),
            ],
            'status' => ['required', 'in:unpaid,partial,paid'],
            'paid_amount' => ['required_if:status,partial', 'numeric', Rule::when($this->discount_type === 'percentage' && $this->discount_amount == 100, 'min:0', ['min:1', 'regex:/^\d{1,10}(\.\d{1,2})?$/'])],
        ];
    }

    public function messages()
    {
        return [
            'discount_amount.required_with' => __('validation.required'),
            'paid_amount.required_if' => __('validation.required'),
            'categories.*.id.required_if' => __('validation.required'),
            'categories.*.items.required_if' => __('validation.required'),
            'categories.*.items.*.price.required_if' => __('validation.required'),
            'categories.*.items.*.qty.required_if' => __('validation.required'),
            'categories.*.items.*.name.required_if' => __('validation.required'),
            'categories.*.items.*.unit_id.required_if' => __('validation.required'),
            'item_id.required_if' => __('validation.required'),
            'total_before_discount.required_if' => __('validation.required'),
            'discount_amount.required_with' => __('validation.required'),

        ];
    }
}
