<?php

namespace App\Listeners;

use App\Events\MessageReadEvent;
use App\Models\Tenant\Message;
use App\Models\Tenant\MessageUserRead;

class MarkMessagesAsReadListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MessageReadEvent $event): void
    {
        $unreadMessages = Message::where('room_id', $event->roomId)
            ->whereDoesntHave('readers', function ($query) use ($event) {
                $query->where('user_id', $event->userId);
            })
            ->get();

        foreach ($unreadMessages as $message) {
            MessageUserRead::create([
                'message_id' => $message->id,
                'user_id' => $event->userId,
                'read_at' => now(),
            ]);
        }
    }
}
