<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\UpdateSubscriptionRequest;
use App\Http\Resources\Tenant\Admin\SubscriptionResource;
use App\Services\Tenant\SubscriptionService;

class SubscriptionController extends Controller
{
    public function __construct(public SubscriptionService $subscriptionService) {}

    public function index()
    {
        $subscription = $this->subscriptionService->getSubscription();

        return success(new SubscriptionResource($subscription));
    }

    public function update(UpdateSubscriptionRequest $request)
    {
        return $this->subscriptionService->update($request->validated());
    }
}
