<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'size' => $this->size,
            'clients' => ProjectUserResource::collection($this->clients),
            'area' => $this->area,
            'payment_method' => [
                'id' => $this->paymentMethod->id,
                'name' => $this->paymentMethod->name,
            ],
            'package' => $this->when($this->payment_method_id == 6 && $this->package, [
                'id' => $this->package?->id,
                'name' => $this->package?->name,
                'price_per_meter' => $this->package?->price_per_meter,
            ]),
            'office_ratio' => $this->payment_method_id == 1 ? $this->office_ratio : null,                       // يعرض نسبة المكتب فى حالة طريقة الدفع (نسبة مئوية من المصاريف)
            'amount_per_meter' => in_array($this->payment_method_id, [4, 5, 6]) ? $this->amount_per_meter : null,   // عرض سعر المتر فى حالة طريقة الدفع (سعر متر إشراف) (سعر متر اشراف والتنفيذ) ( باقات )
            'total' => $this->total,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'description' => $this->description,
            'engineers' => [
                'site_engineers' => ProjectUserResource::collection($this->users->where('type', 'site_engineer')),
                'office_engineers' => ProjectUserResource::collection($this->users->where('type', 'office_engineer')),
            ],
            'accountants' => ProjectUserResource::collection($this->users->where('type', 'accountant')),
            'project_managers' => ProjectUserResource::collection($this->users->where('type', 'project_manager')),
            'admins' => ProjectUserResource::collection($this->users->where('type', 'admin')),
            'withdraw_limit' => $this->withdraw_limit,
            'balance' => $this->balance,
            'media' => MediaResource::collection($this->media),
            'status' => $this->status,
            'user_credit_allowance' => (float) getSetting('user_credit_allowance') ?? 0,
            'project_credit_allowance' => (float) getSetting('project_credit_allowance') ?? 0,
            'has_assessments' => $this->has_project_statements

        ];
    }
}
