<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateUserRequest;
use App\Http\Requests\Tenant\Admin\UpdateUserRequest;
use App\Http\Requests\Tenant\TransferBalanceRequest;
use App\Http\Requests\Tenant\UpdateBalanceRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\UserResource;
use App\Http\Resources\Tenant\UserContractorPaymentResource;
use App\Http\Resources\Tenant\UserDetailsResource;
use App\Http\Resources\Tenant\UserPurchaseResource;
use App\Http\Resources\Tenant\UserTransactionResource;
use App\Services\Tenant\UserService;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class UserController extends Controller implements HasMiddleware
{
    public function __construct(public UserService $userService) {}

    public static function middleware(): array
    {
        return [
            new Middleware('can_create_user', ['store', 'restore']),
        ];
    }

    public function index()
    {
        $users = $this->userService->index();

        return success(new BaseCollection($users, UserResource::class));
    }

    public function show($id)
    {
        $user = $this->userService->show($id);

        return success(new UserDetailsResource($user));
    }

    public function store(CreateUserRequest $request)
    {
        return $this->userService->create($request->validated());
    }

    public function update(string $id, UpdateUserRequest $request)
    {
        return $this->userService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->userService->delete($id);
    }

    public function archive()
    {
        $users = $this->userService->getArchived();

        return success(new BaseCollection($users, UserResource::class));
    }

    public function restore($id)
    {
        return $this->userService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->userService->forceDelete($id);
    }

    public function ProjectsDDl()
    {
        $projects = $this->userService->searchddl();

        return success($projects);
    }

    public function updateBalance(UpdateBalanceRequest $request, string $id)
    {
        return $this->userService->updateBalance($request->validated(), $id);
    }

    public function transferBalance(TransferBalanceRequest $request, string $id)
    {
        return $this->userService->transferBalance($request->validated(), $id);
    }

    public function transactions(string $id)
    {
        $transactions = $this->userService->getTransactions($id);

        return success(new BaseCollection($transactions, UserTransactionResource::class));
    }

    public function purchases(string $id)
    {
        $purchases = $this->userService->getPurchases($id);

        return success(new BaseCollection($purchases, UserPurchaseResource::class));
    }

    public function contractorPayments(string $id)
    {
        $payments = $this->userService->getContractorPayments($id);

        return success(new BaseCollection($payments, UserContractorPaymentResource::class));
    }
}
