<?php

namespace App\Services\Tenant;

use App\Repositories\Tenant\HistoryRepository;

class HistoryService
{
    public function __construct(public HistoryRepository $historyRepository) {}

    public function get()
    {
        $filterable = request()->only(['search', 'date_from', 'date_to' , 'project']);

        return $this->historyRepository->getPaginated($filterable);
    }

    public function getLimit(int $limit = 5)
    {
        return $this->historyRepository->limit($limit);
    }
}
