<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserBalance extends Model
{
    protected $fillable = ['userable_id', 'userable_type'];

    /**
     * Get the owning userable model (User, Merchant, etc.).
     */
    public function userable(): MorphTo
    {
        return $this->morphTo()->withoutGlobalScopes();
    }
    
}   
