<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $type = ucfirst($this->type) . ' ' . preg_replace('/([a-z])([A-Z])/', '$1 $2', class_basename($this->transactionable));

        return [
            'id' => $this->id,
            'type' => $type,
            'message' => $this->getTranslation('message', app()->getLocale()) ?? $this->message,
            'amount' => $this->amount,
            'created_at' => $this->created_at->format('Y-m-d'),
            'user' => $this->userable->name,
        ];
    }
}
