<?php

namespace App\Repositories;

use App\Models\Plan;

class PlanRepository
{
    public function __construct(public Plan $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where('name_ar', 'like', "%{$filters['search']}%")
                ->orWhere('name_en', 'like', "%{$filters['search']}%");
        })->when(isset($filters['status']), function ($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function getAll()
    {
        return $this->model->all();
    }

    public function getWhere(array $data)
    {
        return $this->model->where($data)->get();
    }

    public function count(array $fillters = [])
    {
        return $this->model->when(isset($fillters['start_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '>=', $fillters['start_date']);
        })->when(isset($fillters['end_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '<=', $fillters['end_date']);
        })->count();
    }

    public function findByIdOnCenteral($id)
    {
        return $this->model->on('central')->find($id);
    }
}
