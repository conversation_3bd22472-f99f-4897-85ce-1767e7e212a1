<?php

namespace App\Rules;

use App\Models\Role;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IgnoreSuperAdminRole implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $role = Role::withoutGlobalScope('excludeSuperAdmin')->find($value);

        if ($role?->name === 'Super Admin' || $role?->is_built_in) {
            $fail(__('You cannot assign this role.'));
        }
    }
}
