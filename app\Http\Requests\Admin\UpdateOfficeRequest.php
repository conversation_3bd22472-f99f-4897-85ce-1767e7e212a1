<?php

namespace App\Http\Requests\Admin;

use App\Rules\UniquePhoneWithoutLeadingZero;
use App\Rules\UniqueTenantDomain;
use App\Rules\ValidateTime;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateOfficeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = request()->route('office');

        return [
            'image' => [
                'sometimes',
                'mimes:jpeg,png,jpg,gif,svg',
                'max:5120',
            ],
            'name_ar' => 'required|string|min:3|max:30',
            'name_en' => 'required|string|min:3|max:30',
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('offices', ignoreId: $id)],
            'email' => 'required|email|unique:offices,email,'.$id,
            'address' => 'sometimes|string|min:3|max:100',
            'location' => 'sometimes|array',
            'location.lat' => 'sometimes|numeric',
            'location.lng' => 'sometimes|numeric',
            'work_hour_from' => ['sometimes', 'string', new ValidateTime],
            'work_hour_to' => ['sometimes', 'string', new ValidateTime],
            'work_day_from' => 'sometimes|string',
            'work_day_to' => 'sometimes|string',
            'description' => 'sometimes|string',
            'status' => 'required|in:Active,Disabled',
            'apperance_color' => 'sometimes|string',
            'domain' => [
                'sometimes',
                'string',
                'max:100',
                new UniqueTenantDomain($id),
            ],
        ];
    }

    public function messages()
    {
        return [
            'location.*.lat.required' => __('validation.required'),
            'location.*.lng.required' => __('validation.required'),
        ];
    }
}
