<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreatePartnerRequest;
use App\Http\Requests\Tenant\Admin\UpdatePartnerRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\PartnerResource;
use App\Http\Resources\Tenant\DDLResource;
use App\Services\Tenant\PartnerService;
use Illuminate\Http\Request;

class PartnerController extends Controller
{
    public function __construct(public PartnerService $partnerService) {}

    public function index()
    {
        $partners = $this->partnerService->get();

        return success(new BaseCollection($partners, PartnerResource::class));
    }

    public function store(CreatePartnerRequest $request)
    {
        return $this->partnerService->create($request->validated());
    }

    public function show(string $id)
    {
        $partner = $this->partnerService->getById($id);

        return success(new PartnerResource($partner));
    }

    public function update(UpdatePartnerRequest $request, string $id)
    {
        return $this->partnerService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->partnerService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->partnerService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->partnerService->forceDelete($id);
    }

    public function ddl(Request $request)
    {
        $partners = $this->partnerService->ddl($request->input('type', 'all'));

        return success(DDLResource::collection($partners));
    }
}
