<?php

namespace App\Services;

use App\DTO\AdminData;
use App\Repositories\AdminRepository;
use App\Repositories\PermissionRepository;
use App\Repositories\RoleRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AdminService
{
    public function __construct(
        public AdminRepository $adminRepo,
        public RoleRepository $roleRepo,
        public PermissionRepository $permissionRepo
    ) {}

    public function get()
    {
        $filterable = request()->only(['search']);

        return $this->adminRepo->getPaginated(filters: $filterable);
    }

    public function getById($id)
    {
        $admin = $this->adminRepo->findById($id);

        if (! $admin) {
            throw new NotFoundHttpException;
        }

        return $admin;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $data = AdminData::from($request)->all();

            $admin = $this->adminRepo->create($data);

            if (isset($request['permissions'])) {

                $permissions = $this->permissionRepo->getWhereIn($request['permissions']);

                $admin->syncPermissions($permissions);
            }

            if (request()->hasFile('image')) {
                if ($admin->image) {
                    Storage::disk('public')->delete($admin->image->path);
                }

                $admin->addMedia(request()->image)
                    ->toMediaCollection('admins');
            }

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        try {

            $admin = $this->getById($id);

            $role = $this->roleRepo->findById($request['role_id']);

            if (! $role) {
                throw new NotFoundHttpException;
            }

            if ($role->status === 'Disabled' && $request['status'] === 'Active') {
                return error(__('You cannot assign this role to an admin.'));
            }

            $data = AdminData::from($request)->all();

            $admin->update($data);

            if (request()->hasFile('image')) {
                // Remove the old image(s)
                $admin->clearMediaCollection('admins');
            
                // Add the new image
                $admin->addMedia(request()->file('image'))
                    ->toMediaCollection('admins');
            }
            

            $permissions = $this->permissionRepo->getWhereIn($request['permissions']);

            $admin->syncPermissions($permissions);

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            dd($e);
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function delete($id)
    {
        $admin = $this->getById($id);

        $admin->receivers()->delete();

        return $admin->delete();
    }

    public function updatePassword($id, string $password)
    {
        $admin = $this->getById($id);

        return $admin->update(['password' => $password]);
    }

    public function count()
    {
        $filterable = request()->only(['start_date', 'end_date']);

        return $this->adminRepo->count($filterable);
    }

    public function getAll()
    {
        return $this->adminRepo->all();
    }

    public function ddl()
    {
        return $this->adminRepo->getWhere(['status' => 'Active']);
    }
}
