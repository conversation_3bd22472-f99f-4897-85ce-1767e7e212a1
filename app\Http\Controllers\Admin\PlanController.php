<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreatePlanRequest;
use App\Http\Requests\Admin\UpdatePlanRequest;
use App\Http\Resources\Admin\PlanResource;
use App\Http\Resources\BaseCollection;
use App\Services\PlanService;

class PlanController extends Controller
{
    public function __construct(public PlanService $planService) {}

    public function index()
    {
        $plans = $this->planService->get();

        return success(new BaseCollection($plans, PlanResource::class));
    }

    public function store(CreatePlanRequest $request)
    {
        return $this->planService->create($request->validated());
    }

    public function show(string $id)
    {
        $plan = $this->planService->getById($id);

        return success(new PlanResource($plan));
    }

    public function update(UpdatePlanRequest $request, string $id)
    {
        return $this->planService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->planService->delete($id);
    }

    public function restore(string $id)
    {
        $this->planService->restore($id);

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $this->planService->forceDelete($id);

        return success(__('Deleted Successfully'));
    }
}
