<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RefundsDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'number' => $this->number,
            'name' => $this->name,

            'date' => $this->date,
            'created_at' => $this->created_at,
            'partner' => [
                'id' => $this->supplier->id,
                'name' => $this->supplier->name,
            ],
            'notes' => $this->notes,
            'media' => MediaResource::collection($this->media),
            'categories' => RefundCategoryResource::collection($this->invoiceCategory),
            'summary' => [
                'total_before_discount' => $this->total_before_discount,
                'discount_type' => $this->discount_type,
                'discount_value' => $this->discount_value,
                'total_after_discount' => $this->total_after_discount,
                'paid_amount' => $this->paid_amount,
                'remaining_amount' => $this->remaining_amount,
                'status' => $this->status,
            ],
        ];
    }
}
