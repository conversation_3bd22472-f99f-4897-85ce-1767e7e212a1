<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\PaymentMethodResource;
use App\Repositories\Tenant\PaymentMethodRepository;

class PaymentMethodController extends Controller
{
    public function __construct(protected PaymentMethodRepository $paymentMethodRepo) {}

    public function __invoke()
    {
        $paymentMethods = $this->paymentMethodRepo->ddl();

        return success(PaymentMethodResource::collection($paymentMethods));
    }
}
