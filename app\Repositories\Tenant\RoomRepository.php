<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Room;
use App\Models\Tenant\Message;

class RoomRepository
{
    public function __construct(public Room $room) {}

    public function get(int $limit = 10)
    {
        $userId = auth()->id();
        $perPage = filter_var(request('per_page'), FILTER_VALIDATE_INT) ?: $limit;
        $search = request('search');

        return $this->room
            ->with([
                'project',
                'latestMessage' => function ($query) {
                    $query->latest()->limit(1);
                }
            ])
            ->whereHas('users', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($search, function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->withCount([
                'messages as unread_messages_count' => function ($query) use ($userId) {
                    $query->whereDoesntHave('readers', function ($q) use ($userId) {
                        $q->where('user_id', $userId);
                    });
                }
            ])
            ->orderByDesc(function ($query) {
                $query->select('created_at')
                    ->from('messages')
                    ->whereColumn('room_id', 'rooms.id')
                    ->latest()
                    ->limit(1);
            })
            ->paginate($perPage);
    }

    public function getUnreadRoomsCount()
    {
        $userId = auth()->id();

        return $this->room
            ->whereHas('users', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->whereHas('messages', function ($query) use ($userId) {
                $query->whereDoesntHave('readers', function ($q) use ($userId) {
                    $q->where('user_id', $userId);
                });
            })
            ->count();
    }
}
