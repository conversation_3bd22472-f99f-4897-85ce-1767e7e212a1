<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ClientData;
use App\Models\Tenant\cleint;
use App\Repositories\Tenant\ClientRepository;
use App\Repositories\Tenant\ProjectRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ClientService
{
    public function __construct(
        public ClientRepository $clientRepository,
        public ProjectRepository $projectRepository,

    ) {}

    // List paginated cleints
    public function index()
    {
        return $this->clientRepository->getPaginated();
    }

    // Show a specific cleint by ID
    public function show($id)
    {
        $client = $this->clientRepository->findById($id);

        if (! $client) {
            throw new NotFoundHttpException;
        }

        return $client;
    }

    // Create a new cleint
    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = ClientData::from($request)->all();

            $projectIds = $request['project_ids'] ?? [];

            $client = $this->clientRepository->create($data);

            if (request()->hasFile('image')) {
                $client->addMedia(request()->file('image'))
                    ->toMediaCollection('clients');
            }

            if (! empty($projectIds)) {
                $client->projects()->sync($projectIds);
            }

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    // Update an existing cleint
    public function update($id, array $request)
    {
        DB::beginTransaction();
        $client = $this->clientRepository->findById($id);

        if (! $client) {
            throw new NotFoundHttpException;
        }

        try {

            // Validate and transform the data
            $data = ClientData::from($request)->all();

            // Update the client
            $this->clientRepository->update($client, $data);

            if (request()->hasFile('image')) {
                $client->clearMediaCollection('clients');
                $client->addMedia(request()->file('image'))
                    ->toMediaCollection('clients');
            }

            // Sync projects if provided
            if (isset($request['project_ids']) && is_array($request['project_ids'])) {
                $client->projects()->sync($request['project_ids']);
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'cleint update failed. Please check your input.');
        }
    }

    // Delete a cleint
    public function delete($id)
    {
        DB::beginTransaction();
        $cleint = $this->clientRepository->findById($id);

        if (! $cleint) {
            throw new NotFoundHttpException;
        }
        try {
            $this->clientRepository->delete($cleint);

            DB::commit();

            return response()->json(['message' => 'cleint deleted successfully']);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'cleint deletion failed.');
        }
    }

    public function forceDelete($id)
    {
        DB::beginTransaction();
        $cleint = $this->clientRepository->findOnlyTrashedById($id);

        if (! $cleint) {
            throw new NotFoundHttpException;
        }
        try {
            $this->clientRepository->forceDelete($cleint);

            DB::commit();

            return response()->json(['message' => 'cleint deleted successfully']);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'cleint deletion failed.');
        }
    }

    public function restore($id)
    {
        $cleint = $this->clientRepository->findOnlyTrashedById($id);

        if (! $cleint) {
            throw new NotFoundHttpException;
        }
        DB::beginTransaction();

        try {
            $cleint = $this->clientRepository->restore($cleint);

            DB::commit();

            return $cleint;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'cleint restoration failed.');
        }
    }

    // Get a list of archived cleints
    public function getArchived()
    {
        return $this->clientRepository->getArchived();
    }

    public function searchddl()
    {
        return $this->projectRepository->searchDdl();
    }

    public function ddl(?array $columns = ['id', 'name'])
    {
        return $this->clientRepository->getAll($columns);
    }
}
