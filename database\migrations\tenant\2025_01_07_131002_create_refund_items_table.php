<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refund_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_category_id')->constrained('invoice_categories')->cascadeOnDelete();
            $table->string('name');
            $table->foreignId('unit_id')->constrained('units')->cascadeOnDelete();
            $table->double('price');
            $table->integer('qty_purchased')->nullable();
            $table->integer('qty_refunded');
            $table->double('total');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refund_items');
    }
};
