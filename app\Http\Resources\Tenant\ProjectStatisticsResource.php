<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'project' => $this['project'],
            'financial' => [
                'project_balance' => $this['statistics']['project_balance'],
                'project_purchases' => $this['statistics']['project_purchases'],
                'project_expenses' => $this['statistics']['project_expenses'],
                'project_debts' => $this['statistics']['project_debts'],
                'project_profits' => $this['statistics']['project_profits'],
            ],
            'users' => [
                'clients_count' => $this['statistics']['project_clients_count'],
                'engineers_count' => $this['statistics']['project_engineers_count'],
                'accountants_count' => $this['statistics']['project_accountants_count'],
                'project_managers_count' => $this['statistics']['project_managers_count'],
                'admins_count' => $this['statistics']['project_admins_count'],
            ],
            'charts' => [
                'project_payments' => $this['statistics']['project_payments_chart']->map(function ($item) {
                    return [
                        'month' => $item->month,
                        'total' => $item->total_amount,
                    ];
                }),
                'items_by_unit' => $this['statistics']['project_items_by_unit_chart']->map(function ($item) {
                    return [
                        'unit_name' => $item['unit_name'],
                        'total' => $item['total_amount'],
                    ];
                }),
            ],
        ];
    }
}