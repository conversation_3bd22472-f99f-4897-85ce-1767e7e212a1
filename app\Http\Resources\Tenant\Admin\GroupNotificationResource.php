<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GroupNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sender_name' => $this->sender_name,
            'title' => $this->title,
            'body' => $this->body,
            'created_at' => $this->created_at->format('d/m/Y - h:i A'),
            'receivers' =>  $this->groupReceiversByType(),
        ];
    }

    private function groupReceiversByType(): array
    {
        return $this->notification_receivers
            ->groupBy(fn($receiver) => $receiver->receiverable->type ?? 'unknown') // Group by receiver type
            ->mapWithKeys(fn($receivers, $type) => [
                $type => NotificationReceiverResource::collection($receivers)
            ])
            ->toArray();
    }
}
