<?php

namespace App\Http\Middleware;

use App\Repositories\Tenant\UserRepository;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CanCreateUser
{
    public function __construct(protected UserRepository $userRepo)
    {
        //
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $subscription = tenant()->subscription;

        if ($request->input('type')) {
            $userType = $request->input('type');
        } else {
            $userType = $this->userRepo->findOnlyTrashedById($request->route('id'))?->type;
        }

        $flags = [
            'site_engineer' => [
                'limit' => 'number_of_site_engineers',
                'unlimited' => 'is_unlimited_site_engineers',
                'name' => __('Site Engineer'),
            ],
            'office_engineer' => [
                'limit' => 'number_of_office_engineers',
                'unlimited' => 'is_unlimited_office_engineers',
                'name' => __('Office Engineer'),
            ],
            'accountant' => [
                'limit' => 'number_of_accountants',
                'unlimited' => 'is_unlimited_accountants',
                'name' => __('Accountant'),
            ],
            'project_manager' => [
                'limit' => 'number_of_project_managers',
                'unlimited' => 'is_unlimited_project_managers',
                'name' => __('Project Manager'),
            ],
            'admin' => [
                'limit' => 'number_of_admins',
                'unlimited' => 'is_unlimited_admins',
                'name' => __('Admin'),
            ],
        ];

        if (isset($flags[$userType])) {
            $limitField = $flags[$userType]['limit'];
            $unlimitedField = $flags[$userType]['unlimited'];
            $name = $flags[$userType]['name'];

            if (! $subscription->{$unlimitedField} && $subscription->{$limitField} <= 0) {
                return error(__('You have reached your subscription limit, you cannot add a new :name', ['name' => $name]), 400);
            }
        }

        return $next($request);
    }
}
