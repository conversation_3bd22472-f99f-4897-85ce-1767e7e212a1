<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidateTime implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if the value is a string and matches the HH:MM format
        if (! is_string($value) || ! preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $value)) {
            $fail(__('validation.invalid_time_format'));

            return;
        }
    }
}
