<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $routes = Route::getRoutes();
        $permissions = [];

        foreach ($routes as $route) {
            $name = $route->getName();

            // Skip routes without a name or those that don't start with 'admin.'
            if (! $name || ! Str::startsWith($name, 'admin.')) {
                continue;
            }

            // Extract the part of the route name after 'admin.'
            $permissionParts = explode('.', str_replace('admin.', '', $name));
            $mainRoute = $permissionParts[0] ?? ''; // The main route (e.g., "users")

            // Skip if the main route is empty
            if (empty($mainRoute)) {
                continue;
            }

            $action = $permissionParts[1] ?? 'index'; // Default to "index" if no action is specified

            // Initialize the main route in the permissions array if not already set
            if (! isset($permissions[$mainRoute])) {
                $permissions[$mainRoute] = [];
            }

            // Add the action to the main route if not already present
            if (! in_array($action, $permissions[$mainRoute])) {
                $permissions[$mainRoute][] = $action;
            }
        }

        foreach ($permissions as $mainRoute => $actions) {
            foreach ($actions as $action) {
                // Create or retrieve the permission
                $permission = Permission::firstOrCreate(
                    ['group' => $mainRoute, 'name' => $action, 'guard_name' => 'admin'],
                    ['group' => $mainRoute, 'name' => $action, 'guard_name' => 'admin']
                );

                $admin = Admin::whereHas('role', function ($query) {
                    $query->withoutGlobalScope('excludeSuperAdmin')
                        ->where('name', 'Super Admin');
                })->first();

                // Check if the admin already has this permission
                if (! $admin->hasPermissionTo($permission)) {
                    // Assign the permission to the admin
                    $admin->givePermissionTo($permission);
                }
            }
        }
    }
}
