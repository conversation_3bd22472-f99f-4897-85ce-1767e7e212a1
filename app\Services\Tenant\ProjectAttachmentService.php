<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ProjectAttachmentData;
use App\Repositories\Tenant\ProjectAttachmentRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ProjectAttachmentService
{
    public function __construct(
        public ProjectAttachmentRepository $projectAttachmentRepository,

    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'type', 'from_date', 'item', 'to_date', 'attach_type']);

        if (request()->type === 'archive') {
            return $this->projectAttachmentRepository->getTrashedWithPaginate(filters: $filterable);
        } else {
            return $this->projectAttachmentRepository->getPaginated(filters: $filterable);
        }
    }

    public function getById($id)
    {
        $admin = $this->projectAttachmentRepository->findById($id);

        if (! $admin) {
            throw new NotFoundHttpException(__('resource not found'));
        }

        return $admin;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = ProjectAttachmentData::from($request)->all();
            $data['creator'] = auth()->user()->name;

            $attachment = $this->projectAttachmentRepository->create($data);
            $project_id = $data['project_id'];
            if (! empty($request['attachments'])) {
            
               foreach (request()->file('attachments') as $image) {
                   $attachment->addMedia($image)
                       ->toMediaCollection('project-attachments');
               }
            }

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage(), 422);
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        try {
            $attachment = $this->getById($id);

            $validatedData = ProjectAttachmentData::from($request)->all();

            $attachment->update($validatedData);

            $project_id = $validatedData['project_id'];

            if (! empty($request['attachments'])) {
                
                 $attachment->clearMediaCollection('project-attachments');

                 foreach (request()->file('attachments') as $image) {
                     $attachment->addMedia($image)
                         ->toMediaCollection('project-attachments');
                 }
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error(__($e->getMessage()));
        }
    }

    public function deleteMedia(string $projectAttachmentId, string $mediaId)
    {
        $projectAttachment = $this->getById($projectAttachmentId);
    
        $media = $projectAttachment->media()->where('id', $mediaId)->first();
    
        if (! $media) {
            throw new NotFoundHttpException;
        }
    
        $media->delete(); // Spatie handles file removal automatically
    
        return success(__('Deleted Successfully'));
    }
    
    public function delete($id)
    {
        $admin = $this->getById($id);

        $admin->delete();

        return success(__('Deleted Successfully'));

    }

    public function restore($id)
    {
        $admin = $this->projectAttachmentRepository->findTrashedById($id);

        if (! $admin) {
            throw new NotFoundHttpException('Admin not found');
        }

        return $admin->restore();
    }

    public function forceDelete($id)
    {
        $projectAttachment = $this->projectAttachmentRepository->findTrashedById($id);

        if (! $projectAttachment) {
            throw new NotFoundHttpException(__('resource not found'));
        }

        $projectAttachment->update([
            'permanent_deleted' => 1,
        ]);

        $projectAttachment->save();

        return success(__('Deleted Successfully'));
    }
}
