<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Debt;
use App\Models\Tenant\Unit;
use App\Models\Tenant\User;
use App\Models\Tenant\Partner;
use App\Models\Tenant\Project;
use App\Models\Tenant\Purchase;
use App\Models\Tenant\OfficeDue;
use App\Models\Tenant\SupplyItem;
use App\Models\Tenant\Supply;
use App\Models\Tenant\History;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant\PurchaseItem;
use App\Models\Tenant\ProjectPayment;
use App\Models\Tenant\ContractorPayment;

class StatisticsRepository
{
    /**
     * Get statistics with optional unit_id and project_id filters for items chart
     *
     * @param int|null $unitId Optional unit ID to filter items chart by
     * @param int|null $projectId Optional project ID to filter items chart by
     * @return array
     */
    public function getStatistics($unitId = null, $projectId = null)
    {
        return [
            'total_project_balance' => $this->getTotalProjectBalance(),
            'total_purchases' => $this->getTotalPurchases(),
            'total_expenses' => $this->getTotalExpenses(),
            'total_debts' => $this->getTotalDebts(),
            'total_profits' => $this->getTotalProfits(),
            'clients_count' => $this->getClientsCount(),
            'engineers_count' => $this->getEngineersCount(),
            'accountants_count' => $this->getAccountantsCount(),
            'project_managers_count' => $this->getProjectManagersCount(),
            'admins_count' => $this->getAdminsCount(),
            'new_projects_count' => $this->getNewProjects(),
            'in_progress_projects_count' => $this->getInProgressProjects(),
            'completed_projects_count' => $this->getCompletedProjects(),
            'deferred_projects_count' => $this->getDeferredProjects(),
            'project_payments_chart' => $this->getProjectPaymentsChart(),
            'latest_histories' => $this->getLatestHistories(),
        ];
    }

    protected function getTotalProjectBalance()
    {
        return Project::sum('balance');
    }

    protected function getTotalPurchases()
    {
        return Purchase::sum('total_after_discount');
    }

    protected function getTotalExpenses()
    {
        $purchases = Purchase::sum('total_after_discount');
        $contractorPayments = ContractorPayment::sum('amount');

        return $purchases + $contractorPayments;
    }

    protected function getNewProjects()
    {
        return Project::where('status', 'new')->count();
    }

    protected function getInProgressProjects()
    {
        return Project::where('status', 'in_progress')->count();
    }

    protected function getCompletedProjects()
    {
        return Project::where('status', 'completed')->count();
    }

    protected function getDeferredProjects()
    {
        return Project::where('status', 'deferred')->count();
    }

    protected function getTotalDebts()
    {
        return Debt::sum('total_debt');
    }

    protected function getTotalProfits()
    {
        return OfficeDue::sum('total_profits');
    }

    protected function getClientsCount()
    {
        return Partner::where('type', 'client')->count();
    }

    protected function getEngineersCount()
    {
        return User::whereIn('type', ['site_engineer', 'office_engineer'])->count();
    }

    protected function getAccountantsCount()
    {
        return User::where('type', 'accountant')->count();
    }

    protected function getProjectManagersCount()
    {
        return User::where('type', 'project_manager')->count();
    }

    protected function getAdminsCount()
    {
        return User::where('type', 'admin')->count();
    }

    protected function getProjectPaymentsChart()
    {
        return ProjectPayment::select('projects.name as project_name', DB::raw('SUM(project_payments.amount) as total_amount'))
            ->join('projects', 'project_payments.project_id', '=', 'projects.id')
            ->groupBy('projects.id', 'projects.name')
            ->orderByDesc('total_amount')
            ->limit(10)
            ->get();
    }

    /**
     * Get items by unit chart data with optional unit_id and project_id filters
     *
     * @param int|null $unitId Optional unit ID to filter by
     * @param int|null $projectId Optional project ID to filter by
     * @return \Illuminate\Support\Collection
     */
    public function getItemsByUnitChart($unitId = null, $projectId = null)
    {
        // Get totals from purchase_items
        $purchaseItemsQuery = PurchaseItem::select('purchase_items.unit_id', DB::raw('SUM(purchase_items.total) as total_amount'))
            ->join('invoice_categories', 'purchase_items.invoice_category_id', '=', 'invoice_categories.id')
            ->join('purchases', function ($join) {
                $join->on('invoice_categories.invoiceable_id', '=', 'purchases.id')
                    ->where('invoice_categories.invoiceable_type', '=', Purchase::class);
            })
            ->groupBy('purchase_items.unit_id');

        // Apply unit_id filter if provided
        if ($unitId) {
            $purchaseItemsQuery->where('purchase_items.unit_id', $unitId);
        }

        // Apply project_id filter if provided
        if ($projectId) {
            $purchaseItemsQuery->where('purchases.project_id', $projectId);
        }

        $purchaseItems = $purchaseItemsQuery->get()->keyBy('unit_id');

        // Get totals from supply_items
        $supplyItemsQuery = SupplyItem::select('supply_items.unit_id', DB::raw('SUM(supply_items.total) as total_amount'))
            ->join('invoice_categories', 'supply_items.invoice_category_id', '=', 'invoice_categories.id')
            ->join('supplies', function ($join) {
                $join->on('invoice_categories.invoiceable_id', '=', 'supplies.id')
                    ->where('invoice_categories.invoiceable_type', '=', Supply::class);
            })
            ->groupBy('supply_items.unit_id');

        // Apply unit_id filter if provided
        if ($unitId) {
            $supplyItemsQuery->where('supply_items.unit_id', $unitId);
        }

        // Apply project_id filter if provided
        if ($projectId) {
            $supplyItemsQuery->where('supplies.project_id', $projectId);
        }

        $supplyItems = $supplyItemsQuery->get()->keyBy('unit_id');

        // Get units (either all or just the specified one)
        $unitsQuery = Unit::query();
        if ($unitId) {
            $unitsQuery->where('id', $unitId);
        }
        $units = $unitsQuery->get();

        // Combine the data
        $result = $units->map(function ($unit) use ($purchaseItems, $supplyItems) {
            $purchaseTotal = $purchaseItems->get($unit->id)->total_amount ?? 0;
            $supplyTotal = $supplyItems->get($unit->id)->total_amount ?? 0;

            return [
                'unit_id' => $unit->id,
                'unit_name' => $unit->name,
                'total_amount' => $purchaseTotal + $supplyTotal,
            ];
        });

        // Sort by total amount and limit to top 10 (unless filtering by a specific unit)
        $result = $result->sortByDesc('total_amount');

        // Only take top 10 if not filtering by a specific unit
        if (!$unitId) {
            $result = $result->take(10);
        }

        return $result->values();
    }

    /**
     * Get the latest 10 history records with additional details
     *
     * @return \Illuminate\Support\Collection
     */
    protected function getLatestHistories()
    {
        return History::orderByDesc('created_at')
            ->limit(10)
            ->get()
            ->map(function ($history) {
                return [
                    'id' => $history->id,
                    'causer_name' => $history->causer_name,
                    'model' => $history->model,
                    'message' => $history->message,
                    'message_ar' => $history->message_ar,
                    'message_en' => $history->message_en,
                    'created_at' => $history->created_at->format('Y-m-d H:i:s'),
                    'formatted_date' => $history->created_at->diffForHumans(),
                ];
            });
    }



    public function getProjectStatistics($projectId)
    {
        // Make sure to select all necessary fields
        $project = Project::with([
            'purchases',
            'payments',
            'debts',
            'officeDue',
            'clients',
            'users',
        ])->findOrFail($projectId);

        // Ensure we have the project name
        if (!$project || !$project->name) {
            throw new \Exception("Project name not found for project ID: $projectId");
        }

        // Calculate total expenses (purchases + contractor payments)
        $purchasesTotal = $project->purchases->sum('total_after_discount');
        $contractorPaymentsTotal = ContractorPayment::where('project_id', $projectId)->sum('amount');
        $totalExpenses = $purchasesTotal + $contractorPaymentsTotal;

        // Get users by type
        $engineers = $project->users->whereIn('type', ['site_engineer', 'office_engineer']);
        $accountants = $project->users->where('type', 'accountant');
        $projectManagers = $project->users->where('type', 'project_manager');
        $admins = $project->users->where('type', 'admin');

        // Get project payments chart data
        $projectPaymentsChart = ProjectPayment::select('projects.name as project_name', DB::raw('SUM(project_payments.amount) as total_amount'))
        ->join('projects', 'project_payments.project_id', '=', 'projects.id')
        ->groupBy('projects.id', 'projects.name')
        ->orderByDesc('total_amount')
        ->limit(10)
        ->get();

        // Get items by unit chart data using a different approach
        $purchaseItemsQuery = DB::table('purchase_items')
            ->join('invoice_categories', 'purchase_items.invoice_category_id', '=', 'invoice_categories.id')
            ->join('purchases', function ($join) {
                $join->on('invoice_categories.invoiceable_id', '=', 'purchases.id')
                    ->where('invoice_categories.invoiceable_type', '=', Purchase::class);
            })
            ->where('purchases.project_id', $projectId)
            ->select('purchase_items.unit_id', DB::raw('SUM(purchase_items.total) as total_amount'))
            ->groupBy('purchase_items.unit_id');

        $purchaseItems = collect($purchaseItemsQuery->get())->keyBy('unit_id');

        $supplyItemsQuery = DB::table('supply_items')
            ->join('invoice_categories', 'supply_items.invoice_category_id', '=', 'invoice_categories.id')
            ->join('supplies', function ($join) {
                $join->on('invoice_categories.invoiceable_id', '=', 'supplies.id')
                    ->where('invoice_categories.invoiceable_type', '=', Supply::class);
            })
            ->where('supplies.project_id', $projectId)
            ->select('supply_items.unit_id', DB::raw('SUM(supply_items.total) as total_amount'))
            ->groupBy('supply_items.unit_id');

        $supplyItems = collect($supplyItemsQuery->get())->keyBy('unit_id');

        $units = Unit::all();

        $itemsByUnitChart = $units->map(function ($unit) use ($purchaseItems, $supplyItems) {
            $purchaseTotal = isset($purchaseItems[$unit->id]) ? $purchaseItems[$unit->id]->total_amount : 0;
            $supplyTotal = isset($supplyItems[$unit->id]) ? $supplyItems[$unit->id]->total_amount : 0;

            return [
                'unit_id' => $unit->id,
                'unit_name' => $unit->name,
                'total_amount' => $purchaseTotal + $supplyTotal,
            ];
        })
        ->filter(function ($item) {
            return $item['total_amount'] > 0;
        })
        ->sortByDesc('total_amount')
        ->take(10)
        ->values();



        // Get project-specific histories with additional details
        $projectHistories = History::where('project_id', $projectId)
            ->orderByDesc('created_at')
            ->limit(10)
            ->get()
            ->map(function ($history) {
                return [
                    'id' => $history->id,
                    'causer_name' => $history->causer_name,
                    'model' => $history->model,
                    'message' => $history->message,
                    'message_ar' => $history->message_ar,
                    'message_en' => $history->message_en,
                    'created_at' => $history->created_at->format('Y-m-d H:i:s'),
                    'formatted_date' => $history->created_at->diffForHumans(),
                ];
            });

        return [
            'project' => [
                'id' => $project->id,
                'name' => $project->name ?? "Project #$projectId", // Fallback name if null
                'status' => $project->status,
            ],
            'total_project_balance' => $project->balance,
            'total_purchases' => $purchasesTotal,
            'total_expenses' => $totalExpenses,
            'total_debts' => $project->debts->sum('total_debt'),
            'total_profits' => $project->officeDue ? $project->officeDue->total_profits : 0,
            'clients_count' => $project->clients->count(),
            'engineers_count' => $engineers->count(),
            'accountants_count' => $accountants->count(),
            'project_managers_count' => $projectManagers->count(),
            'admins_count' => $admins->count(),
            'project_payments_chart' => $projectPaymentsChart,
            'items_by_unit_chart' => $itemsByUnitChart,
            'latest_histories' => $projectHistories,
        ];
    }
}



