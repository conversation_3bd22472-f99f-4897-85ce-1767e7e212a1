<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partner extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'mobile',
        'email',
        'address',
        'details',
        'national_id',
        'status',
        'balance',
        'image',
        'type',
    ];

    /**
     * Relationship: A Partner can have many items.
     */
    public function items()
    {
        return $this->belongsToMany(Item::class, 'partner_items', 'partner_id', 'item_id')
            ->withTimestamps();
    }

    
}
