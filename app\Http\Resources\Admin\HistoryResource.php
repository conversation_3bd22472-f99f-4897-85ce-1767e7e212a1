<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'causer_name' => $this->causer_name,
            'model' => $this->model,
            'message' => $this->message,
            'created_at' => $this->created_at->format('d/m/Y - h:i A'),
        ];
    }
}
