<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\UserData;
use App\Mail\Tenant\UserCredintails;
use App\Models\Tenant\Project;
use App\Repositories\Tenant\PermissionRepository;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\RoleRepository;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\UserRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\Tenant\TransactionMessageService;

class UserService
{
    public function __construct(
        protected UserRepository $userRepository,
        protected ProjectRepository $projectRepository,
        protected RoleRepository $roleRepo,
        protected PermissionRepository $permissionRepo,
        protected SettingRepository $settingRepository
    ) {}

    // List paginated users
    public function index()
    {
        return $this->userRepository->getPaginated();
    }

    // Show a specific user by ID
    public function show($id)
    {
        $user = $this->userRepository->findById($id);

        if (! $user) {
            throw new NotFoundHttpException;
        }

        return $user;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $request['role_id'] = $this->getRoleId($request['type'] ?? null, $request['role_id'] ?? null);
            $data = UserData::from($request)->all();
            $user = $this->userRepository->create($data);
            if (isset($request['permissions'])) {
                $this->handlePermissions($user, $request['permissions'] ?? null);
            }
            $this->handleUserImage($user);
            $this->syncUserProjects($user, $request['can_access_all_projects'] ?? false, $request['project_ids'] ?? []);
            $this->sendUserCredentials($user, $request['password'] ?? '');
            $this->updateSubscriptionLimits($user->type);

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        $user = $this->userRepository->findById($id);


        if ($user->isSuperAdmin()) {
            return error(__('You cannot modify a Super Admin user.'), 403);
        }
        if (! $user) {
            throw new NotFoundHttpException;
        }

        try {
            $request['role_id'] = $this->getRoleId($user->type, $request['role_id'] ?? null);
            $data = UserData::from($request)->all();
            $user->update($data);

            if (isset($request['permissions'])) {
                $this->handlePermissions($user, $request['permissions'] ?? null);
            }

            $this->handleUserImage($user);
            if (isset($request['project_ids']) && is_array($request['project_ids'])) {
                $user->projects()->sync($request['project_ids']);
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    private function getRoleId(?string $type, $defaultRoleId)
    {
        return match ($type) {
            'site_engineer' => $this->roleRepo->findRoleByName('Site Engineer')->id,
            'office_engineer' => $this->roleRepo->findRoleByName('Office Engineer')->id,
            'accountant' => $this->roleRepo->findRoleByName('Accountant')->id,
            'project_manager' => $this->roleRepo->findRoleByName('Project Manager')->id,
            default => $defaultRoleId,
        };
    }

    private function handlePermissions($user, ?array $permissions)
    {
        if ($permissions && $user->type == 'admin') {
            $user->syncPermissions($this->permissionRepo->getWhereIn($permissions));
        }
    }

    private function handleUserImage($user)
    {
        if (request()->hasFile('image')) {
            $user->clearMediaCollection('users');
            $user->addMedia(request()->file('image'))
                ->toMediaCollection('users');
        }
    }

    private function syncUserProjects($user, bool $canAccessAll, array $projectIds)
    {
        $user->projects()->sync($canAccessAll ? Project::pluck('id')->toArray() : $projectIds);
    }

    private function sendUserCredentials($user, string $password)
    {
        try {
            Mail::to($user->email)->send(new UserCredintails([
                'name' => $user->name,
                'email' => $user->email,
                'password' => $password,
            ]));
        } catch (Exception $e) {
            logger($e);
        }
    }

    private function updateSubscriptionLimits($userType, $operation = 'decrement')
    {
        $subscription = tenant()->subscription;

        $flags = [
            'site_engineer' => ['limit' => 'number_of_site_engineers', 'unlimited' => 'is_unlimited_site_engineers'],
            'office_engineer' => ['limit' => 'number_of_office_engineers', 'unlimited' => 'is_unlimited_office_engineers'],
            'accountant' => ['limit' => 'number_of_accountants', 'unlimited' => 'is_unlimited_accountants'],
            'project_manager' => ['limit' => 'number_of_project_managers', 'unlimited' => 'is_unlimited_project_managers'],
            'admin' => ['limit' => 'number_of_admins', 'unlimited' => 'is_unlimited_admins'],
        ];

        if (isset($flags[$userType])) {
            $limitField = $flags[$userType]['limit'];
            $unlimitedField = $flags[$userType]['unlimited'];

            if (! $subscription->{$unlimitedField}) {
                if ($operation === 'decrement' && $subscription->{$limitField} > 0) {
                    $subscription->decrement($limitField);
                } elseif ($operation === 'increment') {
                    $subscription->increment($limitField);
                }
            }
        }

        $subscription->save();
    }

    // Delete a user
    public function delete($id)
    {
        DB::beginTransaction();

        $user = $this->userRepository->findById($id);

        if ($user->isSuperAdmin()) {
            return error(__('You cannot delete a Super Admin user.'), 403);
        }

        if (! $user) {
            throw new NotFoundHttpException;
        }

        if ($user->balance > 0) {
            return error(__('You cannot delete this account, please transfer the balance first.'));
        }

        try {
            $this->updateSubscriptionLimits($user->type, 'increment');

            $user->receivers()->delete(); // Delete user receivers from notifications

            $this->userRepository->delete($user);

            DB::commit();

            return success(__('Deleted Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function forceDelete($id)
    {
        DB::beginTransaction();

        $user = $this->userRepository->findOnlyTrashedById($id);

        if (! $user || $user->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        if ($user->isSuperAdmin()) {
            return error(__('You cannot delete a Super Admin user.'), 403);
        }

        try {
            $uniqueFields = ['email', 'phone', 'national_id'];
            $updates = ['deleted_at' => now(), 'permanent_deleted' => true];

            foreach ($uniqueFields as $field) {
                if (! empty($user->$field)) {
                    $updates[$field] = 'deleted_' . $user->id . '_' . $user->$field;
                }
            }

            $user->update($updates);

            DB::commit();

            return success(__('Deleted Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function restore($id)
    {
        $user = $this->userRepository->findOnlyTrashedById($id);

        if (! $user || $user->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        DB::beginTransaction();

        try {
            $this->updateSubscriptionLimits($user->type, 'decrement');

            $user = $this->userRepository->restore($user);

            DB::commit();

            return success(__('Restored Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    // Get a list of archived users
    public function getArchived()
    {
        return $this->userRepository->getArchived();
    }

    public function searchddl()
    {
        return $this->projectRepository->searchDdl();
    }

    public function getAll()
    {
        return $this->userRepository->all();
    }

    public function ddl(array $types)
    {
        return $this->userRepository->getDdl($types);
    }

    public function updateBalance(array $request, string $id)
    {
        $auth = auth()->user();
        $user = $this->userRepository->findById($id);

        if (! $user) {
            return response()->json(['message' => __('User not found.')], 404);
        }

        if ($user->is($auth)) {
            return response()->json(['message' => __('You cannot update your own balance.')], 422);
        }

        $amount = $request['amount'];
        $type = $request['type'];

        // Check balance based on operation type
        if ($type === 'withdraw') {
            // Check user's balance for withdrawal
            $balanceCheck = $this->settingRepository->balanceCheck($amount, $user->id);
        } else {
            // Check auth user's balance for adding
            $balanceCheck = $this->settingRepository->balanceCheck($amount, $auth->id);
        }

        if (!$balanceCheck['status']) {
            return error($balanceCheck['message'], 422);
        }

        DB::transaction(function () use ($user, $auth, $amount, $type) {
            if ($type === 'withdraw') {
                $user->decrement('balance', $amount);
                $auth->increment('balance', $amount);
            } else {
                $user->increment('balance', $amount);
                $auth->decrement('balance', $amount);
            }

            // Create transaction record
            $operation = $type === 'withdraw' ? 'withdraw' : 'deposit';
            $message = TransactionMessageService::balanceOperation($operation, $auth->name, $amount, $user->name);

            $user->transactions()->create([
                'amount' => $amount,
                'userable_id' => $auth->id,
                'userable_type' => $auth->getMorphClass(),
                'type' => $type === 'withdraw' ? 'transfer' : 'add',
                'message' => $message
            ]);
        });

        return response()->json(['message' => __('Balance Updated Successfully')], 200);
    }

    public function transferBalance(array $request, string $id)
    {
        $user = $this->userRepository->findById($id);
        $transferUser = $this->userRepository->findUser($request['user']);

        if (! $user || ! $transferUser) {
            throw new NotFoundHttpException;
        }

        if ($user->balance == 0) {
            return error(__('This user has no balance to transfer.'), 422);
        }

        // Store the absolute amount before modifying balances
        $transferAmount = abs($user->balance);

        DB::transaction(function () use ($user, $transferUser, $transferAmount) {
            // Adjust balances based on positive or negative value
            if ($user->balance < 0) {
                // If the balance is negative, deduct from the recipient and reset sender balance
                $transferUser->decrement('balance', $transferAmount);
                $user->update(['balance' => 0]);
            } else {
                // If positive, transfer normally
                $transferUser->increment('balance', $transferAmount);
                $user->decrement('balance', $transferAmount);
            }

            // Log transaction
            $message = TransactionMessageService::transferOperation($user->name, $transferUser->name, $transferAmount);

            $user->transactions()->create([
                'amount' => $transferAmount,
                'userable_id' => auth()->id(),
                'userable_type' => auth()->user()->getMorphClass(),
                'type' => 'transfer',
                'message' => $message,
            ]);
        });

        return success(__('Balance Transferred Successfully'));
    }

    public function getTransactions(string $id, $limit = 10)
    {
        $user = $this->userRepository->findById($id);

        if (! $user) {
            throw new NotFoundHttpException;
        }

        return $user->userTransactions()->paginate($limit);
    }

    public function getPurchases(string $id, $limit = 10)
    {
        $user = $this->userRepository->findById($id);

        if (! $user) {
            throw new NotFoundHttpException;
        }

        return $user->purchases()
            ->with('transactionable')
            ->paginate($limit);
    }

    public function getContractorPayments(string $id, $limit = 10)
    {
        $user = $this->userRepository->findById($id);

        if (! $user) {
            throw new NotFoundHttpException;
        }

        return $user->contractorPayments()
            ->with('transactionable')
            ->paginate($limit);
    }
}
