<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name_ar' => 'required|string|min:3|max:30',
            'name_en' => 'required|string|min:3|max:30',
            'price' => 'required|numeric|min:0|max:9999',
            'number_of_site_engineers' => 'required_if_declined:is_unlimited_site_engineers|integer|min_digits:1|max_digits:4',
            'number_of_projects' => 'required_if_declined:is_unlimited_projects|integer|min_digits:1|max_digits:4',
            'number_of_office_engineers' => 'required_if_declined:is_unlimited_office_engineers|integer|min_digits:1|max_digits:4',
            'number_of_accountants' => 'required_if_declined:is_unlimited_accountants|integer|min_digits:1|max_digits:4',
            'number_of_project_managers' => 'required_if_declined:is_unlimited_project_managers|integer|min_digits:1|max_digits:4',
            'number_of_admins' => 'required_if_declined:is_unlimited_admins|integer|min_digits:1|max_digits:4',
            'storage' => 'required|integer|min_digits:1|max_digits:4',
            'storage_type' => 'required|in:MB,GB,TB',
            'status' => 'required|in:Active,Disabled',
            'has_domain' => 'required|in:1,0',
            'has_free_website' => 'required|in:1,0',
            'has_chat_availability' => 'required|in:1,0',
            'is_unlimited_projects' => 'required|in:1,0',
            'is_unlimited_site_engineers' => 'required|in:1,0',
            'is_unlimited_office_engineers' => 'required|in:1,0',
            'is_unlimited_accountants' => 'required|in:1,0',
            'is_unlimited_project_managers' => 'required|in:1,0',
            'is_unlimited_admins' => 'required|in:1,0',
        ];
    }
}
