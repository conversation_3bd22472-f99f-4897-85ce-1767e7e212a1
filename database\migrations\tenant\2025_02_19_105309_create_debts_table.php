<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('debts', function (Blueprint $table) {
            $table->id();
            $table->morphs('creditor');
            $table->morphs('debtor');
            $table->enum('type', ['purchase', 'refund', 'certification'])->default('purchase');
            $table->double('total_invoices')->default(0);
            $table->double('total_payments')->default(0);
            $table->double('total_debt')->default(0); // total_before_payment - total_after_payment
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('debts');
    }
};
