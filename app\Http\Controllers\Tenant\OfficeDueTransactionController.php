<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\OfficeDueTransactionService;
use App\Http\Requests\Tenant\Admin\OfficeDueTransactionReuqest;
use App\Http\Resources\Tenant\Admin\OfficeDueTransactionResource;

class OfficeDueTransactionController extends Controller
{
    public function __construct(protected OfficeDueTransactionService $OfficeDueTransactionService)
    {
        //
    }

    public function index()
    {
        $data = $this->OfficeDueTransactionService->index();

        return success(new BaseCollection($data, OfficeDueTransactionResource::class));
    }

    public function store(OfficeDueTransactionReuqest $request)
    {
        return $this->OfficeDueTransactionService->create($request->validated());
    }
}
