<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Project;
use Carbon\Carbon;

class ProjectRepository
{
    public function __construct(public Project $model) {}

    public function searchDdl()
    {
        return $this->model->select('id', 'name')->get();
    }

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with(['clients'])
            ->when(isset($filters['search']), function ($query) use ($filters) {
                return $query->where('name', 'like', "%{$filters['search']}%");
            })->when(isset($filters['status']), function ($query) use ($filters) {
                return $query->where('status', $filters['status']);
            })->when(isset($filters['type']), function ($query) use ($filters) {
                return $query->where('type', $filters['type']);
            })->when(isset($filters['size']), function ($query) use ($filters) {
                return $query->where('size', $filters['size']);
            })->when(isset($filters['payment_method']), function ($query) use ($filters) {
                return $query->where('payment_method_id', $filters['payment_method']);
            })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $dates = [
                    Carbon::parse($filters['date_from'])->startOfDay(),
                    Carbon::parse($filters['date_to'] ?? null)->endOfDay(),
                ];

                return $query->whereBetween('created_at', $dates);
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function findById(string $id)
    {
        return $this->model
            ->with(['users', 'clients', 'paymentMethod', 'package'])
            ->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function getAll(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }

    public function updateBalance($project_id, $balance, $operator)
    {
        $user = $this->model->find($project_id);
        if ($operator == '+') {
            $user->balance += $balance;
        } else {
            $user->balance -= $balance;
        }
        $user->save();

        return $user;
    }
}
