<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class OfficeDue extends Model
{
    protected $fillable = [
        'project_id',
        'project_expenses',
        'office_ratio',
        'total_profits',
        'recieved_profits',
        'due_profits',
    ];

    /**
     * Get the project associated with the office due.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
