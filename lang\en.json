{"The credentials must be a valid email address or phone number.": "The credentials must be a valid email address or phone number.", "This Email Or Phone Does Not Exist": "This Email Or Phone Does Not Exist", "Your credentials are wrong": "Your credentials are wrong", "Logged In Successfully": "Logged In Successfully", "This Email Does Not Exist": "This Email Does Not Exist", "Your password reset code has been sent to your email": "Your password reset code has been sent to your email", "This Code Is Invalid": "This Code Is Invalid", "Your Code Is Valid": "Your Code Is Valid", "Your password has been reset successfully": "Your password has been reset successfully", "Logged Out Successfully": "Logged Out Successfully", "Created Successfully": "Created Successfully", "Updated Successfully": "Updated Successfully", "Deleted Successfully": "Deleted Successfully", "Restored Successfully": "Restored Successfully", "The :attribute field must be a valid Google Maps URL.": "The :attribute field must be a valid Google Maps URL.", "Password generated successfully": "Password generated successfully", "Deactivated Successfully": "Deactivated Successfully", "Notification Sent Successfully": "Notification Sent Successfully", "Notification Resent Successfully": "Notification Resent Successfully", "Total cannot be less than the remaining amount.": "Total cannot be less than the remaining amount.", "Your Password Has Been Updated Successfully": "Your Password Has Been Updated Successfully", "Sorry, you cannot renew at the moment. Please wait until the last month before the end date.": "Sorry, you cannot renew at the moment. Please wait until the last month before the end date.", "Admin creation failed. Please check your input.": "Admin creation failed. Please check your input.", "Admin update failed. Please check your input.": "Admin update failed. Please check your input.", "Message sent successfully.": "Message sent successfully.", "You cannot add a custom domain to this office": "You cannot add a custom domain to this office", "The new password cannot be the same as the old password.": "The new password cannot be the same as the old password.", "Your password should not contain personal information such as your name, email, or phone number.": "Your password should not contain personal information such as your name, email, or phone number.", "You have reached the resend limit. Please try again in 12 hours.": "You have reached the resend limit. Please try again in 12 hours.", "Please wait 1 minutes before requesting another reset code.": "Please wait 1 minutes before requesting another reset code.", "You have exceeded your attempts to verify reset code, please wait 15 minutes to try again": "You have exceeded your attempts to verify reset code, please wait 15 minutes to try again", "Site Engineer": "Site Engineer", "Office Engineer": "Office Engineer", "Project Manager": "Project Manager", "Accountant": "Accountant", "Admin": "Admin", "You have reached your subscription limit, you cannot add a new :name": "You have reached your subscription limit, you cannot add a new :name", "Your current password is incorrect": "Your current password is incorrect", "This code is not correct": "This code is not correct", "Too many failed attempts, please resend a new reset code": "Too many failed attempts, please resend a new reset code", "This Code Is Expired": "This Code Is Expired", "The domain :value is already taken": "The domain :value is already taken", "You cannot delete the domain": "You cannot delete the domain", "Project": "Project", "Project not found": "Project not found", "Paid amount must be less than total amount": "Paid amount must be less than total amount", "You cannot delete this role.": "You cannot delete this role.", "You cannot assign this role to an admin.": "You cannot assign this role to an admin.", "You cannot delete a plan that has active subscriptions.": "You cannot delete a plan that has active subscriptions.", "You cannot delete this account, please pay off the debts first.": "You cannot delete this account, please pay off the debts first.", "You cannot assign this role.": "You cannot assign this role.", "Balance Updated Successfully": "Balance Updated Successfully", "No receivers found for this notification.": "No receivers found for this notification.", "You cannot update your balance.": "You cannot update your balance.", "You cannot pay more than the remaining amount.": "You cannot pay more than the remaining amount.", "Paid amount cannot be greater than total debt": "Paid amount cannot be greater than total debt", "Debt already paid": "Debt already paid", "You cannot delete this account, please transfer the balance first.": "You cannot delete this account, please transfer the balance first.", "This user has no balance to transfer.": "This user has no balance to transfer.", "Balance Transferred Successfully": "Balance Transferred Successfully", "The amount must be equal to or less than the total debt.": "The amount must be equal to or less than the total debt.", "There is no debt to pay.": "There is no debt to pay.", "Withdrawal approved (Unlimited Spending).": "Withdrawal approved (Unlimited Spending).", "Withdrawals are not allowed.": "Withdrawals are not allowed.", "Withdrawal approved.": "<PERSON><PERSON><PERSON> approved.", "Insufficient balance for withdrawal.": "Insufficient balance for withdrawal.", "Cannot change payment method while project statements exist.": "Cannot change payment method while project statements exist.", "You can only upload :count more images": "You can only upload :count more images", "model.Office": "Office", "model.Project": "Project", "model.Refund": "Refund", "model.Purchase": "Purchase", "model.Partner": "Partner", "model.User": "User", "model.Admin": "Admin", "model.Role": "Role", "model.Plan": "Plan", "model.Subscription": "Subscription", "model.Tenant": "Tenant", "model.GroupNotification": "Group Notification", "model.MessageNotification": "Message Notification", "model.DebtPayment": "Debt Payment", "model.History": "History", "model.ProjectPayment": "Project Payment", "model.PaymentCertificate": "Payment Certificate", "model.OfficeDue": "Office Due", "model.OfficeDueTransaction": "Office Due Transaction", "model.ProjectAttachment": "Project Attachment", "model.Task": "Task", "model.TaskHistory": "Task History", "model.DeviceToken": "<PERSON><PERSON>", "model.Item": "<PERSON><PERSON>", "model.Media": "Media", "model.NotificationReceiver": "Notification Receiver", "model.PartnerItem": "Partner Item", "model.PasswordResetCode": "Password Reset Code", "model.AdminProject": "Admin Project", "model.CertificationItem": "Certification Item", "model.Client": "Client", "model.ClientProject": "Client Project", "model.ContractorPayment": "Contractor Payment", "model.Debt": "Debt", "model.InvoiceCategory": "Invoice Category", "model.Message": "Message", "model.MessageUserRead": "Message User Read", "model.Package": "Package", "model.PaymentMethod": "Payment Method", "model.ProjectUser": "Project User", "model.PurchaseItem": "Purchase Item", "model.RefundItem": "Refund Item", "model.Report": "Report", "model.Room": "Room", "model.RoomUser": "Room User", "model.Setting": "Setting", "model.Supply": "Supply", "model.SupplyItem": "Supply Item", "model.Transaction": "Transaction", "model.Unit": "Unit", "model.UserBalance": "User Balance"}