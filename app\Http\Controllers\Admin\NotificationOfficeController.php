<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationOfficeResource;
use App\Services\AdminService;
use App\Services\OfficeService;

class NotificationOfficeController extends Controller
{
    public function __construct(public OfficeService $officeService, public AdminService $adminService) {}

    public function __invoke()
    {
        $offices = $this->officeService->ddl();
        $admins = $this->adminService->ddl();

        return success(
            [
                'offices' => NotificationOfficeResource::collection($offices),
                'admins' => NotificationOfficeResource::collection($admins),
            ]
        );
    }
}
