<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateRefundRequest;
use App\Http\Requests\Tenant\Admin\PayRefundRequest;
use App\Http\Requests\Tenant\Admin\UpdateRefundRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\RefundsDetailsResource;
use App\Http\Resources\Tenant\Admin\RefundsResource;
use App\Services\Tenant\RefundService;

class RefundController extends Controller
{
    public function __construct(public RefundService $refundService) {}

    public function index()
    {
        $refundes = $this->refundService->get();

        return success(new BaseCollection($refundes, RefundsResource::class, [
            'total_refunds' => $this->refundService->getTotal(),
        ]));
    }

    public function show(string $id)
    {
        $purchase = $this->refundService->getById($id);

        return success(new RefundsDetailsResource($purchase));
    }

    public function store(CreateRefundRequest $request)
    {
        return $this->refundService->create($request->validated());
    }

    public function update(UpdateRefundRequest $request, $id)
    {
        return $this->refundService->update($request->validated(), $id);
    }

    public function destroy(string $id)
    {
        return $this->refundService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->refundService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->refundService->forceDelete($id);
    }

    public function deleteMedia(string $projectId, string $mediaId)
    {
        return $this->refundService->deleteMedia($projectId, $mediaId);
    }

    public function pay(PayRefundRequest $request, string $id)
    {
        return $this->refundService->pay($request->validated(), $id);
    }
}
