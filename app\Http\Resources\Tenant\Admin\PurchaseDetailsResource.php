<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'number' => $this->number,
            'name' => $this->name,
            'type' => $this->type,
            'date' => $this->date->format('Y-m-d'),
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
            'partner' => [
                'id' => $this->partner->id,
                'name' => $this->partner->name,
            ],
            'notes' => $this->notes,
            'media' => MediaResource::collection($this->media),
            'categories' => PurchaseCategoryResource::collection($this->invoiceCategory),
            'summary' => [
                'total_before_discount' => $this->total_before_discount,
                'discount_type' => $this->discount_type,
                'discount_amount' => $this->discount_amount,
                'total_after_discount' => $this->total_after_discount,
                'paid_amount' => $this->paid_amount,
                'remaining_amount' => $this->remaining_amount,
                'status' => $this->status,
            ],
        ];
    }
}
