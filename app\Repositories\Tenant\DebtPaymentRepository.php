<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\DebtPayment;

class DebtPaymentRepository
{
    public function __construct(protected DebtPayment $model)
    {
        //
    }

    public function getPurchasesDebtPayments(string $debtId, int $limit = 10)
    {
        return $this->model
            ->with('debt', 'user')
            ->whereHas('debt', function ($query) use ($debtId) {
                $query->where('id', $debtId)
                    ->where('type', 'purchase');
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getRefundsDebtPayments(string $debtId, int $limit = 10)
    {
        return $this->model
            ->with(['debt', 'user'])
            ->whereHas('debt', function ($query) use ($debtId) {
                $query->where('id', $debtId)
                    ->where('type', 'refund');
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getContractorDebtPayments(string $debtId, int $limit = 10)
    {
        return $this->model
            ->with('debt', 'user')
            ->whereHas('debt', function ($query) use ($debtId) {
                $query->where('id', $debtId)
                    ->where('type', 'certification');
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }
}
