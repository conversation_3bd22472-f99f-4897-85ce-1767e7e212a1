<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use App\Http\Resources\Tenant\Admin\UserListResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

class UserCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return $this->collection
            ->groupBy('type')
            ->map(fn($group) => UserListResource::collection($group))
            ->toArray();
    }
}
