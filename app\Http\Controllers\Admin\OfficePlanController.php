<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\OfficePlanResource;
use App\Services\PlanService;

class OfficePlanController extends Controller
{
    public function __construct(public PlanService $planService) {}

    public function __invoke()
    {
        $plans = $this->planService->ddl();

        return success(OfficePlanResource::collection($plans));
    }
}
