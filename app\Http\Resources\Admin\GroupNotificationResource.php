<?php

namespace App\Http\Resources\Admin;

use App\Models\Admin;
use App\Models\Office;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GroupNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sender_name' => $this->sender_name,
            'title' => $this->title,
            'body' => $this->body,
            'created_at' => $this->created_at->format('d/m/Y - h:i A'),
            'receivers' => [
                'offices' => NotificationReceiverResource::collection($this->notification_receivers->where('receiverable_type', Office::class)),
                'admins' => NotificationReceiverResource::collection($this->notification_receivers->where('receiverable_type', Admin::class)),
            ],
        ];
    }
}
