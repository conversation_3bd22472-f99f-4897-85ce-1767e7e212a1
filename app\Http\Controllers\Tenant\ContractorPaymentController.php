<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateContractorPaymentRequest;
use App\Http\Requests\Tenant\Admin\UpdateContractorPaymentRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\ContractorPaymentResource;
use App\Services\Tenant\ContractorPaymentService;

class ContractorPaymentController extends Controller
{
    public function __construct(protected ContractorPaymentService $contractorPaymentService)
    {
        //
    }

    public function index()
    {
        $contractorPayments = $this->contractorPaymentService->get();

        $totalPayments = $this->contractorPaymentService->getTotalPayment();

        return success(new BaseCollection($contractorPayments, ContractorPaymentResource::class, [
            'total_payments' => $totalPayments,
        ]));
    }

    public function store(CreateContractorPaymentRequest $request)
    {
        return $this->contractorPaymentService->create($request->validated());
    }

    public function show(string $id)
    {
        $contractorPayment = $this->contractorPaymentService->getById($id);

        return success(new ContractorPaymentResource($contractorPayment));
    }

    public function update(UpdateContractorPaymentRequest $request, string $id)
    {
        return $this->contractorPaymentService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->contractorPaymentService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->contractorPaymentService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->contractorPaymentService->forceDelete($id);
    }
}
