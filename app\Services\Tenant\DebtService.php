<?php

namespace App\Services\Tenant;

use App\Repositories\Tenant\DebtRepository;

class DebtService
{
    public function __construct(protected DebtRepository $debtRepo)
    {
        //
    }

    public function getPurchaseDebts(string $projectId)
    {
        $filterable = request()->only(['search']);

        return $this->debtRepo->getPurchaseDebts($projectId, $filterable);
    }

    public function getRefundDebts(string $projectId)
    {
        $filterable = request()->only(['search']);

        return $this->debtRepo->getRefundDebts($projectId, $filterable);
    }

    public function getContractorDebts(string $projectId)
    {
        $filterable = request()->only(['search']);

        return $this->debtRepo->getContractorDebts($projectId, $filterable);
    }

    public function getTotalDebts(string $projectId, string $type)
    {
        return $this->debtRepo->totalDebts($projectId, $type);
    }
}
