<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Project extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $fillable = [
        'name',
        'description',
        'status',
        'type',
        'size',
        'area',
        'payment_method_id',
        'package_id',
        'office_ratio',
        'site_engineer_ratio',
        'amount_per_meter',
        'total',
        'withdraw_limit',
        'start_date',
        'end_date',
        'balance',
    ];

    public function getImageAttribute()
    {
        return $this->getFirstMedia('projects');
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

   

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_user');
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class, 'client_project')
            ->withTimestamps();
    }

    public function room()
    {
        return $this->hasOne(Room::class);
    }

    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    public function refunds()
    {
        return $this->hasMany(Refund::class);
    }

    public function certificates()
    {
        return $this->hasMany(PaymentCertificate::class);
    }

    public function Payments()
    {
        return $this->hasMany(ProjectPayment::class);
    }

    public function asCreditor()
    {
        return $this->morphOne(Debt::class, 'creditor');
    }

    public function asDebtor()
    {
        return $this->morphOne(Debt::class, 'debtor');
    }

    public function debts()
    {
        return $this->morphMany(Debt::class, 'debtor');
    }

    public function officeDue()
    {
        return $this->hasOne(OfficeDue::class, 'project_id');
    }


    

    public function officeDueTransactions()
    {
        return $this->hasMany(OfficeDueTransaction::class, 'project_id');
    }

    public function getHasProjectStatementsAttribute(): bool
    {
        return $this->purchases()->exists() ||
               $this->refunds()->exists() ||
               $this->payments()->exists() ||
               $this->certificates()->exists();
    }
    
}
