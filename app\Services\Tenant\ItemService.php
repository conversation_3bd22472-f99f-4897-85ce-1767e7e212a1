<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ItemData;
use App\Repositories\Tenant\ItemRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ItemService
{
    public function __construct(
        public ItemRepository $itemRepository,

    ) {}

    // List paginated items
    public function index()
    {
        return $this->itemRepository->getPaginated();
    }

    // Show a specific item by ID
    public function show($id)
    {
        $client = $this->itemRepository->findById($id);

        if (! $client) {
            throw new NotFoundHttpException;
        }

        return $client;
    }

    // Create a new item
    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            if (request()->hasFile('image')) {
                $request['image'] = request()->file('image')->store('admins', 'public');
            }

            $data = ItemData::from($request)->all();

            $item = $this->itemRepository->create($data);

            DB::commit();

            return $item;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'item creation failed. Please check your input.');
        }
    }

    // Update an existing item
    public function update($id, array $request)
    {
        DB::beginTransaction();

        $item = $this->itemRepository->findById($id);

        if (! $item) {
            throw new NotFoundHttpException;
        }

        try {
            $data = ItemData::from($request)->all();

            $this->itemRepository->update($item, $data);

            DB::commit();

            return $item;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'item update failed. Please check your input.');
        }
    }

    // Delete a item
    public function delete($id)
    {
        DB::beginTransaction();
        $item = $this->itemRepository->findById($id);

        if (! $item) {
            throw new NotFoundHttpException;
        }
        try {
            $this->itemRepository->delete($item);

            DB::commit();

            return response()->json(['message' => 'item deleted successfully']);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'item deletion failed.');
        }
    }

    public function ddl(?array $columns = ['id', 'name'])
    {
        return $this->itemRepository->getAll($columns);
    }
}
