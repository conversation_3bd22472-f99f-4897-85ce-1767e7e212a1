<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\OfficeDueTransaction;

class OfficeDueTransactionRepository
{
    public function __construct(protected OfficeDueTransaction $model)
    {
        //
    }

    public function getPaginated(int $limit = 10)
    {
        $query = $this->model->orderByDesc('id');

        if (request()->has('project_id')) {
            $query->where('project_id', request()->project_id);
        }

        return $query->paginate($limit);
    }

   


    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }
}
