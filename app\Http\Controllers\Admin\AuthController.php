<?php

namespace App\Http\Controllers\Admin;

use App\Mail\PasswordReset;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Http\Resources\AdminResource;
use App\Repositories\AdminRepository;
use Illuminate\Support\Facades\Password;
use App\Http\Requests\Admin\Auth\LoginRequest;
use App\Http\Resources\Admin\PermissionResource;
use App\Http\Requests\Admin\Auth\VerifyCodeRequest;
use App\Http\Requests\Admin\Auth\ResetPasswordRequest;
use App\Http\Requests\Admin\Auth\ForgetPasswordRequest;

class AuthController extends Controller
{
    public function __construct(public AdminRepository $adminRepo) {}

    public function login(LoginRequest $request)
    {
        $admin = $this->adminRepo->findByEmailOrPhone($request->emailOrPhone);

        if (! $admin) {
            return error(__('This Email Or Phone Does Not Exist'), 404);
        }

        if (! Hash::check($request->password, $admin->password)) {
            return error(__('Your credentials are wrong'), 401);
        } else {

            if ($request->device_id && $request->token) {
                $admin->updateDeviceToken([
                    'device_id' => $request->device_id,
                    'token' => $request->token,
                ]);
            }

            $token = $admin->createToken('auth_token')->plainTextToken;

            return success([
                'token' => $token,
                'admin' => new AdminResource($admin),
            ]);
        }
    }

    public function forgetPassword(ForgetPasswordRequest $request)
    {
        $admin = $this->adminRepo->findByEmailOrPhone($request->email);

        if (! $admin) {
            return error(__('This Email Does Not Exist'), 404);
        }

        // Check for recent reset code
        $lastResetCode = $admin->passwordResetCode()
            ->where('updated_at', '>=', now()->subMinutes(1))
            ->exists();

        if ($lastResetCode) {
            return error(__('Please wait 1 minutes before requesting another reset code.'), 400);
        }

        // Reset resend attempts after 12 hours
        $resetCode = $admin->passwordResetCode()->firstOrCreate(
            [
                'resettable_id' => $admin->id,
                'resettable_type' => get_class($admin),
            ],
            [
                'code' => random_int(1000, 9999),
                'expires_at' => now()->addMinutes(10),
                'resend_attempts' => 0,
                'last_resend_attempt_at' => now(),
            ]
        );

        if ($resetCode->resend_attempts >= 3 && $resetCode->last_resend_attempt_at->diffInHours(now()) < 12) {
            return error(__('You have reached the resend limit. Please try again in 12 hours.'), 400);
        }

        if ($resetCode->last_resend_attempt_at->diffInHours(now()) >= 12) {
            $resetCode->update([
                'resend_attempts' => 0,
            ]);
        }

        // Update the reset code details
        $resetCode->update([
            'code' => random_int(1000, 9999),
            'expires_at' => now()->addMinutes(10),
            'resend_attempts' => $resetCode->resend_attempts + 1,
            'last_resend_attempt_at' => now(),
        ]);

        // Update admin attempts
        $admin->update(['check_code_attempts' => 5]);

        // Generate the password reset token
        $broker = Password::broker('admins');
        $token = $broker->createToken($admin);

        // Include the token in the email
        try {
            Mail::to($admin->email)->send(new PasswordReset($resetCode->code));
        } catch (Exception $e) {
            logger($e);
        }

        return success([
            'reset_code' => $resetCode->code,
            'reset_token' => $token,
        ]);
    }

    public function verifyResetCode(VerifyCodeRequest $request)
    {
        $admin = $this->adminRepo->findByEmailOrPhone($request->email);

        if (! $admin) {
            return error(__('This Email Does Not Exist'), 404);
        }

        $resetCode = $admin->passwordResetCode()->where('code', $request->code)->first();
        $isExpired = $resetCode && now()->gte($resetCode->expires_at);
        $outOfAttempts = $admin->check_code_attempts == 0;

        if ((! $resetCode || $isExpired) && $outOfAttempts) {
            return error(__('Too many failed attempts, please resend a new reset code'), 429);
        }

        if (! $resetCode || $isExpired) {
            if ($admin->check_code_attempts > 0) {
                $admin->decrement('check_code_attempts');
            }

            return error($isExpired ? __('This Code Is Expired') : __('This Code Is Invalid'), 404);
        }

        return success(__('Your Code Is Valid'));
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $admin = $this->adminRepo->findByEmailOrPhone($request->email);

        if (! $admin) {
            return error(__('This Email Does Not Exist'), 404);
        }

        // Validate the reset token
        $isValidToken = Password::broker('admins')->tokenExists($admin, $request->token);

        if (! $isValidToken) {
            return error(__('This Token Is Invalid or Expired'), 404);
        }

        // Check if the new password is the same as the old password
        if (Hash::check($request->new_password, $admin->password)) {
            return error(__('The new password cannot be the same as the old password.'), 422);
        }

        // Update the password
        $admin->update([
            'password' => Hash::make($request->new_password),
            'check_code_attempts' => null,
        ]);

        // Delete the password reset token and reset code
        Password::broker('admins')->deleteToken($admin);
        $admin->passwordResetCode()->delete();

        return success(__('Your Password Has Been Updated Successfully'));
    }

    public function logout()
    {
        auth()->user()->currentAccessToken()->delete();

        return success(__('Logged Out Successfully'));
    }

    public function userPermissions()
    {
        $user = auth()->user();

        return success(PermissionResource::collection($user->permissions->groupBy('group')));
    }

    public function getRemainingAttempts(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = $this->adminRepo->findByEmailOrPhone($request->email);

        if (! $user) {
            return error(__('This Email Does Not Exist'), 404);
        }

        $checkCodeAttempts = (int) $user->check_code_attempts > 0 || $user->check_code_attempts === null;
        $resetCode = $user->passwordResetCode;
        $resendCodeAttempts = !($resetCode && $resetCode->resend_attempts >= 3 && $resetCode->last_resend_attempt_at->diffInHours(now()) < 12);

        return success([
            'has_check_code_attempts' => $checkCodeAttempts,
            'has_resend_code_attempts' => $resendCodeAttempts,
        ]);
    }
}
