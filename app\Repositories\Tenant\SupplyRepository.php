<?php

declare(strict_types=1);

namespace App\Repositories\Tenant;

use App\Models\Tenant\Supply;
use Carbon\Carbon;

class SupplyRepository
{
    public function __construct(protected Supply $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with('partner')
            ->where('project_id', request()->project)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                return $query->where(function ($query) use ($filters) {
                    $query->where('name', 'like', "%{$filters['search']}%")
                        ->orWhere('created_by', 'like', "%{$filters['search']}%");
                });
            })->when(isset($filters['partner']), function ($query) use ($filters) {
                return $query->where('partner_id', $filters['partner']);
            })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $dates = [
                    Carbon::parse($filters['date_from'])->startOfDay(),
                    Carbon::parse($filters['date_to'] ?? null)->endOfDay(),
                ];

                return $query->whereBetween('created_at', $dates);
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getTrashedWithPaginate(int $limit = 10)
    {
        return $this->model->onlyTrashed()->where('permanent_deleted', false)->orderByDesc('id')->paginate($limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findById($id)
    {
        return $this->model->with(['partner', 'items'])->find($id);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->with(['partner', 'items'])->find($id);
    }
}
