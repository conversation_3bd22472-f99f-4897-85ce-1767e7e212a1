<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfficeDueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $paymentMethodId = $this->project->paymentMethod->id;

        return [
            'id' => $this->id,
            'payment_method' => [
                'id' => $paymentMethodId,
                'payment_method' => $this->project->paymentMethod->name
            ],
            'project_expenses' => $this->project_expenses,
            'project_payments' => $this->project->payments->sum('amount'),
            'project_total' => $this->project->total,
            'office_ratio' => $paymentMethodId == 1 ? $this->office_ratio : null,
            'total_profits' => $this->total_profits,
            'recieved_profits' => $this->recieved_profits,
            'due_profits' => $this->due_profits,
            'remaining' => $this->project->officeDueTransactions()->latest()->first()->remaining ?? $this->due_profits
        ];
    }
}
