<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\ProjectAttachment;

class ProjectAttachmentRepository
{
    public function __construct(public ProjectAttachment $model) {}

    public function index()
    {
        return $this->model->all();
    }

    public function getPaginated(int $limit = 10, array $filters = [])
    {
        return $this->model->where('project_id', request()->project)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                return $query->where('name', 'like', "%{$filters['search']}%");
            })
            ->when(isset($filters['type']), function ($query) use ($filters) {
                return $query->where('type', 'like', "%{$filters['type']}%");
            })
            ->when(isset($filters['item']), function ($query) use ($filters) {
                return $query->when(isset($filters['item']), function ($query) use ($filters) {
                    $query->whereHas('item', function ($historyQuery) use ($filters) {
                        $historyQuery->where('name_' . app()->getLocale(), 'like', "%{$filters['item']}%");
                    });
                });
            })
            ->when(isset($filters['from_date']) && isset($filters['to_date']), function ($query) use ($filters) {
                return $query->whereBetween('created_at', [$filters['from_date'], $filters['to_date']]);
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function getTrashedWithPaginate($limit = 10, array $filters = [])
    {
        return $this->model->when(isset($filters['attach_type']), function ($query) use ($filters) {
            return $query->where('type', 'like', "%{$filters['attach_type']}%");
        })->where('permanent_deleted', 0)->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findTrashedById($id)
    {
        return $this->model->where('permanent_deleted', 0)->onlyTrashed()->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(array $data)
    {
        return $this->model->update($data);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }
}
