<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'partner' => $this->partner?->name,
            'date' => $this->date,
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
            'created_by' => $this->created_by,
            'archived_by' => $this->archived_by,
        ];
    }
}
