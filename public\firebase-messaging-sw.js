importScripts("https://www.gstatic.com/firebasejs/7.19.0/firebase-app.js");
importScripts(
    "https://www.gstatic.com/firebasejs/7.19.0/firebase-messaging.js",
);
// For an optimal experience using Cloud Messaging, also add the Firebase SDK for Analytics.

// Initialize the Firebase app in the service worker by passing in the
// messagingSenderId.
var firebaseConfig = {
    apiKey: "AIzaSyBSmzkPYClvTO8GhKTKfUs7tb6PuBtRK34",
    authDomain: "engsaas-de75d.firebaseapp.com",
    projectId: "engsaas-de75d",
    storageBucket: "engsaas-de75d.firebasestorage.app",
    messagingSenderId: "547001759178",
    appId: "1:547001759178:web:4612bedc0dd96bd6668b33"
};

firebase.initializeApp(firebaseConfig)

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();
messaging.usePublicVapidKey("BGhzg-qcJ9pr0Et6t6nl4E5ia2WrrE3RbItTbX97uY5d-2lOajCX5qkYdngyCkSj9fRpa-bmCj_KHCRP6fx7YPM");

messaging.setBackgroundMessageHandler(function (payload) {
    console.log(
        "[firebase-messaging-sw.js] Received background message ",
        payload,
    );
    // Customize notification here
    const notificationTitle = "Notification";
    const notificationOptions = {
        body: "Please check, you have notification.",
    };

    return self.registration.showNotification(
        notificationTitle,
        notificationOptions,
    );
});
