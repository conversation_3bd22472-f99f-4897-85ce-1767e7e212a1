<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\UnitData;
use App\Repositories\Tenant\UnitRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UnitService
{
    public function __construct(
        public UnitRepository $unitRespository,

    ) {}

    // List paginated units
    public function index()
    {
        return $this->unitRespository->getPaginated();
    }

    // Show a specific unit by ID
    public function show($id)
    {
        $client = $this->unitRespository->findById($id);

        if (! $client) {
            throw new NotFoundHttpException;
        }

        return $client;
    }

    // Create a new unit
    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = UnitData::from($request)->all();

            $unit = $this->unitRespository->create($data);

            DB::commit();

            return $unit;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'unit creation failed. Please check your input.');
        }
    }

    // Update an existing unit
    public function update($id, array $request)
    {
        DB::beginTransaction();

        $unit = $this->unitRespository->findById($id);

        if (! $unit) {
            throw new NotFoundHttpException;
        }

        try {
            $data = UnitData::from($request)->all();

            $this->unitRespository->update($unit, $data);

            DB::commit();

            return $unit;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'unit update failed. Please check your input.');
        }
    }

    // Delete a unit
    public function delete($id)
    {
        DB::beginTransaction();
        $unit = $this->unitRespository->findById($id);

        if (! $unit) {
            throw new NotFoundHttpException;
        }
        try {
            $this->unitRespository->delete($unit);

            DB::commit();

            return response()->json(['message' => 'unit deleted successfully']);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'unit deletion failed.');
        }
    }

    public function ddl(?array $columns = ['id', 'name'])
    {
        return $this->unitRespository->getAll($columns);
    }
}
