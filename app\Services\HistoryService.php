<?php

namespace App\Services;

use App\Repositories\HistoryRepository;

class HistoryService
{
    public function __construct(public HistoryRepository $historyRepository) {}

    public function get()
    {
        $filterable = request()->only(['search', 'date_from', 'date_to']);

        return $this->historyRepository->getPaginated($filterable);
    }

    public function getLimit(int $limit = 5)
    {
        return $this->historyRepository->limit($limit);
    }
}
