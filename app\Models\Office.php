<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Stancl\Tenancy\Database\Models\Domain;

class Office extends Model implements HasMedia
{
    use Notifiable, SoftDeletes , InteractsWithMedia;

    protected $fillable = [
        'name_ar',
        'name_en',
        'phone',
        'email',
        'password',
        'address',
        'location',
        'work_hour_from',
        'work_hour_to',
        'work_day_from',
        'work_day_to',
        'description',
        'status',
        'apperance_color',
    ];

    protected function casts(): array
    {
        return [
            'password' => 'hashed',
            'location' => 'array',
        ];
    }

    public function getNameAttribute()
    {
        return $this->{'name_'.app()->getLocale()};
    }

    public function getImageAttribute()
    {
        return $this->media->first();
    }

    public function subscription()
    {
        return $this->hasOne(Subscription::class);
    }

    public function receivers()
    {
        return $this->morphMany(NotificationReceiver::class, 'receiverable');
    }

    public function deviceTokens()
    {
        return $this->morphMany(DeviceToken::class, 'tokenable');
    }

    public function tenant()
    {
        return $this->hasOne(Tenant::class);
    }

    public function domains()
    {
        return $this->hasManyThrough(Domain::class, Tenant::class);
    }

  
}
