<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Unit;

class UnitRepository
{
    public function __construct(public Unit $model) {}

    public function getPaginated(int $limit = 10)
    {
        return $this->model
            ->when(request('search'), function ($query) {
                $searchTerm = '%'.request('search').'%';

                return $query->where(function ($subQuery) use ($searchTerm) {
                    $subQuery->where('name_ar', 'like', $searchTerm)
                        ->orWhere('name_en', 'like', $searchTerm);
                });
            })
            ->when(request('status'), function ($query) {
                return $query->where('status', request('status'));
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(Unit $unit, array $data)
    {

        $unit->update($data);
    }

    public function delete(Unit $unit)
    {
        $unit->delete();
    }

    public function getAll(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }
}
