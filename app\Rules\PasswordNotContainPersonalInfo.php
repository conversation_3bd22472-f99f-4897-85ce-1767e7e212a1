<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class PasswordNotContainPersonalInfo implements Rule
{
    protected $personalInfo;

    /**
     * Create a new rule instance.
     */
    public function __construct(array $personalInfo)
    {
        $this->personalInfo = $personalInfo;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        foreach ($this->personalInfo as $info) {
            if (! empty($info) && str_contains($value, $info)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('Your password should not contain personal information such as your name, email, or phone number.');
    }
}
