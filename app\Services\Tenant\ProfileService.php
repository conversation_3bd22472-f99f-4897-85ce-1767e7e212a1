<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ProfileData;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\Tenant\ProfileResource;


class ProfileService
{
    public function get()
    {
        return success(new ProfileResource(auth()->user()));
    }

    public function update(array $request)
    {
        $user = auth()->user();

        $data = ProfileData::from($request)->all();

        $user->update($data);

        if (request()->hasFile('image')) {
            // Clear existing images in the 'projects' collection
            $user->clearMediaCollection('users');

            $user->addMedia(request()->image)
                ->toMediaCollection('users');
        }

        return success(new ProfileResource($user->refresh()));
    }

    public function updatePassword(array $request)
    {
        $admin = auth()->user();

        if (! Hash::check($request['current_password'], $admin->password)) {
            return error(__('Your current password is incorrect'));
        }

        $admin->update([
            'password' => Hash::make($request['new_password']),
        ]);

        return success(__('Updated Successfully'));
    }
}
