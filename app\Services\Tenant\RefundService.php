<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\RefundData;
use App\Models\Tenant\Partner;
use App\Models\Tenant\RefundItem;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\DebtPaymentData;
use App\Repositories\Tenant\RefundRepository;
use App\Repositories\Tenant\PartnerRepository;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\PurchaseRepository;
use App\Repositories\Tenant\DebtPaymentRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\Tenant\Traits\HasSufficientBalanceCheck;
use App\Services\Tenant\TransactionMessageService;

class RefundService
{
    use HasSufficientBalanceCheck;
    // Dependencies
    protected RefundRepository $refundRepository;
    protected ProjectRepository $projectRepo;
    protected PurchaseRepository $purchaseRepo;
    protected DebtPaymentRepository $debtPaymentRepo;
    protected PartnerRepository $partnerRepository;

    public function __construct(
        RefundRepository $refundRepository,
        ProjectRepository $projectRepo,
        PurchaseRepository $purchaseRepo,
        DebtPaymentRepository $debtPaymentRepo,
        PartnerRepository $partnerRepository
    ) {
        $this->refundRepository = $refundRepository;
        $this->projectRepo = $projectRepo;
        $this->purchaseRepo = $purchaseRepo;
        $this->debtPaymentRepo = $debtPaymentRepo;
        $this->partnerRepository = $partnerRepository;
    }


    // Public API
    public function get()
    {
        $filterable = request()->only(['search', 'status', 'date']);
        return request()->type === 'archive'
            ? $this->refundRepository->getTrashedWithPaginate()
            : $this->refundRepository->getPaginated($filterable);
    }

    // --- Project Debt ---
    private function calcProjectDebt(array $request, array $refundAmounts, ?int $oldSupplierId = null, array $oldRefundAmounts = [])
    {
        $projectId = $request['project_id'] ?? null;
        if (!$projectId) {
            throw new \Exception('Project ID is required for debt calculation');
        }

        $project = $this->projectRepo->findById($projectId);

        $debt = $project->asCreditor()->where('debtor_id', $request['partner_id'])->firstOrCreate([
            'debtor_id' => $request['partner_id'],
            'debtor_type' => Partner::class,
            'type' => 'refund',
        ]);

        // If there's an old partner, recalculate for them
        if ($oldSupplierId) {
            $oldDebt = $project->asCreditor()->where(function ($query) use ($oldSupplierId) {
                $query->where('type', 'refund')
                    ->where('debtor_id', $oldSupplierId);
            })->first();

            if ($oldDebt) {
                $oldDebt->update([
                    'total_invoices' => $oldDebt->total_invoices - $oldRefundAmounts['total_after_discount'],
                    'total_payments' => $oldDebt->total_payments - $oldRefundAmounts['paid_amount'],
                    'total_debt' => $oldDebt->total_debt - ($oldRefundAmounts['total_after_discount'] - $oldRefundAmounts['paid_amount']),
                ]);
            }
        }

        $debt->update([
            'total_invoices' => $debt->total_invoices + $refundAmounts['total_after_discount'],
            'total_payments' => $debt->total_payments + $refundAmounts['paid_amount'],
            'total_debt' => $debt->total_debt + ($refundAmounts['total_after_discount'] - $refundAmounts['paid_amount']),
        ]);
    }

    // --- Payment Status ---
    protected function calculatePaymentStatus(array &$request)
    {
        // Ensure total_after_discount exists and is valid
        $totalAfterDiscount = $request['total_after_discount'] ?? 0;

        // Handle payment status based on the provided status or calculate from amounts
        switch ($request['status'] ?? 'unpaid') {
            case 'paid':
                $request['paid_amount'] = $totalAfterDiscount;
                $request['remaining_amount'] = 0;
                break;
            case 'partial':
                $paidAmount = $request['paid_amount'] ?? 0;
                $request['paid_amount'] = min($paidAmount, $totalAfterDiscount);
                $request['remaining_amount'] = max(0, $totalAfterDiscount - $request['paid_amount']);
                break;
            default: // 'unpaid'
                $request['paid_amount'] = 0;
                $request['remaining_amount'] = $totalAfterDiscount;
                $request['status'] = 'unpaid';
                break;
        }

        // Auto-adjust status based on calculated amounts
        if ($request['paid_amount'] >= $totalAfterDiscount && $totalAfterDiscount > 0) {
            $request['status'] = 'paid';
            $request['remaining_amount'] = 0;
        } elseif ($request['paid_amount'] > 0 && $request['paid_amount'] < $totalAfterDiscount) {
            $request['status'] = 'partial';
        } elseif ($request['paid_amount'] <= 0) {
            $request['status'] = 'unpaid';
        }
    }


    // --- Refund Creation ---
    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $user = auth()->user();

            // Validate required fields
            if (empty($request['project_id'])) {
                throw new \Exception('Project ID is required for refund creation');
            }

            $request['number'] = generateInvoiceNumber();

            // Ensure partner_id is set in request for DTO
            if (isset($request['type']) && $request['type'] === 'invoice') {
                $purchase = $this->purchaseRepo->findById($request['purchase_id'] ?? null);
                if (! $purchase) {
                    throw new \Exception("Purchase not found for refund creation");
                }
                $request['partner_id'] = $purchase->partner_id;
            }

            // Check sufficient balance for paid_amount
            $this->assertSufficientBalance(
                'refund',
                $request['paid_amount'] ?? 0,
                null,
                [
                    'user_id' => $user->id,
                    'project_id' => $request['project_id']
                ],
                $this->debtPaymentRepo instanceof \App\Repositories\Tenant\SettingRepository ? $this->debtPaymentRepo : $this->settingRepository ?? app(\App\Repositories\Tenant\SettingRepository::class)
            );

            $refundData = RefundData::from($request)->all();
            $refundData['created_by'] = $user->name;
            $refund = $this->refundRepository->create($refundData);

            // Set supplier_id
            if ($refund->type === 'invoice') {
                $purchase = $this->purchaseRepo->findById($refund->purchase_id);
                if (! $purchase) {
                    throw new \Exception("Purchase not found for refund ID: {$refund->id}");
                }
                $refund->supplier_id = $purchase->partner_id;
            } else {
                $refund->supplier_id = $refundData['partner_id'];
            }
            $refund->save();

            // Calculate totals and create refund items, and update request for totals
            $totalBeforeDiscount = 0;
            if ($refund->type === 'invoice') {
                $this->processInvoiceRefund($request, $refund, $totalBeforeDiscount);
            } else {
                $this->processCategoryRefund($request, $refund, $totalBeforeDiscount);
            }
            $request['total_before_discount'] = $totalBeforeDiscount;

            // Apply discount and update refund totals
            $this->applyDiscount($refund, $request, $totalBeforeDiscount);

            // Apply payment status calculations (paid, partial, unpaid)
            $this->calculatePaymentStatus($request);

            // Update the refund with all correct values
            $refund->update([
                'paid_amount' => $request['paid_amount'],
                'remaining_amount' => $request['remaining_amount'],
                'total_before_discount' => $request['total_before_discount'],
                'total_after_discount' => $request['total_after_discount'],
                'discount_value' => $request['discount_value'] ?? 0,
            ]);

            // Upload images
            if (!empty($request['images'])) {
                foreach (request()->file('images') as $image) {
                    $refund->addMedia($image)->toMediaCollection('refunds');
                }
            }

            if ($refund->type === 'invoice') {
                $this->updatePurchaseInvoice($refund->purchase, $refund);
            }

            $this->calcProjectDebt($request, [
                'total_after_discount' => $refund->total_after_discount,
                'paid_amount' => $refund->paid_amount,
            ]);

            $this->updateBalance($request, $user, $refund);

            $partner = $this->partnerRepository->findById($refund->supplier_id);
            $partner->update([
                'balance' => $partner->balance - $refund->remaining_amount,
            ]);

            $message = TransactionMessageService::refundOperation('created', $user->name, $refund->number);

            $refund->transactions()->create([
                'amount' => $refund->paid_amount,
                'is_creator' => true,
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'add',
                'message' => $message,
            ]);

            DB::commit();

            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    // --- Balance Update ---
    protected function updateBalance(array $request, $user, $refund = null)
    {
        $projectId = $request['project_id'] ?? null;
        if (!$projectId) {
            throw new \Exception('Project ID is required for balance update');
        }

        $project = $this->projectRepo->findById($projectId);

        $refundPaidAmount = $refund->paid_amount ?? 0;
        // increment user balance
        $user->increment('balance', ($request['paid_amount'] - $refundPaidAmount));

        // increment project balance
        $project->increment('balance', ($request['paid_amount'] - $refundPaidAmount));
    }

    // --- Invoice Refund Processing ---
    protected function processInvoiceRefund(array $request, $refund, &$totalBeforeDiscount)
    {
        $purchase = $this->purchaseRepo->findById($request['purchase_id']);

        if (!$purchase) {
            throw new \Exception('Purchase not found');
        }

        // Clear existing refund items for updates
        if ($refund->exists) {
            foreach ($refund->invoiceCategory as $category) {
                $category->refundItems()->delete();
            }
            $refund->invoiceCategory()->delete();
        }

        $matchedItems = 0;
        $totalBeforeDiscount = 0;

        // Handle new structure: refund_items as array of ['category_id', 'item_id', 'qty_refunded']
        if (!empty($request['refund_items'])) {
            $refundItemsByCategory = $this->groupRefundItemsByCategory($request['refund_items']);
            $matchedItems = $this->processRefundItemsStructure($purchase, $refund, $refundItemsByCategory, $totalBeforeDiscount);
        } else {
            // Handle legacy structure: item_ids and qty_refunded arrays
            $matchedItems = $this->processLegacyRefundStructure($purchase, $refund, $request, $totalBeforeDiscount);
        }

        if ($matchedItems === 0) {
            throw new \Exception('No refund items matched purchase items. Please check your request structure and IDs.');
        }
    }

    // --- Refund Item Grouping ---
    private function groupRefundItemsByCategory(array $refundItems): array
    {
        $refundItemsByCategory = [];
        foreach ($refundItems as $refundItem) {
            $catId = $refundItem['category_id'];
            if (!isset($refundItemsByCategory[$catId])) {
                $refundItemsByCategory[$catId] = [];
            }
            $refundItemsByCategory[$catId][] = $refundItem;
        }
        return $refundItemsByCategory;
    }

    // --- Refund Item Processing (New Structure) ---
    private function processRefundItemsStructure($purchase, $refund, array $refundItemsByCategory, &$totalBeforeDiscount): int
    {
        $matchedItems = 0;

        foreach ($purchase->invoiceCategory as $category) {
            $invoiceCategory = $refund->invoiceCategory()->create([
                'item_id' => $category['item_id']
            ]);

            $categoryRefundItems = $refundItemsByCategory[$category['item_id']] ?? [];

            foreach ($category->items as $item) {
                foreach ($categoryRefundItems as $refundItem) {
                    if ($refundItem['item_id'] == $item->id && isset($refundItem['qty_refunded'])) {
                        $qtyRefundedValue = (float) $refundItem['qty_refunded'];

                        if ($qtyRefundedValue <= 0) {
                            continue; // Skip zero or negative quantities
                        }

                        // Prevent refunding more than purchased (across all refunds)
                        $totalRefundedQty = RefundItem::where('name', $item->name)
                            ->where('price', $item->price)
                            ->where('unit_id', $item->unit_id)
                            ->where('invoice_category_id', $invoiceCategory->id)
                            ->sum('qty_refunded');

                        $availableQty = $item->qty - $totalRefundedQty;

                        if ($qtyRefundedValue > $availableQty) {
                            throw new \Exception("Invalid Refund Quantity: Cannot refund {$qtyRefundedValue} when only {$availableQty} is available for item {$item->name}");
                        }

                        $newQtyPurchased = max(0, $item->qty - $qtyRefundedValue);
                        $itemTotal = $item->price * $qtyRefundedValue;
                        $totalBeforeDiscount += $itemTotal;
                        $matchedItems++;

                        RefundItem::create([
                            'invoice_category_id' => $invoiceCategory->id,
                            'name' => $item->name,
                            'unit_id' => $item->unit_id,
                            'qty_purchased' => $newQtyPurchased,
                            'qty_refunded' => $qtyRefundedValue,
                            'price' => $item->price,
                            'total' => $itemTotal,
                        ]);

                        // Immediately update the purchase item qty
                        $item->update([
                            'qty' => $newQtyPurchased
                        ]);
                    }
                }
            }
        }

        return $matchedItems;
    }

    // --- Refund Item Processing (Legacy Structure) ---
    private function processLegacyRefundStructure($purchase, $refund, array $request, &$totalBeforeDiscount): int
    {
        $matchedItems = 0;
        $itemIds = $request['item_ids'] ?? [];
        $qtyRefunded = $request['qty_refunded'] ?? [];

        foreach ($purchase->invoiceCategory as $category) {
            $invoiceCategory = $refund->invoiceCategory()->create([
                'item_id' => $category['item_id']
            ]);

            foreach ($category->items as $key => $item) {
                if (!isset($itemIds[$key]) || !isset($qtyRefunded[$key])) {
                    continue;
                }

                $qtyRefundedValue = (float) $qtyRefunded[$key];

                if ($qtyRefundedValue <= 0) {
                    continue; // Skip zero or negative quantities
                }

                // Prevent refunding more than purchased (across all refunds)
                $totalRefundedQty = RefundItem::where('name', $item->name)
                    ->where('price', $item->price)
                    ->where('unit_id', $item->unit_id)
                    ->where('invoice_category_id', $invoiceCategory->id)
                    ->sum('qty_refunded');

                $availableQty = $item->qty - $totalRefundedQty;

                if ($qtyRefundedValue > $availableQty) {
                    throw new \Exception("Invalid Refund Quantity: Cannot refund {$qtyRefundedValue} when only {$availableQty} is available for item {$item->name}");
                }

                $newQtyPurchased = max(0, $item->qty - $qtyRefundedValue);
                $itemTotal = $item->price * $qtyRefundedValue;
                $totalBeforeDiscount += $itemTotal;
                $matchedItems++;

                RefundItem::create([
                    'invoice_category_id' => $invoiceCategory->id,
                    'name' => $item->name,
                    'unit_id' => $item->unit_id,
                    'qty_purchased' => $newQtyPurchased,
                    'qty_refunded' => $qtyRefundedValue,
                    'price' => $item->price,
                    'total' => $itemTotal,
                ]);

                // Immediately update the purchase item qty
                $item->update([
                    'qty' => $newQtyPurchased
                ]);
            }
        }

        return $matchedItems;
    }

    // --- Purchase Update for Refund ---
    protected function updatePurchaseInvoice($purchase, $refund, string $operator = '-')
    {
        $applyOperation = function ($a, $b) use ($operator) {
            return $operator === '-' ? ($a - $b) : ($a + $b);
        };

        // Update individual purchase items based on refund items
        foreach ($refund->items as $refundItem) {
            $purchaseCategory = $purchase->invoiceCategory
                ->where('item_id', $refundItem->category->item_id)
                ->first();

            if (!$purchaseCategory) {
                continue;
            }

            // Find the specific purchase item that matches the refund item
            foreach ($purchaseCategory->items as $purchaseItem) {
                if ($purchaseItem->name === $refundItem->name && $purchaseItem->price == $refundItem->price) {
                    $newQty = $applyOperation($purchaseItem->qty, $refundItem->qty_refunded);
                    $newTotal = $applyOperation($purchaseItem->total, $refundItem->total);

                    $purchaseItem->update([
                        'qty' => max(0, $newQty),
                        'total' => max(0, $newTotal),
                    ]);
                    break; // Found the matching item, no need to continue
                }
            }
        }

        // Update purchase totals
        $newTotalBeforeDiscount = max(0, $applyOperation($purchase->total_before_discount, $refund->total_before_discount));
        $newTotalAfterDiscount = max(0, $applyOperation($purchase->total_after_discount, $refund->total_after_discount));

        // Handle paid amount calculation based on operation
        if ($operator === '-') {
            // For refund deletion/subtraction, don't change paid amount, just recalculate remaining
            $newPaidAmount = $purchase->paid_amount;
            $newRemainingAmount = max(0, $newTotalAfterDiscount - $newPaidAmount);
        } else {
            // For refund restoration/addition, restore the paid amount as well
            $newPaidAmount = max(0, $applyOperation($purchase->paid_amount, $refund->total_after_discount));
            $newRemainingAmount = max(0, $newTotalAfterDiscount - $newPaidAmount);
        }

        $purchase->update([
            'total_before_discount' => $newTotalBeforeDiscount,
            'total_after_discount' => $newTotalAfterDiscount,
            'paid_amount' => $newPaidAmount,
            'remaining_amount' => $newRemainingAmount,
        ]);
    }



    // --- Category Refund Processing ---
    protected function processCategoryRefund(array $request, $refund, &$totalBeforeDiscount)
    {
        // Clear existing refund items for updates
        if ($refund->exists) {
            foreach ($refund->invoiceCategory as $category) {
                $category->refundItems()->delete();
            }
            $refund->invoiceCategory()->delete();
        }

        $calc = $this->calculateRefundItems($request['categories']);
        $totalBeforeDiscount = $calc['overall_total'];
        $this->createCategoryItems($calc, $refund);
    }

    // --- Purchase Update for Refund Update (Advanced) ---
    protected function updatePurchaseInvoiceForRefundUpdate($purchase, array $oldRefundState, $newRefund)
    {
        logger('Starting purchase invoice update for refund update');
        logger('Old refund items count: ' . count($oldRefundState['items']));
        logger('New refund items count: ' . $newRefund->items->count());

        // First, revert the old refund impact (add back the old refunded quantities)
        foreach ($oldRefundState['items'] as $oldItem) {
            logger('Restoring item: ' . $oldItem['name'] . ' qty: ' . $oldItem['qty_refunded']);
            $this->updatePurchaseItemQuantity($purchase, $oldItem, 'restore');
        }

        // Then apply the new refund impact (subtract the new refunded quantities)
        foreach ($newRefund->items as $newItem) {
            $itemData = [
                'name' => $newItem->name,
                'price' => $newItem->price,
                'qty_refunded' => $newItem->qty_refunded,
                'total' => $newItem->total,
                'category_item_id' => $newItem->category->item_id
            ];
            logger('Applying new refund for item: ' . $newItem->name . ' qty: ' . $newItem->qty_refunded);
            $this->updatePurchaseItemQuantity($purchase, $itemData, 'refund');
        }

        // Update purchase totals
        $oldTotalBefore = $oldRefundState['total_before_discount'];
        $oldTotalAfter = $oldRefundState['total_after_discount'];
        $newTotalBefore = $newRefund->total_before_discount;
        $newTotalAfter = $newRefund->total_after_discount;

        $purchase->update([
            'total_before_discount' => max(0, $purchase->total_before_discount + $oldTotalBefore - $newTotalBefore),
            'total_after_discount' => max(0, $purchase->total_after_discount + $oldTotalAfter - $newTotalAfter),
            'remaining_amount' => max(0, ($purchase->total_after_discount + $oldTotalAfter - $newTotalAfter) - $purchase->paid_amount),
        ]);
    }

    // --- Purchase Item Quantity Update ---
    protected function updatePurchaseItemQuantity($purchase, array $itemData, string $operation)
    {
        $purchaseCategory = $purchase->invoiceCategory
            ->where('item_id', $itemData['category_item_id'])
            ->first();

        if (!$purchaseCategory) {
            logger('Purchase category not found for item_id: ' . $itemData['category_item_id']);
            return;
        }

        logger('Found purchase category, looking for item: ' . $itemData['name'] . ' with price: ' . $itemData['price']);

        // Find the specific purchase item that matches
        foreach ($purchaseCategory->items as $purchaseItem) {
            logger('Checking purchase item: ' . $purchaseItem->name . ' with price: ' . $purchaseItem->price . ' current qty: ' . $purchaseItem->qty);

            if ($purchaseItem->name === $itemData['name'] && $purchaseItem->price == $itemData['price']) {
                $oldQty = $purchaseItem->qty;

                if ($operation === 'restore') {
                    // Restore quantity (add back when reverting old refund)
                    $newQty = $purchaseItem->qty + $itemData['qty_refunded'];
                    $newTotal = $purchaseItem->total + $itemData['total'];
                    logger("RESTORE: {$itemData['name']} qty: {$oldQty} + {$itemData['qty_refunded']} = {$newQty}");
                } else { // 'refund'
                    // Decrement quantity (subtract when applying refund)
                    $newQty = max(0, $purchaseItem->qty - $itemData['qty_refunded']);
                    $newTotal = max(0, $purchaseItem->total - $itemData['total']);
                    logger("REFUND: {$itemData['name']} qty: {$oldQty} - {$itemData['qty_refunded']} = {$newQty}");
                }

                $purchaseItem->update([
                    'qty' => $newQty,
                    'total' => $newTotal,
                ]);
                logger("Updated purchase item {$itemData['name']} qty to: {$newQty}");
                break;
            }
        }
    }

    // --- Discount Application ---
    protected function applyDiscount(array &$request, $totalBeforeDiscount)
    {
        $discountValue = $request['discount_value'] ?? 0;
        $discountType = $request['discount_type'] ?? null;
        $discountAmount = 0;

        if ($discountType === 'percentage' && $discountValue > 0) {
            $discountAmount = $totalBeforeDiscount * ($discountValue / 100);
        } elseif ($discountValue > 0) {
            $discountAmount = $discountValue;
        }

        // Ensure discount doesn't exceed total
        $discountAmount = min($discountAmount, $totalBeforeDiscount);
        $totalAfterDiscount = max(0, $totalBeforeDiscount - $discountAmount);
        $request['total_after_discount'] = $totalAfterDiscount;
    }


    // --- Refund Item Calculation ---
    protected function calculateRefundItems(array $items)
    {
        $categories = collect($items);

        $categories->transform(function ($category) {
            $category['items'] = collect($category['items'])->transform(function ($item) {
                // Ensure numeric values and validate
                $price = (float) ($item['price'] ?? 0);
                $qtyRefunded = (float) ($item['qty_refunded'] ?? 0);

                if ($price < 0 || $qtyRefunded < 0) {
                    throw new \Exception('Price and quantity must be non-negative values');
                }

                $item['price'] = $price;
                $item['qty_refunded'] = $qtyRefunded;
                $item['total'] = $price * $qtyRefunded;

                return $item;
            });

            $category['total'] = $category['items']->sum('total');

            return $category;
        });

        $overallTotal = $categories->sum('total');

        return [
            'categories' => $categories->toArray(),
            'overall_total' => $overallTotal,
        ];
    }

    // --- Category Item Creation ---
    protected function createCategoryItems(array $calc, $refund)
    {
        foreach ($calc['categories'] as $category) {
            if (!isset($category['id'])) {
                throw new \Exception('Category ID is required for refund items');
            }

            $invoiceCategory = $refund->invoiceCategory()->updateOrCreate(
                ['item_id' => $category['id']],
                ['item_id' => $category['id']]
            );

            // Clear existing items before creating new ones
            $invoiceCategory->refundItems()->delete();

            // Validate and create items
            foreach ($category['items'] as $item) {
                if (empty($item['name']) || !isset($item['price']) || !isset($item['qty_refunded'])) {
                    throw new \Exception('Invalid item data: name, price, and qty_refunded are required');
                }
            }

            $invoiceCategory->refundItems()->createMany($category['items']);
        }
    }


    // --- Totals ---
    public function getTotal()
    {
        return $this->refundRepository->getRefundsTotal();
    }


    // --- Get Refund By ID ---
    public function getById(string $id)
    {
        $refund = $this->refundRepository->findById($id);
        if (! $refund) {
            throw new NotFoundHttpException;
        }
        return $refund;
    }


    // --- Refund Deletion ---
    public function delete(string $id)
    {
        $refund = $this->getById($id);
        $refundCreator = $refund->transactions()->creator()->first()->userable;
        $refundCreator->decrement('balance', $refund->paid_amount);
        $refund->project->decrement('balance', $refund->paid_amount);
        if ($refund->type === 'invoice') {
            $this->updatePurchaseInvoice($refund->purchase, $refund, '+');
        }
        $debt = $refund->project->asCreditor()->where('debtor_id', $refund->supplier_id)->first();
        if ($debt) {
            $newTotalInvoices = $debt->total_invoices - $refund->total_after_discount;
            $newTotalDebt = $debt->total_debt - ($refund->total_after_discount - $refund->paid_amount);
            $debt->update([
                'total_invoices' => max(0, $newTotalInvoices),
                'total_debt' => max(0, $newTotalDebt),
            ]);
        }
        $refund->delete();
        return success(__('Deleted Successfully'));
    }


    // --- Refund Restore ---
    public function restore(string $id)
    {
        $refund = $this->refundRepository->findTrashedById($id);
        if (!$refund || $refund->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $refundCreator = $refund->transactions()->creator()->first()->userable;
        $refundCreator->increment('balance', $refund->paid_amount);
        $refund->project->increment('balance', $refund->paid_amount);
        if ($refund->type === 'invoice') {
            $this->updatePurchaseInvoice($refund->purchase, $refund);
        }
        $debt = $refund->project->asCreditor()->where('debtor_id', $refund->supplier_id)->first();
        if ($debt) {
            $newTotalInvoices = $debt->total_invoices + $refund->total_after_discount;
            $newTotalDebt = $debt->total_debt + ($refund->total_after_discount - $refund->paid_amount);
            $debt->update([
                'total_invoices' => $newTotalInvoices,
                'total_debt' => $newTotalDebt,
            ]);
        }
        $refund->restore();
        return success(__('Restored Successfully'));
    }


    // --- Refund Force Delete ---
    public function forceDelete(string $id)
    {
        $refund = $this->refundRepository->findTrashedById($id);
        if (! $refund || $refund->permanent_deleted) {
            throw new NotFoundHttpException;
        }
        $refund->update([
            'permanent_deleted' => true,
            'deleted_at' => now(),
        ]);
        return success(__('Deleted Successfully'));
    }


    // --- Media Deletion ---
    public function deleteMedia(string $refundId, string $mediaId)
    {
        $refund = $this->getById($refundId);
        $media = $refund->getMedia('refunds')->find($mediaId);
        if (! $media) {
            throw new NotFoundHttpException;
        }
        $media->delete();
        return success(__('Deleted Successfully'));
    }


    // --- Refund Payment ---
    public function pay(array $request, string $id)
    {
        $refund = $this->getById($id);
        $user = auth()->user();
        DB::beginTransaction();
        try {
            $debt = $refund->project->asCreditor()
                ->where(function ($query) use ($refund) {
                    $query->where('debtor_id', $refund->supplier_id);
                })
                ->first();
            $user = auth()->user();
            if ((int) $debt->total_debt === 0) {
                return error(__('There is no debt to pay.'), 422);
            }
            $amountToPay = $request['amount'];
            if ($amountToPay > $debt->total_debt) {
                return error(__('The amount must be equal to or less than the total debt.'), 422);
            }
            $paidAmount = $debt->total_payments + $amountToPay;
            $remainingDebt = $debt->total_debt - $amountToPay;
            $data = DebtPaymentData::from([
                'debt_id' => $debt->id,
                'user_id' => $user->id,
                'total_debt' => $debt->total_debt,
                'remaining_debt' => $remainingDebt,
                'paid_amount' => $amountToPay,
            ])->all();
            $this->debtPaymentRepo->create($data);
            $debt->update([
                'total_payments' => $paidAmount,
                'total_debt' => $remainingDebt,
            ]);
            $refund->project->increment('balance', $amountToPay);
            $user->increment('balance', $amountToPay);
            $message = TransactionMessageService::refundOperation('paid', $user->name, $refund->project->name, [
                'amount' => $amountToPay,
                'target' => $refund->project->name,
            ]);

            $refund->transactions()->create([
                'amount' => $amountToPay,
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'pay',
                'message' => $message,
            ]);
            DB::commit();
            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            logger($e);
            DB::rollBack();
            return error(__('An error occurred: ') . $e->getMessage(), 500);
        }
    }

    public function update(array $request, $id)
    {
        $user = auth()->user();
        $refund = $this->refundRepository->findById($id);

        if (!$refund) {
            throw new NotFoundHttpException('Refund not found');
        }

        $refundCreator = optional($refund->transactions()->creator()->first())->userable;

        if (!$refundCreator) {
            throw new NotFoundHttpException('Refund creator not found');
        }

        $oldSupplierId = $refund->supplier_id;
        $oldTotalAfterDiscount = $refund->total_after_discount;
        $oldPaidAmount = $refund->paid_amount;
        $oldRemainingAmount = $refund->remaining_amount;

        DB::beginTransaction();
        try {
            // Set project_id early since it's needed by multiple methods
            $request['project_id'] = $refund->project_id;

            // For invoice refunds, prepare for purchase updates
            if ($refund->type === 'invoice') {
                $purchase = $this->purchaseRepo->findById($refund->purchase_id);
                if (!$purchase) {
                    throw new \Exception("Purchase not found for refund ID: {$refund->id}");
                }
                $request['partner_id'] = $purchase->partner_id;
                $request['purchase_id'] = $refund->purchase_id;

                // First, revert the current refund impact on purchase before updating
                logger('Reverting current refund impact on purchase before update');
                $this->updatePurchaseInvoice($refund->purchase, $refund, '+');
            }


            // Process refund items and calculate totals
            $newTotal = 0;
            if ($refund->type === 'invoice') {
                $this->processInvoiceRefund($request, $refund, $newTotal);
                // Reload refund items after update to ensure correct purchase update
                $refund->load('items.category');
            } else {
                $this->processCategoryRefund($request, $refund, $newTotal);
                $refund->load('items.category');
            }
            $request['total_before_discount'] = $newTotal;

            // Apply discount calculations
            $this->applyDiscount($refund, $request, $newTotal);

            // Calculate payment status
            $this->calculatePaymentStatus($request);

            // Check sufficient balance for paid_amount (only if changed)
            $this->assertSufficientBalance(
                'refund',
                $request['paid_amount'] ?? 0,
                $refund->paid_amount,
                [
                    'user_id' => $user->id,
                    'project_id' => $request['project_id']
                ],
                $this->debtPaymentRepo instanceof \App\Repositories\Tenant\SettingRepository ? $this->debtPaymentRepo : $this->settingRepository ?? app(\App\Repositories\Tenant\SettingRepository::class)
            );

            // Update refund with new data
            $data = RefundData::from($request)->all();
            $refund->update($data);

            // Update supplier_id based on refund type
            if ($refund->type === 'invoice') {
                $refund->supplier_id = $request['partner_id'];
            } else {
                $refund->supplier_id = $data['partner_id'];
            }
            $refund->save();

            // For invoice refunds, apply the new refund impact on purchase
            if ($refund->type === 'invoice') {
                logger('Applying new refund impact on purchase after update');
                $this->updatePurchaseInvoice($refund->purchase, $refund, '-');
                logger('Finished updating purchase invoice');
            }

            // Update project debt calculations
            $this->calcProjectDebt(
                $request,
                [
                    'total_after_discount' => $request['total_after_discount'],
                    'paid_amount' => $request['paid_amount'],
                ],
                $oldSupplierId,
                [
                    'total_after_discount' => $oldTotalAfterDiscount,
                    'paid_amount' => $oldPaidAmount,
                ]
            );

            // Update balances
            $this->updateBalance($request, $user, $refund);

            // Update partner balance with the difference in remaining amounts
            $partner = $this->partnerRepository->findById($request['partner_id']);
            $balanceChange = $request['remaining_amount'] - $oldRemainingAmount;
            $partner->update([
                'balance' => $partner->balance - $balanceChange
            ]);

            // Create transaction record
            $message = TransactionMessageService::refundOperation('updated', $user->name, $refund->number);

            $refund->transactions()->create([
                'amount' => $request['paid_amount'],
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'update',
                'message' => $message,
            ]);

            // Handle image uploads
            if (!empty($request['images'])) {
                foreach (request()->file('images') as $image) {
                    $refund->addMedia($image)->toMediaCollection('refunds');
                }
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e);

            return error($e->getMessage());
        }
    }
}
