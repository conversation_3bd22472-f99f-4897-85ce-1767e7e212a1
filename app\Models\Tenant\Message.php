<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Message extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $fillable = [
        'room_id',
        'body',
        'type',
        'file_path',
    ];

    public function sender()
    {
        return $this->morphTo()->withTrashed();
    }

    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    public function getImageAttribute()
    {
        return $this->getFirstMedia('messages');
    }

    public function readers()
    {
        return $this->belongsToMany(User::class, 'message_user_reads')
            ->using(MessageUserRead::class)
            ->withPivot('read_at')
            ->withTimestamps();
    }

    public function readStatusFor(User $user)
    {
        return $this->readers()->where('user_id', $user->id)->first()?->pivot;
    }
}
