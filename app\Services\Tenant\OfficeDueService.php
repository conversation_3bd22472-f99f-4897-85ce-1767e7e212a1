<?php

namespace App\Services\Tenant;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\OfficeDueRepository;
use App\Repositories\Tenant\PurchaseRepository;
use App\Repositories\Tenant\ContractorPaymentRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OfficeDueService
{
    public function __construct(
        protected OfficeDueRepository $OfficeDueRepository,
        protected PurchaseRepository $purchaseRepository,
        protected ContractorPaymentRepository $contractor,
        protected ProjectRepository $projectRepository,
    ) {
        //
    }

    public function getProjectOfficeDue()
    {
        $record = $this->OfficeDueRepository->findByProjectId();

        if (! $record) {
            throw new NotFoundHttpException;
        }

        return $record;
    }

    public function createOrUpdate($project_id)
    {
        $project = $this->projectRepository->findById($project_id);

        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        DB::beginTransaction();

        try {
            // Calculate project expenses correctly as total purchases + total contractor payments
            $project_expenses = $this->purchaseRepository->totalPurchases($project_id) +
                $this->contractor->totalPayments($project_id);

            // Calculate total profits based on payment method
            if ($project->payment_method_id == 1) {
                // Case 1: Payment method is 1 (percentage of expenses)
                // total_profits = expenses * office_ratio
                $total_profits = $project_expenses * ($project->office_ratio / 100);
            } else {
                // Case 2: Payment method is not 1 (fixed price)
                // total_profits = project_total - expenses
                $total_profits = $project->total - $project_expenses;
            }

            // Get the existing office due record
            $officeDue = $project->officeDue;

            // Calculate due profits (common for both methods)
            $received_profits = $officeDue?->recieved_profits ?? 0;
            $due_profits = $total_profits - $received_profits;

            // Prepare data for update
            $data = [
                'project_id' => $project_id,
                'project_expenses' => $project_expenses,
                'office_ratio' => $project->office_ratio,
                'total_profits' => $total_profits,
                'recieved_profits' => $received_profits,
                'due_profits' => $due_profits,
            ];

            // Update or create the office due record
            $this->OfficeDueRepository->calculate($data);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            logger()->error('Office due update failed: ' . $e->getMessage());
            abort(422, 'Office due update failed. Please check your input.');
        }
    }
}
