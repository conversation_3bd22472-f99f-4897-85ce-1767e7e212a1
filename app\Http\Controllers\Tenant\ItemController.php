<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateItemRequest;
use App\Http\Requests\Tenant\Admin\UpdateItemRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\DDLResource;
use App\Http\Resources\Tenant\ItemResource;
use App\Services\Tenant\ItemService;

class ItemController extends Controller
{
    public function __construct(public ItemService $itemService) {}

    public function index()
    {
        $clients = $this->itemService->index();

        return success(new BaseCollection($clients, ItemResource::class));
    }

    public function show($id)
    {
        $client = $this->itemService->show($id);

        return success(new ItemResource($client));
    }

    public function store(CreateItemRequest $request)
    {
        $this->itemService->create($request->validated());

        return success(__('Created Successfully'));
    }

    public function update(string $id, UpdateItemRequest $request)
    {
        $this->itemService->update($id, $request->validated());

        return success(__('updated Successfully'));
    }

    public function destroy(string $id)
    {
        $this->itemService->delete($id);

        return success(__('deleted successfully'));
    }

    public function ddl()
    {
        $items = $this->itemService->ddl(['id', 'name_ar', 'name_en']);

        return success(DDLResource::collection($items));
    }
}
