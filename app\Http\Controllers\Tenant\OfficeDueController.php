<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\OfficeDueService;
use App\Http\Resources\Tenant\Admin\OfficeDueResource;

class OfficeDueController extends Controller
{
    public function __construct(protected OfficeDueService $OfficeDueService)
    {
        //
    }

    public function show()
    {
        $record = $this->OfficeDueService->getProjectOfficeDue();

        return success(new OfficeDueResource($record));
    }
}
