<?php

namespace App\Services\Tenant;

use App\Models\Tenant\Partner;
use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\ContractorPaymentData;
use App\Repositories\Tenant\PartnerRepository;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\ContractorPaymentRepository;
use App\Repositories\Tenant\SettingRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Services\Tenant\Traits\HasSufficientBalanceCheck;

class ContractorPaymentService
{
    use HasSufficientBalanceCheck;
    public function __construct(
        protected ContractorPaymentRepository $contractorPaymentRepo,
        protected PartnerRepository $partnerRepo,
        protected ProjectRepository $projectRepo,
        protected OfficeDueService $officeDueService,
        protected SettingRepository $settingRepository
    ) {
        //
    }

    public function get()
    {
        $filterable = request()->only(['search', 'contractor', 'date_from', 'date_to']);

        if (request()->type === 'archive') {
            return $this->contractorPaymentRepo->getTrashedWithPaginate();
        } else {
            return $this->contractorPaymentRepo->getPaginated($filterable);
        }
    }

    public function getById(string $id)
    {
        $contractorPayment = $this->contractorPaymentRepo->findById($id);

        if (! $contractorPayment) {
            throw new NotFoundHttpException('resource not found');
        }

        return $contractorPayment;
    }

    private function updateBalances($user, $contractor, $amountAdjustment, $projectId = null)
    {
        $user->update(['balance' => $user->balance - $amountAdjustment]);
        $contractor->update(['balance' => $contractor->balance - $amountAdjustment]);

        if ($projectId) {
            $project = $this->projectRepo->findById($projectId);

            if (! $project) {
                return error('Project not found', 404);
            }

            $project->update(['balance' => $project->balance - $amountAdjustment]);
        }
    }

    private function reverseBalances($user, $contractor, $amountAdjustment, $projectId = null)
    {
        $contractor->update(['balance' => $contractor->balance + $amountAdjustment]);
        $user->update(['balance' => $user->balance + $amountAdjustment]);

        if ($projectId) {
            $project = $this->projectRepo->findById($projectId);

            if (! $project) {
                return error('Project not found', 404);
            }

            $project->update(['balance' => $project->balance + $amountAdjustment]);
        }
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $user = auth()->user();
            $request['created_by'] = $user->name;

            $data = ContractorPaymentData::from($request)->all();

            $contractor = $this->partnerRepo->findById($request['contractor_id']);

            if (! $contractor) {
                return error('Contractor not found', 404);
            }

            $this->assertSufficientBalance(
                'contractor_payment',
                $request['amount'],
                null,
                ['user_id' => $user->id, 'project_id' => $data['project_id']],
                $this->settingRepository
            );
            // Ensure amount is deducted correctly
            $this->updateBalances($user, $contractor, $request['amount'], $request['project_id'] ?? null);

            $contractorPayment = $this->contractorPaymentRepo->create($data);

            $this->calcProjectDebt($request, [
                'total' => $request['amount'],
            ]);

            $contractorPayment->transactions()->create([
                'amount' => $request['amount'],
                'is_creator' => true,
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'add',
                'message' => "{$user->name} added {$request['amount']} to {$contractor->name}",
            ]);
            $this->officeDueService->createOrUpdate($data['project_id']);

            DB::commit();

            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    private function calcProjectDebt(array $request, array $certificateAmounts, ?int $oldContractorId = null, array $oldCertificateAmounts = [])
    {
        $project = $this->projectRepo->findById($request['project_id']);

        $debt = $project->asDebtor()->where('creditor_id', $request['contractor_id'])->firstOrCreate([
            'creditor_id' => $request['contractor_id'],
            'creditor_type' => Partner::class,
            'type' => 'certification',
        ]);

        if ($oldContractorId) {
            $oldDebt = $project->asDebtor()->where(function ($query) use ($oldContractorId) {
                $query->where('type', 'certification')
                    ->where('creditor_id', $oldContractorId);
            })->first();

            if ($oldDebt) {
                $oldDebt->update([
                    'total_invoices' => $oldDebt->total_invoices - $oldCertificateAmounts['total'],
                    'total_debt' => $oldDebt->total_debt - $oldCertificateAmounts['total'],
                ]);
            }
        }

        $debt->update([
            'total_invoices' => $debt->total_invoices + $certificateAmounts['total'],
            'total_debt' => $debt->total_debt + $certificateAmounts['total'],
        ]);
    }

    public function update(string $id, array $request)
    {
        DB::beginTransaction();

        $contractorPayment = $this->getById($id);

        $request['project_id'] = $contractorPayment->project_id;

        try {
            $user = auth()->user();
            $paymentCreator = $contractorPayment->transactions()->creator()->first()->userable;
            $data = collect(ContractorPaymentData::from($request)->toArray())->except(['created_by', 'archived_by'])->all();

            $balanceCheck = $this->settingRepository->balanceCheck($request['amount'],  $paymentCreator->id, $data['project_id']);
            if (!$balanceCheck['status']) {
                DB::rollBack();
                return error($balanceCheck['message']); // Return error message if balance check fails
            }


            // Store old contractor and old amount
            $oldContractorId = $contractorPayment->contractor_id;
            $oldAmount = $contractorPayment->amount;
            // Fetch new contractor
            $contractor = $this->partnerRepo->getwithoutglobalscopes($request['contractor_id']);
            if (! $contractor) {
                return error('Contractor not found', 404);
            }

            // Handle contractor change
            if ($oldContractorId != $request['contractor_id']) {
                $oldContractor = $this->partnerRepo->getwithoutglobalscopes($oldContractorId);
                if (! $oldContractor) {
                    return error('Previous contractor not found', 404);
                }

                // Adjust balances for old and new contractors
                $oldContractor->update(['balance' => $oldContractor->balance + $oldAmount]);
                $contractor->update(['balance' => $contractor->balance - $request['amount']]);

                // Update debt for old contractor (subtract the old amount)
                $this->calcProjectDebt($request, [
                    'total' => 0,
                ], $oldContractorId, ['total' => $oldAmount]);
            } else {
                // If contractor remains the same, adjust based on amount difference
                $amountDifference = $request['amount'] - $oldAmount;
                $this->updateBalances($paymentCreator, $contractor, $amountDifference, $contractorPayment->project_id);
            }

            // Update contractor payment record
            $contractorPayment->update($data);

            // Update debt for new contractor (add the new amount)
            $this->calcProjectDebt($request, [
                'total' => $request['amount'],
            ]);

            // Create a new transaction entry
            $contractorPayment->transactions()->create([
                'amount' => $request['amount'],
                'userable_id' => $user->id,
                'userable_type' => $user->getMorphClass(),
                'type' => 'update',
                'message' => "{$user->name} updated {$contractorPayment->amount} to {$request['amount']}",
            ]);

            $this->officeDueService->createOrUpdate($contractorPayment->project_id);

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function delete(string $id)
    {
        DB::beginTransaction();

        try {
            $contractorPayment = $this->contractorPaymentRepo->findById($id);

            if (! $contractorPayment) {
                throw new NotFoundHttpException('resource not found');
            }

            $paymentCreator = $contractorPayment->transactions()->creator()->first()->userable;

            // Reverse balance deductions
            $this->reverseBalances(
                $paymentCreator,
                $contractorPayment->contractor,
                $contractorPayment->amount,
                $contractorPayment->project_id
            );

            // Soft delete with metadata
            $contractorPayment->update([
                'archived_by' => auth()->user()->name,
                'deleted_at' => now(),
            ]);

            DB::commit();

            return success(__('Deleted Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function restore(string $id)
    {
        DB::beginTransaction();

        try {
            $contractorPayment = $this->contractorPaymentRepo->findTrashedById($id);

            if (! $contractorPayment) {
                throw new NotFoundHttpException('resource not found');
            }

            $paymentCreator = $contractorPayment->transactions()->creator()->first()->userable;

            // Reapply the balance deduction
            $this->updateBalances(
                $paymentCreator,
                $contractorPayment->contractor,
                $contractorPayment->amount,
                $contractorPayment->project_id
            );

            // Restore the record
            $contractorPayment->update([
                'archived_by' => null,
                'deleted_at' => null,
            ]);

            DB::commit();

            return success(__('Restored Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function forceDelete(string $id)
    {
        $contractorPayment = $this->contractorPaymentRepo->findTrashedById($id);

        if (! $contractorPayment || $contractorPayment->permanent_deleted) {
            throw new NotFoundHttpException('resource not found');
        }

        $contractorPayment->update([
            'deleted_at' => now(),
            'permanent_deleted' => true,
        ]);

        return success(__('Deleted Successfully'));
    }

    public function getTotalPayment()
    {
        return $this->contractorPaymentRepo->totalPayments(request()->project);
    }
}
