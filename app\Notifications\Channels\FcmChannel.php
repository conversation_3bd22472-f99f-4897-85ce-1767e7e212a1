<?php

namespace App\Notifications\Channels;

use App\Services\FcmService;
use Illuminate\Notifications\Notification;

class FcmChannel
{
    public function __construct(private FcmService $fcm) {}

    public function send($notifiable, Notification $notification)
    {
        // get unique notifiable fcm tokens
        $deviceTokens = $notifiable->deviceTokens()->get()->unique('device_id');

        foreach ($deviceTokens as $deviceToken) {
            $this->fcm->send($deviceToken->token, $notification->data);
        }
    }
}
