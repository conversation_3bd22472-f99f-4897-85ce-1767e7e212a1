<?php

namespace App\Http\Controllers\Tenant;

use App\DTO\Tenant\TaskStatusData;
use Illuminate\Support\Collection;
use App\Http\Controllers\Controller;
use App\Services\Tenant\TaskService;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\TaskResource;
use App\Http\Resources\Tenant\ArchivedTaskResource;
use App\Http\Requests\Tenant\Admin\CreateTaskRequest;
use App\Http\Requests\Tenant\Admin\UpdateTaskRequest;
use App\Http\Requests\Tenant\Admin\UpdateTaskStatusRequest;

class TaskController extends Controller
{
    public function __construct(protected TaskService $taskService) {}

    public function index()
    {
        $tasks = $this->taskService->index();

        return success([
            'new' => TaskResource::collection($tasks['new'] ?? Collection::empty()),
            'in_progress' => TaskResource::collection($tasks['in_progress'] ?? Collection::empty()),
            'finished' => TaskResource::collection($tasks['finished'] ?? Collection::empty()),
            'cancelled' => TaskResource::collection($tasks['cancelled'] ?? Collection::empty()),
        ]);
    }

    public function archived()
    {
        $tasks =  $this->taskService->getArchived();
        
        return success(new BaseCollection($tasks, ArchivedTaskResource::class));
    }

    public function show($id)
    {

        $task = $this->taskService->show($id);

        return success(new TaskResource($task));
    }

    public function store(CreateTaskRequest $request)
    {
        return $this->taskService->create($request->validated());
    }

    public function update(UpdateTaskRequest $request, $id)
    {
        return $this->taskService->update($id, $request->validated());
    }

    public function destroy($id)
    {
        return $this->taskService->delete($id);
    }

    public function restore($id)
    {
        return $this->taskService->restore($id);
    }

    public function forceDelete($id)
    {
        return $this->taskService->forceDelete($id);
    }

    public function myTasks()
    {
        $tasks = $this->taskService->getUserTasks();

        return success([
            'new' => TaskResource::collection($tasks['new'] ?? Collection::empty()),
            'in_progress' => TaskResource::collection($tasks['in_progress'] ?? Collection::empty()),
            'finished' => TaskResource::collection($tasks['finished'] ?? Collection::empty()),
            'cancelled' => TaskResource::collection($tasks['cancelled'] ?? Collection::empty()),
        ]);
    }

    public function updateStatus($id,UpdateTaskStatusRequest $request)
    {
        return $this->taskService->updateStatus($id, $request->validated());
    }
}

