<?php

namespace Database\Seeders\tenant;

use App\Models\Tenant\Setting;
use Illuminate\Database\Seeder;

class GeneralSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Setting::insert([
            [
                'id' => 1,
                'key' => 'office_name_ar',
                'value' => 'Geexar',
            ],
            [
                'id' => 2,
                'key' => 'office_name_en',
                'value' => 'Geexar',
            ],
            [
                'id' => 3,
                'key' => 'logo',
                'value' => 'settings/logo.png',
            ],
            [
                'id' => 4,
                'key' => 'mobile',
                'value' => '01000000000',
            ],
            [
                'id' => 5,
                'key' => 'secondary_mobile',
                'value' => '01000000000',
            ],
            [
                'id' => 6,
                'key' => 'email',
                'value' => '<EMAIL>',
            ],
            [
                'id' => 7,
                'key' => 'address',
                'value' => 'Egypt, Cairo',
            ],
            [
                'id' => 8,
                'key' => 'user_credit_allowance',
                'value' => 10000,
            ],
            [
                'id' => 9,
                'key' => 'project_credit_allowance',
                'value' => 10000,
            ],
        ]);
    }
}
