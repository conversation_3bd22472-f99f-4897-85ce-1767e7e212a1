<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfficeDueTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'payment_method' => [
                'id' => $this->project->paymentMethod->id,
                'payment_method' => $this->project->paymentMethod->name
            ],

            'total_profits' => $this->project->officeDue->total_profits,
            'recieved_profits' => $this->project->officeDue->recieved_profits,
            'due_profits' => $this->project->officeDue->due_profits,
            'remaining' => $this->remaining,
            'username' => $this->withdraw_by,
        ];
    }
}
