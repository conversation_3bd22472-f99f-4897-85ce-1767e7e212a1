<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\CreateDebtPaymentRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\DebtPaymentResource;
use App\Services\Tenant\DebtPaymentService;

class DebtPaymentController extends Controller
{
    public function __construct(protected DebtPaymentService $debtPaymentService) {}

    public function purchasesDebtPayments(string $debtId)
    {
        $data = $this->debtPaymentService->getPurchasesDebtPayments($debtId);

        return success(new BaseCollection($data['payments'], DebtPaymentResource::class, [
            'total_debts' => $data['total_debts'],
        ]));
    }

    public function refundsDebtPayments(string $debtId)
    {
        $data = $this->debtPaymentService->getRefundsDebtPayments($debtId);

        return success(new BaseCollection($data['payments'], DebtPaymentResource::class, [
            'total_debts' => $data['total_debts'],
        ]));
    }

    public function contractorDebtPayments(string $debtId)
    {
        $data = $this->debtPaymentService->getContractorDebtPayments($debtId);

        return success(new BaseCollection($data['payments'], DebtPaymentResource::class, [
            'total_debts' => $data['total_debts'],
        ]));
    }

    public function store(CreateDebtPaymentRequest $request, string $debtId)
    {
        return $this->debtPaymentService->create($request->validated(), $debtId);
    }
}
