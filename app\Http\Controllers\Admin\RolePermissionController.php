<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateRoleRequest;
use App\Http\Requests\Admin\UpdateRoleRequest;
use App\Http\Resources\Admin\RoleResource;
use App\Http\Resources\BaseCollection;
use App\Services\RoleService;

class RolePermissionController extends Controller
{
    public function __construct(public RoleService $roleService) {}

    public function index()
    {
        $roles = $this->roleService->get();

        return success(new BaseCollection($roles, RoleResource::class));
    }

    public function store(CreateRoleRequest $request)
    {
        $this->roleService->create($request->validated());

        return success(__('Created Successfully'));
    }

    public function show(string $id)
    {
        $role = $this->roleService->getById($id);

        return success(new RoleResource($role));
    }

    public function update(UpdateRoleRequest $request, string $id)
    {
        $this->roleService->update($id, $request->validated());

        return success(__('Updated Successfully'));
    }

    public function destroy(string $id)
    {
        return $this->roleService->delete($id);
    }
}
