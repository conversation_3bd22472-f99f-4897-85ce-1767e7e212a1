<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\HistoryResource;
use App\Http\Resources\BaseCollection;
use App\Services\HistoryService;

class HistoryController extends Controller
{
    public function __construct(public HistoryService $historyService) {}

    public function index()
    {
        $history = $this->historyService->get();
        return success(new BaseCollection($history, HistoryResource::class));
    }
}
