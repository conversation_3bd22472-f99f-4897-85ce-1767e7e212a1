<?php

namespace App\Services;

use App\DTO\SubscriptionData;
use App\Repositories\PlanRepository;
use App\Repositories\SubscriptionRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class SubscriptionService
{
    public function __construct(public SubscriptionRepository $subscriptionRepo, public PlanRepository $planRepo) {}

    public function get()
    {
        $filterable = request()->only(['search']);

        return $this->subscriptionRepo->getPaginated($filterable);
    }

    public function getById(string $id)
    {
        $subscription = $this->subscriptionRepo->findById($id);

        if (! $subscription) {
            throw new NotFoundHttpException;
        }

        return $subscription;
    }

    public function update(array $request, string $id)
    {
        $subscription = $this->getById($id);

        if ($request['total'] < 0) {
            return error('Total cannot be less than the remaining amount.');
        }

        if ($request['type'] === 'renew') {
            if ((int) now()->diffInMonths($subscription->end_date) > 0) {
                return error('Sorry, you cannot renew at the moment. Please wait until the last month before the end date.', 422);
            }

            $subscriptionData = [
                'duration' => $request['duration'],
                'plan_price' => $request['plan_price'],
                'total' => $request['total'],
                'status' => 'Active',
                'start_date' => now()->gt($subscription->end_date)
                    ? now()->format('Y-m-d')
                    : $subscription->end_date->addDay()->format('Y-m-d'),
                'end_date' => now()->gt($subscription->end_date)
                    ? now()->addMonthsNoOverflow((int) $request['duration'])->format('Y-m-d')
                    : $subscription->end_date->addMonthsNoOverflow((int) $request['duration'])->format('Y-m-d'),
            ];
        } else {
            $plan = $this->planRepo->findById($request['plan_id']);

            $subscriptionData = [
                'plan_id' => $request['plan_id'],
                'duration' => $request['duration'],
                'plan_price' => $request['plan_price'],
                'total' => $request['total'],
                'status' => 'Active',
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonthsNoOverflow((int) $request['duration'])->format('Y-m-d'),
                'number_of_projects' => $plan->number_of_projects,
                'number_of_site_engineers' => $plan->number_of_site_engineers,
                'number_of_office_engineers' => $plan->number_of_office_engineers,
                'number_of_accountants' => $plan->number_of_accountants,
                'number_of_project_managers' => $plan->number_of_project_managers,
                'number_of_admins' => $plan->number_of_admins,
                'storage' => $plan->storage,
                'storage_type' => $plan->storage_type,
                'has_domain' => $plan->has_domain,
                'has_free_website' => $plan->has_free_website,
                'has_chat_availability' => $plan->has_chat_availability,
                'is_unlimited_projects' => $plan->is_unlimited_projects,
                'is_unlimited_site_engineers' => $plan->is_unlimited_site_engineers,
                'is_unlimited_office_engineers' => $plan->is_unlimited_office_engineers,
                'is_unlimited_accountants' => $plan->is_unlimited_accountants,
                'is_unlimited_project_managers' => $plan->is_unlimited_project_managers,
                'is_unlimited_admins' => $plan->is_unlimited_admins,
            ];
        }

        $data = SubscriptionData::from($subscriptionData)->all();

        $subscription->update($data);

        return success(__('Updated Successfully'));
    }

    public function deactivate(string $id)
    {
        $subscription = $this->getById($id);

        if ($subscription->status === 'Ended') {
            throw new UnprocessableEntityHttpException('This subscription has already ended and cannot be changed, please renew it.');
        }

        $subscription->update(['status' => $subscription->status === 'Active' ? 'Disabled' : 'Active']);
    }

    protected function calculateRemainingAmount($subscription)
    {
        $remainingMonths = (int) now()->diffInMonths($subscription->end_date); // Calculate difference in months

        if ($subscription->status === 'Ended' || $remainingMonths === 0 || $remainingMonths < 0) {
            return 0;
        } else {
            return $remainingMonths * $subscription->plan_price; // Calculate remaining amount
        }
    }

    public function getProfit()
    {
        $filterable = request()->only(['start_date', 'end_date']);

        return $this->subscriptionRepo->claculateProfit($filterable);
    }

    public function getSalesData(): array
    {
        $currentYear = now()->year;
        $lastYear = $currentYear - 1;

        $salesData = $this->subscriptionRepo->getGroupedSubscriptions();

        $thisYearData = $salesData->where('year', $currentYear)->pluck('total', 'month');
        $lastYearData = $salesData->where('year', $lastYear)->pluck('total', 'month');

        return [
            'this_year' => $thisYearData,
            'last_year' => $lastYearData,
        ];
    }

    public function getSubscriptionsCount(): array
    {
        $expiredSubscriptions = $this->subscriptionRepo->getCountWhere(['status', 'Ended']);
        $activeSubscriptions = $this->subscriptionRepo->getCountWhere(['status', 'Active']);

        return [
            'active' => $activeSubscriptions,
            'expired' => $expiredSubscriptions,
        ];
    }

    public function getSubscriptionPlan()
    {
        $subscriptions = $this->subscriptionRepo->getGroupedByPlan();

        $totalSubscriptions = $subscriptions->sum('count');

        $data = $subscriptions->map(function ($subscription) use ($totalSubscriptions) {
            return [
                'name' => $subscription->plan->name,
                'percentage' => (string) round(($subscription->count / $totalSubscriptions) * 100, 2),
                'offices_count' => $subscription->office_count,
            ];
        });

        return [
            'labels' => $data->pluck('name'),
            'values' => $data->pluck('percentage'),
            'offices_count' => $data->pluck('offices_count'),
        ];
    }
}
