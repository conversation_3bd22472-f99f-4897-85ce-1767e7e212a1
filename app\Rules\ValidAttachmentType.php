<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Http\UploadedFile;

class ValidAttachmentType implements ValidationRule
{
    protected string $type;

    protected array $panelExtensions = ['dwg', 'dxf', 'max', 'fbx', 'obj', '3ds'];
    protected array $defaultExtensions = ['pdf', 'jpeg', 'jpg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'tif', 'heic', 'heif', 'dwg', 'dxf'];

    public function __construct(string $type)
    {
        $this->type = $type;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$value instanceof UploadedFile) {
            $fail(__('The :attribute must be a valid file.'));
            return;
        }

        $allowedExtensions = $this->type === 'panel'
            ? $this->panelExtensions
            : $this->defaultExtensions;

        $extension = strtolower($value->getClientOriginalExtension());

        if (!in_array($extension, $allowedExtensions)) {
            $fail(__('The :attribute must be a file of type: :types.', [
                'types' => implode(', ', $allowedExtensions),
            ]));
        }
    }
}
