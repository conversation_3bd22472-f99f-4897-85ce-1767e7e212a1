<?php

namespace App\Services\Tenant;

use App\DTO\GroupNotificationData;
use App\Notifications\GroupNotification;
use App\Repositories\Tenant\GroupNotificationRepository;
use App\Repositories\Tenant\UserRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class GroupNotificationService
{
    public function __construct(
        public GroupNotificationRepository $groupNotificationRepo,
        public UserRepository $userRepo,
    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'date_from', 'date_to']);

        return $this->groupNotificationRepo->getPaginated($filterable);
    }

    public function getById($id)
    {
        $notification = $this->groupNotificationRepo->findById($id);

        if (! $notification) {
            throw new NotFoundHttpException;
        }

        return $notification;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            // Add sender name from the authenticated user
            $request['sender_name'] = auth()->user()->name;

            // Prepare notification data
            $data = GroupNotificationData::from($request)->all();

            // Create the notification
            $notification = $this->groupNotificationRepo->create($data);

            // Handle users in the receivers array
            if (! empty($request['receivers'])) {
                foreach ($request['receivers'] as $userId) {
                    $user = $this->userRepo->findUser($userId);

                    if ($user) {
                        $user->notify(new GroupNotification($request)); // Send notification to the user

                        $user->receivers()->create([
                            'group_notification_id' => $notification->id,
                        ]);
                    }
                }
            }

            // Commit the transaction
            DB::commit();

            return success(__('Notification Sent Successfully'));
        } catch (Exception $e) {
            // Rollback on error
            DB::rollBack();
            logger($e->getMessage());

            return error('Failed to create notification.', 422);
        }
    }

    public function delete($id)
    {
        $notification = $this->getById($id);

        return $notification->delete();
    }

    public function resend($id)
    {
        $notification = $this->getById($id);

        if ($notification->notification_receivers->isEmpty()) {
            return error(__('No receivers found for this notification.'), 422);
        }

        $clone = $notification->replicate();

        $clone->save();

        $data = [
            'title' => $notification->title,
            'body' => $notification->body,
            'sender_name' => $notification->sender_name,
            'receivers' => $notification->notification_receivers->map(function ($receiver) {
                return [
                    'type' => $receiver->receiverable_type,
                    'id' => $receiver->receiverable_id,
                ];
            })->toArray(),
        ];

        // Resend notifications to all receivers
        foreach ($notification->notification_receivers as $receiver) {
            $receiverModel = $receiver->receiverable;

            if ($receiverModel) {
                $receiverModel->notify(new GroupNotification($data));

                $receiverModel->receivers()->create([
                    'group_notification_id' => $clone->id,
                ]);
            }
        }

        return success(__('Notification Resent Successfully'));
    }
}
