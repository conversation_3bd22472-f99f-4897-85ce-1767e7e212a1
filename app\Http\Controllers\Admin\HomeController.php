<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\HomeFilterRequest;
use App\Http\Resources\Admin\HistoryResource;
use App\Services\AdminService;
use App\Services\HistoryService;
use App\Services\OfficeService;
use App\Services\PlanService;
use App\Services\SubscriptionService;

class HomeController extends Controller
{
    public function __construct(
        public OfficeService $officeService,
        public AdminService $adminService,
        public PlanService $planService,
        public SubscriptionService $subscriptionService,
        public HistoryService $historyService
    ) {}

    public function __invoke(HomeFilterRequest $request)
    {
        $checkHistoryPermission = auth()->user()->permissions()->where(function ($query) {
            $query->where('group', 'history')
                ->where('name', 'index');
        })->exists();

        $offices = $this->officeService->count();
        $admins = $this->adminService->count();
        $plans = $this->planService->count();
        $subscriptionProfit = $this->subscriptionService->getProfit();
        $salesChart = $this->subscriptionService->getSalesData();
        $latestActivities = $this->historyService->getLimit();
        $subscriptionsCount = $this->subscriptionService->getSubscriptionsCount();
        $subscriptionsPlan = $this->subscriptionService->getSubscriptionPlan();

        return success([
            'offices' => $offices,
            'admins' => $admins,
            'plans' => $plans,
            'profit' => $subscriptionProfit,
            'projects' => 0,
            'salesChart' => $salesChart,
            'subscriptions' => $subscriptionsCount,
            'subscription_plan' => $subscriptionsPlan,
            'activities' => $checkHistoryPermission ? HistoryResource::collection($latestActivities) : null,
        ]);
    }
}
