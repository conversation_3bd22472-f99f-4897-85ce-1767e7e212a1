<?php

namespace App\DTO\Tenant;

use Spa<PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Optional;

class RefundData extends Data
{
    public function __construct(
        public int|Optional $project_id,
        public int|null|Optional $purchase_id,
        public string|Optional $type,
        public string $name,
        public string|optional $number,
        public string $date,
        public int $partner_id,
        public array|Optional $item_ids,
        public string|Optional $status,

        public array|Optional $qty_refunded,
        public float|Optional $total_before_discount,
        public float|Optional $total_after_discount,
        public float|Optional $paid_amount,
        public float|Optional $remaining_amount,
        public string|null $discount_type,
        public float|Optional $discount_value,
        public string|null $notes,
    ) {}
}
