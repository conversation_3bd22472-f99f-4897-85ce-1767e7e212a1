<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\ClientService;
use App\Http\Resources\Tenant\DDLResource;
use App\Http\Resources\Tenant\Admin\ClientResource;
use App\Http\Requests\Tenant\Admin\CreateClientRequest;
use App\Http\Requests\Tenant\Admin\UpdateClientRequest;

class ClientController extends Controller
{
    public function __construct(public ClientService $clientService) {}

    public function index()
    {
        $clients = $this->clientService->index();

        return success(new BaseCollection($clients, ClientResource::class));
    }

    public function show($id)
    {
        $client = $this->clientService->show($id);

        return success(new ClientResource($client));
    }

    public function store(CreateClientRequest $request)
    {
        return $this->clientService->create($request->validated());
    }

    public function update(string $id, UpdateClientRequest $request)
    {
        return $this->clientService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        $this->clientService->delete($id);

        return success(__('deleted successfully'));
    }

    public function archive()
    {
        $clients = $this->clientService->getArchived();

        return success(new BaseCollection($clients, ClientResource::class));
    }

    public function restore($id)
    {
        $this->clientService->restore($id);

        return success(__('restored successfully'));
    }

    public function forceDelete(string $id)
    {
        $this->clientService->forceDelete($id);

        return success(__('deleted successfully'));
    }

    public function ProjectsDDl()
    {
        $projects = $this->clientService->searchddl();

        return success($projects);
    }

    public function ddl()
    {
        $partners = $this->clientService->ddl();

        return success(DDLResource::collection($partners));
    }
}
