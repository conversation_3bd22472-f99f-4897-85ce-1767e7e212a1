<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Package;

class PackageRepository
{
    public function __construct(public Package $model) {}

    public function getPaginated(int $limit = 10)
    {
        return $this->model
            ->when(request('search'), function ($query) {
                $searchTerm = '%'.request('search').'%';

                return $query->where(function ($subQuery) use ($searchTerm) {
                    $subQuery->where('name_ar', 'like', $searchTerm)
                        ->orWhere('name_en', 'like', $searchTerm);
                });
            })
            ->when(request('status'), function ($query) {
                return $query->where('status', request('status'));
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(Package $package, array $data)
    {

        $package->update($data);
    }

    public function delete(Package $package)
    {
        $package->delete();
    }

    public function getAll(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }
}
