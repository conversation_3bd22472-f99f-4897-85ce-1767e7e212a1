<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\PackageData;
use App\Repositories\Tenant\PackageRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PackageService
{
    public function __construct(
        public PackageRepository $packageRepo,

    ) {}

    // List paginated pakcages
    public function index()
    {
        return $this->packageRepo->getPaginated();
    }

    // Show a specific pakcage by ID
    public function show($id)
    {
        $package = $this->packageRepo->findById($id);

        if (! $package) {
            throw new NotFoundHttpException;
        }

        return $package;

    }

    // Create a new pakcage
    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = PackageData::from($request)->all();

            $pakcage = $this->packageRepo->create($data);

            DB::commit();

            return $pakcage;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'pakcage creation failed. Please check your input.');
        }
    }

    // Update an existing pakcage
    public function update($id, array $request)
    {
        DB::beginTransaction();

        $pakcage = $this->packageRepo->findById($id);

        if (! $pakcage) {
            throw new NotFoundHttpException;
        }

        try {
            $data = PackageData::from($request)->all();

            $this->packageRepo->update($pakcage, $data);

            DB::commit();

            return $pakcage;
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'pakcage update failed. Please check your input.');
        }
    }

    // Delete a pakcage
    public function delete($id)
    {
        DB::beginTransaction();
        $pakcage = $this->packageRepo->findById($id);

        if (! $pakcage) {
            throw new NotFoundHttpException;
        }
        try {
            $this->packageRepo->delete($pakcage);

            DB::commit();

            return response()->json(['message' => 'pakcage deleted successfully']);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            abort(422, 'pakcage deletion failed.');
        }
    }

    public function ddl(?array $columns = ['id', 'name'])
    {
        return $this->packageRepo->getAll($columns);
    }
}
