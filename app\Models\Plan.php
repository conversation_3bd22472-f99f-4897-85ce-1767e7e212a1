<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Plan extends Model implements HasMedia
{
    use SoftDeletes , InteractsWithMedia;

    protected $fillable = [
        'image',
        'name_ar',
        'name_en',
        'price',
        'number_of_projects',
        'number_of_site_engineers',
        'number_of_office_engineers',
        'number_of_accountants',
        'number_of_project_managers',
        'number_of_admins',
        'storage',
        'storage_type',
        'status',
        'has_domain',
        'has_free_website',
        'has_chat_availability',
        'is_unlimited_projects',
        'is_unlimited_site_engineers',
        'is_unlimited_office_engineers',
        'is_unlimited_accountants',
        'is_unlimited_project_managers',
        'is_unlimited_admins',
    ];

    public function getNameAttribute()
    {
        return $this->{'name_'.app()->getLocale()};
    }

    public function getImageAttribute()
    {
        return $this->media->first();
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    
}
