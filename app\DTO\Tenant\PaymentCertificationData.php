<?php

namespace App\DTO\Tenant;

use Spa<PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Optional;

class PaymentCertificationData extends Data
{
    public function __construct(
        public string|optional $title,
        public array|optional $images,
        public string|optional $date,
        public string|optional $created_by,
        public string|optional $notes,

        public int|optional $contractor_id,
        public int|optional $project_id,
        public int|optional $item_id,
        public array $items, // Array of unit_id, qty, price, item_name
        public float|Optional $total,
        

    ) {}
}
