<?php

namespace App\Repositories\Tenant;

use Carbon\Carbon;
use App\Models\Tenant\History;

class HistoryRepository
{
    public function __construct(public History $model) {}

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->when(!empty($filters['project']), function ($query) use ($filters) {
            return $query->where('project_id', $filters['project']);
        })->when(isset($filters['search']), function ($query) use ($filters) {
            return $query->where(function ($query) use ($filters) {
                $query->where('causer_name', 'like', "%{$filters['search']}%")
                    ->orWhere('model', 'like', "%{$filters['search']}%")
                    ->orWhere('message_ar', 'like', "%{$filters['search']}%")
                    ->orWhere('message_en', 'like', "%{$filters['search']}%");
            });
        })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
            if (isset($filters['date_to']) && !isset($filters['date_from'])) {
                return $query->whereDate('created_at', '<=', Carbon::parse($filters['date_to'])->endOfDay());
            }
            if (isset($filters['date_from']) && !isset($filters['date_to'])) {
                return $query->whereDate('created_at', '>=', Carbon::parse($filters['date_from'])->startOfDay());
            }
            if (isset($filters['date_from']) && isset($filters['date_to'])) {
                $dates = [
                    Carbon::parse($filters['date_from'])->startOfDay(),
                    Carbon::parse($filters['date_to'])->endOfDay(),
                ];
                return $query->whereBetween('created_at', $dates);
            }
        })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function limit(int $limit)
    {
        return $this->model->limit($limit)->orderByDesc('created_at')->get();
    }
}
