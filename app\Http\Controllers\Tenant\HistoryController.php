<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\HistoryService;
use App\Http\Resources\Admin\HistoryResource;

class HistoryController extends Controller
{
    public function __construct(public HistoryService $historyService) {}

    public function index()
    {
        $history = $this->historyService->get();
        return success(new BaseCollection($history, HistoryResource::class));

    }
}
