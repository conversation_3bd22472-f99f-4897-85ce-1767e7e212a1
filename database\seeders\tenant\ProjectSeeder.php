<?php

namespace Database\Seeders\tenant;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProjectSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('projects')->insert([
            [
                'name' => 'Residential Project Alpha',
                'type' => 'residential',
                'size' => 'medium',
                'area' => 1200,
                'payment_method_id' => 1,
                'package_id' => 1,
                'office_ratio' => 15,
                'site_engineer_ratio' => 10,
                'amount_per_meter' => 50.5,
                'total' => 60600,
                'withdraw_limit' => 3000,
                'start_date' => now()->subMonths(2),
                'end_date' => now()->addMonths(4),
                'status' => 'in_progress',
                'description' => 'A medium-scale residential project located in the central city.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Commercial Tower Beta',
                'type' => 'commercial',
                'size' => 'large',
                'area' => 2500,
                'payment_method_id' => 2,
                'package_id' => 2,
                'office_ratio' => 20,
                'site_engineer_ratio' => 15,
                'amount_per_meter' => 100.0,
                'total' => 250000,
                'withdraw_limit' => 5000,
                'start_date' => now()->subMonths(1),
                'end_date' => now()->addMonths(8),
                'status' => 'new',
                'description' => 'A high-rise commercial tower under initial planning.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Industrial Factory Gamma',
                'type' => 'industrial',
                'size' => 'large',
                'area' => 5000,
                'payment_method_id' => 3,
                'package_id' => 3,
                'office_ratio' => 25,
                'site_engineer_ratio' => 20,
                'amount_per_meter' => 75.75,
                'total' => 378750,
                'withdraw_limit' => 7500,
                'start_date' => now()->subMonths(3),
                'end_date' => now()->addMonths(6),
                'status' => 'in_progress',
                'description' => 'Large industrial factory construction in the outskirts.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Infrastructure Bridge Delta',
                'type' => 'infrastructure',
                'size' => 'small',
                'area' => 800,
                'payment_method_id' => 1,
                'package_id' => null,
                'office_ratio' => 10,
                'site_engineer_ratio' => 5,
                'amount_per_meter' => 120.0,
                'total' => 96000,
                'withdraw_limit' => 2000,
                'start_date' => now()->subMonths(4),
                'end_date' => now(),
                'status' => 'completed',
                'description' => 'Small-scale infrastructure bridge that was recently completed.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
