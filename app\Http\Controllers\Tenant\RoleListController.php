<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\RoleResource;
use App\Services\Tenant\RoleService;

class RoleListController extends Controller
{
    public function __construct(public RoleService $roleService) {}

    public function __invoke()
    {
        $roles = $this->roleService->getActiveRoles();

        return success(RoleResource::collection($roles));
    }
}
