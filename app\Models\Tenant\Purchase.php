<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Purchase extends Model implements HasMedia
{
    use SoftDeletes , InteractsWithMedia;

    protected $fillable = [
        'number',
        'project_id',
        'partner_id',
        'type',
        'name',
        'date',
        'total_before_discount',
        'discount_type',
        'discount_amount',
        'total_after_discount',
        'status',
        'paid_amount',
        'remaining_amount',
        'notes',
        'created_by',
        'permanent_deleted',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class)->withTrashed();
    }

    public function invoiceCategory()
    {
        return $this->morphMany(InvoiceCategory::class, 'invoiceable');
    }

    public function items()
    {
        return $this->hasManyThrough(
            PurchaseItem::class,
            InvoiceCategory::class,
            'invoiceable_id', // Foreign key on `invoice_categories` table
            'invoice_category_id', // Foreign key on `purchase_items` table
            'id', // Local key on `purchases` table
            'id' // Local key on `invoice_categories` table
        )->where('invoiceable_type', Purchase::class);
    }

  

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }
}
