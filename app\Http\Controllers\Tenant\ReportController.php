<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\CreateReportRequest;
use App\Http\Requests\Tenant\UpdateReportRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\ReportDetailsResource;
use App\Http\Resources\Tenant\ReportResource;
use App\Services\Tenant\ReportService;

class ReportController extends Controller
{
    public function __construct(public ReportService $reportService) {}

    public function index()
    {
        $reports = $this->reportService->get();

        return success(new BaseCollection($reports, ReportResource::class));
    }

    public function store(CreateReportRequest $request)
    {
        return $this->reportService->create($request->validated());
    }

    public function show(string $id)
    {
        $report = $this->reportService->getById($id);

        return success(new ReportDetailsResource($report));
    }

    public function update(UpdateReportRequest $request, string $id)
    {
        return $this->reportService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->reportService->delete($id);
    }
    
    public function restore(string $id)
    {
        return $this->reportService->restore($id);
    }
    
    public function forceDelete(string $id)
    {
        return $this->reportService->forceDelete($id);
    }
}
