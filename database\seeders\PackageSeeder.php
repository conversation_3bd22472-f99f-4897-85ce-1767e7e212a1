<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PackageSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('packages')->insert([
            [
                'name_ar' => 'Basic Package',
                'name_en' => 'Basic Package',
                'price_per_meter' => 50.0,
                'status' => 'Active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name_ar' => 'Standard Package',
                'name_en' => 'Standard Package',
                'price_per_meter' => 75.0,
                'status' => 'Active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name_ar' => 'Premium Package',
                'name_en' => 'Premium Package',
                'price_per_meter' => 100.0,
                'status' => 'Active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name_ar' => 'Custom Package',
                'name_en' => 'Custom Package',
                'price_per_meter' => 0.0,
                'status' => 'Disabled',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
