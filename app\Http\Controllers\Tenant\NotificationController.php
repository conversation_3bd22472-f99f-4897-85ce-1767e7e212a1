<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateNotificationRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\GroupNotificationResource;
use App\Services\Tenant\GroupNotificationService;

class NotificationController extends Controller
{
    public function __construct(public GroupNotificationService $groupNotificationService) {}

    public function index()
    {
        $notifications = $this->groupNotificationService->get();

        return success(new BaseCollection($notifications, GroupNotificationResource::class));
    }

    public function store(CreateNotificationRequest $request)
    {
        return $this->groupNotificationService->create($request->validated());
    }

    public function show(string $id)
    {
        $notification = $this->groupNotificationService->getById($id);

        return success(new GroupNotificationResource($notification));
    }

    public function destroy(string $id)
    {
        $this->groupNotificationService->delete($id);

        return success(__('Deleted Successfully'));
    }

    public function resend(string $id)
    {
        return $this->groupNotificationService->resend($id);
    }
}
