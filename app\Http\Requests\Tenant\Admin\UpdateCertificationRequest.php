<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCertificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => 'nullable|date',
            'title' => 'nullable|string|min:3|max:30',
            'contractor_id' => ['nullable', Rule::exists('partners', 'id')->where('type', 'contractor')],
            'project_id' => 'nullable|exists:projects,id',
            'item_id' => 'required|exists:items,id', // Single item ID is required
            'items' => 'required|array|min:1', // Ensure at least one item exists
            'items.*.unit_id' => 'required|exists:units,id',
            'items.*.price' => 'required|numeric|min:0|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'items.*.qty' => 'required|numeric|max_digits:10',
            'items.*.item_name' => 'nullable|string|min:3|max:100',
            'images' => ['nullable', 'array', 'min:1', 'max:10'],
            'images.*' => ['nullable', 'mimes:png,jpg,jpeg', 'max:5120'],
        ];
    }
}
