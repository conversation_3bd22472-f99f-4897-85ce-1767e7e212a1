<?php

namespace App\Models\Tenant;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\PasswordResetCode;
use Database\Factories\UserFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements HasMedia
{
    /** @use HasFactory<UserFactory> */
    use HasApiTokens, HasFactory, HasRoles, InteractsWithMedia, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'image',
        'status',
        'type',
        'can_access_all_projects',
        'balance',
        'details',
        'national_id',
        'check_code_attempts',
        'role_id',
        'deleted_at',
        'permanent_deleted',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function scopeWithoutSuperAdmin($query)
    {
        $query->where(function ($query) {
            $query->where('type', '!=', 'admin')
                ->orWhereHas('role', function ($query) {
                    $query->where('name', '!=', 'Super Admin');
                });
        });
    }

    public function isSuperAdmin(): bool
    {
        return $this->type === 'admin' && $this->role?->name === 'Super Admin';
    }

    public function updateDeviceToken(array $data)
    {
        $this->deviceTokens()->updateOrCreate([
            'device_id' => $data['device_id'],
        ], [
            'token' => $data['token'],
        ]);
    }

    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class);
    }

    public function deviceTokens()
    {
        return $this->morphMany(DeviceToken::class, 'tokenable');
    }

    public function receivers(): MorphMany
    {
        return $this->morphMany(NotificationReceiver::class, 'receiverable');
    }

    public function rooms()
    {
        return $this->morphMany(RoomUser::class, 'user');
    }

    public function messages()
    {
        return $this->morphMany(Message::class, 'sender');
    }

    // public function media()
    // {
    //     return $this->morphMany(Media::class, 'mediable');
    // }

    public function getImageAttribute()
    {
        return $this->getFirstMedia('users');
    }

    public function passwordResetCode()
    {
        return $this->morphOne(PasswordResetCode::class, 'resettable');
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function balance(): MorphOne
    {
        return $this->morphOne(UserBalance::class, 'userable');
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function userTransactions()
    {
        return $this->morphMany(Transaction::class, 'userable');
    }

    public function purchases()
    {
        return $this->userTransactions()
            ->where('transactionable_type', Purchase::class);
    }

    public function contractorPayments()
    {
        return $this->userTransactions()
            ->where('transactionable_type', ContractorPayment::class);
    }

    public function messageReads()
    {
        return $this->hasMany(MessageUserRead::class);
    }
}
