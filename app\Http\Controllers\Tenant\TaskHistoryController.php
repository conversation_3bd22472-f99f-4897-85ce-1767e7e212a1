<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\TaskHistoryService;
use App\Http\Requests\Tenant\Admin\CreateTaskHistoryRequest;

class TaskHistoryController extends Controller
{
    protected $taskHistoryService;

    public function __construct(TaskHistoryService $taskHistoryService)
    {
        $this->taskHistoryService = $taskHistoryService;
    }

    public function addComment(CreateTaskHistoryRequest $request)
    {

        return $this->taskHistoryService->addComment($request->validated());
    }
}
