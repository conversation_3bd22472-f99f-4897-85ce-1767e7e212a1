<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateCertificationRequest;
use App\Http\Requests\Tenant\Admin\PayCertificateRequest;
use App\Http\Requests\Tenant\Admin\UpdateCertificationRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\CertificationResource;
use App\Services\Tenant\PaymentCertificateService;

class PaymentCertificateController extends Controller
{
    public function __construct(protected PaymentCertificateService $PaymentCertificateService)
    {
        //
    }

    public function index()
    {
        $certificates = $this->PaymentCertificateService->get();

        $totalCertificates = $this->PaymentCertificateService->getTotalCertificate();

        return success(new BaseCollection($certificates, CertificationResource::class, [
            'total_certificates' => $totalCertificates,
        ]));
    }

    public function show(string $id)
    {
        $certificate = $this->PaymentCertificateService->getById($id);

        return success(new CertificationResource($certificate));
    }

    public function store(CreateCertificationRequest $request)
    {
        return $this->PaymentCertificateService->create($request->validated());
    }

    public function update(UpdateCertificationRequest $request, string $id)
    {
        return $this->PaymentCertificateService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->PaymentCertificateService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->PaymentCertificateService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->PaymentCertificateService->forceDelete($id);
    }

    public function pay($id, PayCertificateRequest $request)
    {
        return $this->PaymentCertificateService->pay($id, $request->validated());
    }
}
