<?php

namespace App\Services;

use App\DTO\TenantData;
use App\Models\Tenant\User;
use App\Repositories\OfficeRepository;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\UserRepository;
use App\Repositories\TenantRepository;
use Database\Seeders\PackageSeeder;
use Database\Seeders\RoleSeeder;
use Database\Seeders\tenant\PaymentMethodSeeder;
use Illuminate\Support\Facades\Artisan;
use Stancl\Tenancy\Jobs\MigrateDatabase;
use WebReinvent\CPanel\CPanel;

class TenantService
{
    public function __construct(
        protected TenantRepository $tenantRepository,
        protected UserRepository $userRepository,
        protected SettingRepository $settingRepository,
        protected OfficeRepository $officeRepository
    ) {}

    public function create(array $request)
    {
        $office = $this->officeRepository->findById($request['office_id']);

        // Map request data
        $data = TenantData::from($request)->all();

        $tenant = $this->tenantRepository->create($data);

        $tenant->domains()->create(['domain' => $data['domain_name']]);

        if (app()->environment('production')) {
            $cpanel = new CPanel;
            $cpanel->createDatabase($data['tenancy_db_name']);
            $cpanel->setAllPrivilegesOnDatabase('engsaas_laravel', $data['tenancy_db_name']);
            dispatch(new MigrateDatabase($tenant));
        }

        tenancy()->initialize($tenant);

        // create User
        $this->userRepository->create($request);

        $this->settingRepository->create([
            'office_name_ar' => $office?->name_ar,
            'office_name_en' => $office?->name_en,
            'logo' => null,
            'mobile' => $office?->phone,
            'secondary_mobile' => $office?->phone,
            'email' => $office?->email,
            'address' => $office?->address,
        ], 'general');

        $this->settingRepository->create([
            'user_allow_withdraw' => '0',
            'project_allow_withdraw' => '0',
            'user_overdraft_limit' => '0',
            'project_overdraft_limit' => '0',
            'project_unlimitid' => '1',
            'user_unlimitid' => '1',
        ], 'withdraw');

        $this->runTenantSeeders($tenant->id);

        tenancy()->end();

        return $tenant;
    }

    protected function runTenantSeeders($tenantKey = null)
    {
        // Use Artisan to call specific seeders
        Artisan::call('tenants:seed', [
            '--tenants' => $tenantKey,
            '--class' => RoleSeeder::class,
            '--force' => true,
        ]);

        Artisan::call('tenants:seed', [
            '--tenants' => $tenantKey,
            '--class' => PackageSeeder::class,
            '--force' => true,
        ]);

        Artisan::call('tenants:seed', [
            '--tenants' => $tenantKey,
            '--class' => PaymentMethodSeeder::class,
            '--force' => true,
        ]);
    }
}
