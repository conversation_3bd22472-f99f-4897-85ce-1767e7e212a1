<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Admin;

class AdminRepository
{
    public function __construct(public Admin $model) {}

    public function findByEmailOrPhone($emailOrPhone)
    {
        return $this->model
            ->where('status', 'Active')
            ->where(function ($query) use ($emailOrPhone) {
                $query->where('email', $emailOrPhone)
                    ->orWhere('phone', $emailOrPhone);
            })
            ->first();
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function getPaginated(int $limit = 10, array $filters = [])
    {
        return $this->model
            ->when(isset($filters['search']), function ($query) use ($filters) {
                return $query->where('name', 'like', "%{$filters['search']}%");
            })->when(isset($filters['status']), function ($query) use ($filters) {
                return $query->where('status', $filters['status']);
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function count(array $fillters = [])
    {
        return $this->model->when(isset($fillters['start_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '>=', $fillters['start_date']);
        })->when(isset($fillters['end_date']), function ($query) use ($fillters) {
            return $query->whereDate('created_at', '<=', $fillters['end_date']);
        })->count();
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function all()
    {
        return $this->model->get();
    }

    public function getSuperAdmin()
    {
        return $this->model->where('role_id', 1)->first();
    }
}
