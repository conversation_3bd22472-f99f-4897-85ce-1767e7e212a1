<?php

namespace App\Services\Tenant;

use App\DTO\AdminData;
use App\Repositories\Tenant\AdminRepository;
use App\Repositories\Tenant\PermissionRepository;
use App\Repositories\Tenant\RoleRepository;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AdminService
{
    public function __construct(
        public AdminRepository $adminRepo,
        public RoleRepository $roleRepo,
        public PermissionRepository $permissionRepo
    ) {}

    public function get()
    {
        $filterable = request()->only(['search', 'status']);

        if (request()->type === 'archive') {
            return $this->adminRepo->getTrashedWithPaginate();
        } else {
            return $this->adminRepo->getPaginated(filters: $filterable);
        }
    }

    public function getById($id)
    {
        $admin = $this->adminRepo->findById($id);

        if (! $admin) {
            throw new NotFoundHttpException('Admin not found');
        }

        return $admin;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $data = AdminData::from($request)->all();

            $admin = $this->adminRepo->create($data);

            if (request()->hasFile('image')) {
                if ($admin->image) {
                    Storage::disk('public')->delete($admin->image->path);
                }

                $upload = uploadFile(path: 'admins');

                $admin->media()->updateOrCreate([
                    'mediable_id' => $admin->id,
                    'mediable_type' => get_class($admin),
                ], [
                    'name' => $upload['name'],
                    'path' => $upload['path'],
                    'type' => $upload['type'],
                    'extension' => $upload['extension'],
                ]);
            }

            if (request('role_id')) {
                $role = $this->roleRepo->findById(request('role_id'));

                if (! $role || $role->name === 'Super Admin') {
                    return error(__('You cannot assign this role to an admin.'), 422);
                }
            }

            if (request('permissions')) {
                $permissions = $this->permissionRepo->getWhereIn(request('permissions'))->pluck('id')->toArray();

                $admin->syncPermissions($permissions);
            }

            $subscription = tenant()->subscription;

            if (! $subscription->is_unlimited_admins && $subscription->number_of_admins > 0) {
                $subscription->decrement('number_of_admins');
            }

            $subscription->save();

            DB::commit();

            return success(__('Created Successfully'));
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error('Admin creation failed. Please check your input.', 422);
        }
    }

    public function update($id, array $request)
    {
        DB::beginTransaction();

        try {
            $admin = $this->getById($id);

            $data = AdminData::from($request)->all();

            if (request('role_id')) {
                $role = $this->roleRepo->findById(request('role_id'));

                if (! $role || $role->name === 'Super Admin') {
                    return error(__('You cannot assign this role to an admin.'), 422);
                }
            }

            $admin->update($data);

            if (request()->hasFile('image')) {
                if ($admin->image) {
                    Storage::disk('public')->delete($admin->image->path);
                }

                $upload = uploadFile(path: 'admins');

                $admin->media()->updateOrCreate([
                    'mediable_id' => $admin->id,
                    'mediable_type' => get_class($admin),
                ], [
                    'name' => $upload['name'],
                    'path' => $upload['path'],
                    'type' => $upload['type'],
                    'extension' => $upload['extension'],
                ]);
            }

            $permissions = $this->permissionRepo->getWhereIn($request['permissions']);

            $admin->syncPermissions($permissions);

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (NotFoundHttpException $e) {
            DB::rollBack();

            return error($e->getMessage(), 404);
        } catch (Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error('Admin update failed. Please check your input.', 422);
        }
    }

    public function delete($id)
    {
        $admin = $this->getById($id);

        return $admin->delete();
    }

    public function restore($id)
    {
        $admin = $this->adminRepo->findTrashedById($id);

        if (! $admin) {
            throw new NotFoundHttpException('Admin not found');
        }

        return $admin->restore();
    }

    public function forceDelete($id)
    {
        $admin = $this->adminRepo->findTrashedById($id);

        if (! $admin) {
            throw new NotFoundHttpException('Admin not found');
        }

        $admin->forceDelete();

        $subscription = tenant()->subscription;

        if (! $subscription->is_unlimited_admins && $subscription->number_of_admins > 0) {
            $subscription->increment('number_of_admins');
        }

        $subscription->save();

        return success(__('Deleted Successfully'));
    }

    public function getAll()
    {
        return $this->adminRepo->all();
    }
}
