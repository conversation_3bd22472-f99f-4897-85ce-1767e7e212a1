<?php

declare(strict_types=1);

use App\Models\Tenant\Setting;
use Illuminate\Pagination\LengthAwarePaginator;

function apiResponse($data = [], string $message = '', int $status = 200)
{
    return response()->json([
        'data' => $data,
        'message' => $message,
        'success' => in_array($status, [200, 201]),
    ], $status);
}

function success($data = [])
{
    return response()->json([
        'data' => $data,
    ], 200);
}

function error($data = [], $status = 400)
{
    return response()->json([
        'data' => $data,
    ], $status);
}

function paginatedResponse(LengthAwarePaginator $paginator, $resource = null)
{
    return response()->json([
        'data' => $resource::collection($paginator->items()),
        'meta' => [
            'total' => $paginator->total(),
            'per_page' => $paginator->perPage(),
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
        ],
        'success' => true,
    ], 200);
}

function addHistory($message)
{
    $user = auth()->user();
    $user->histories()->create([
        'message' => $message,
    ]);
}

function uploadFile(string $path = 'public', string $fileName = 'image')
{
    $file = request()->file($fileName);
    $name = $file->getClientOriginalName();
    $extension = $file->getClientOriginalExtension();
    $type = $file->getClientMimeType();
    $path = $file->storeAs($path, time().'.'.$name, 'public');

    return [
        'name' => $name,
        'path' => $path,
        'type' => $type,
        'extension' => $extension,
    ];
}

function uploadMultipleFiles(string $path, string $key = 'images')
{
    $files = request()->file($key);

    $paths = [];

    // Define known image extensions
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];

    foreach ($files as $file) {
        $name = $file->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $type = $file->getClientMimeType();
        $size = $file->getSize(); // Returns the size in bytes

        // If the extension is NOT an image, override the type
        if (!in_array($extension, $imageExtensions)) {
            $type = "{$extension}/{$name}";
        }

        $filePath = $file->storeAs($path, time().'_'.$name, 'public');

        $paths[] = [
            'name' => $name,
            'path' => $filePath,
            'type' => $type,
            'extension' => $extension,
            'size' => $size,
        ];
    }

    return $paths;
}


function generateInvoiceNumber()
{
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    $base = 62;

    $calcBase = function ($num) use ($characters, $base) {
        $result = '';
        while ($num > 0) {
            $result = $characters[$num % $base].$result;
            $num = floor($num / $base);
        }

        return $result ?: '0';
    };

    // Generate unique identifier and timestamp
    $uuid = $calcBase(hexdec(bin2hex(random_bytes(4))));
    $timestamp = $calcBase(time());

    // Concatenate uuid and timestamp, then trim or pad to 10 characters
    $invoice = strtoupper($uuid.$timestamp);

    // Trim to 10 characters if longer, or pad if shorter
    return substr($invoice, 0, 10);
}

function getSetting($key)
{
    return Setting::where('key', $key)->first()?->value;
}
