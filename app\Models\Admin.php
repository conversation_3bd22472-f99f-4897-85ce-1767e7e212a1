<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>tie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;

class Admin extends Authenticatable implements HasMedia
{
    use HasApiTokens, HasFactory, HasRoles, Notifiable, InteractsWithMedia;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'status',
        'role_id',
        'check_code_attempts',
    ];

    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    public function updateDeviceToken(array $data)
    {
        $this->deviceTokens()->updateOrCreate([
            'device_id' => $data['device_id'],
        ], [
            'token' => $data['token'],
        ]);
    }

    public function getImageAttribute()
    {
        return $this->media->first();
    }

    public function scopeWithoutSuperAdmin($query)
    {
        $query->whereRelation('role', 'name', '!=', 'Super Admin');
    }

    public function passwordResetCode()
    {
        return $this->morphOne(PasswordResetCode::class, 'resettable');
    }

    public function deviceTokens()
    {
        return $this->morphMany(DeviceToken::class, 'tokenable');
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }


    public function receivers()
    {
        return $this->morphMany(NotificationReceiver::class, 'receiverable');
    }
}
