<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class RefundItem extends Model
{
    protected $fillable = [
        'invoice_category_id',
        'name',
        'unit_id',
        'qty_purchased',
        'qty_refunded',
        'price',
        'total',
    ];

    public function category()
    {
        return $this->belongsTo(InvoiceCategory::class, 'invoice_category_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
}
