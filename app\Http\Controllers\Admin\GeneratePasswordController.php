<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

class GeneratePasswordController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke()
    {
        $minLength = 12;

        // Define the characters for each type
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()-_=+[]{}<>?';

        // Ensure each required type is present
        $password = $uppercase[random_int(0, strlen($uppercase) - 1)] .
            $lowercase[random_int(0, strlen($lowercase) - 1)] .
            $numbers[random_int(0, strlen($numbers) - 1)] .
            $symbols[random_int(0, strlen($symbols) - 1)];

        // Fill the rest of the password to meet the minimum length
        $allCharacters = $uppercase . $lowercase . $numbers . $symbols;
        for ($i = strlen($password); $i < $minLength; $i++) {
            $password .= $allCharacters[random_int(0, strlen($allCharacters) - 1)];
        }

        // Shuffle the password to ensure random order
        $password = str_shuffle($password);

        return success($password);
    }
}
