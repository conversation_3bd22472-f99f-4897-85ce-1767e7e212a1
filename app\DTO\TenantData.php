<?php

namespace App\DTO;

use <PERSON><PERSON>\LaravelData\Data;

class TenantData extends Data
{
    public function __construct(
        public string $id,
        public int $office_id,
        public int $subscription_id,
        public ?string $domain_name,
        public ?string $name,      // Optional name
        public ?string $email,     // Optional email
        public ?string $phone,     // Optional phone
        public ?string $address,   // Optional address
        public ?string $password,  // Optional password
        public string $tenancy_db_name,
        public bool $can_access_all_projects,
    ) {}
}
