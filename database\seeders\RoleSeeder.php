<?php

namespace Database\Seeders;

use App\Models\Tenant\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Step 1: Create or retrieve the Super Admin role
        $superAdminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'tenant_admin',
            'is_built_in' => true,
        ]);

        // Step 2: Fetch all routes and extract permissions
        $routes = Route::getRoutes();
        $permissions = [];

        foreach ($routes as $route) {
            $name = $route->getName();

            // Skip routes without a name or those that don't start with 'admin.'
            if (! $name || ! Str::startsWith($name, 'admin.')) {
                continue;
            }

            // Extract the part of the route name after 'admin.'
            $permissionParts = explode('.', str_replace('admin.', '', $name));
            $mainRoute = $permissionParts[0] ?? ''; // The main route (e.g., "users")

            // Skip if the main route is empty
            if (empty($mainRoute)) {
                continue;
            }

            $action = $permissionParts[1] ?? 'index'; // Default to "index" if no action is specified

            // Initialize the main route in the permissions array if not already set
            if (! isset($permissions[$mainRoute])) {
                $permissions[$mainRoute] = [];
            }

            // Add the action to the main route if not already present
            if (! in_array($action, $permissions[$mainRoute])) {
                $permissions[$mainRoute][] = $action;
            }
        }

        // Step 3: Create permissions and assign them to the Super Admin role
        foreach ($permissions as $mainRoute => $actions) {
            foreach ($actions as $action) {
                // Create or retrieve the permission
                $permission = Permission::firstOrCreate(
                    ['group' => $mainRoute, 'name' => $action, 'guard_name' => 'tenant_admin'],
                    ['group' => $mainRoute, 'name' => $action, 'guard_name' => 'tenant_admin']
                );

                $admin = User::firstOrCreate([
                    'id' => 1,
                ], [
                    'name' => 'Super Admin',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('b)F3W10]1'),
                    'phone' => '1234567890',
                    'status' => 'Active',
                    'type' => 'admin',
                    'can_access_all_projects' => true,
                ]);

                $admin->update(['role_id' => $superAdminRole->id]);

                // Assign the permission to the Super Admin role
                if (! $superAdminRole->hasPermissionTo($permission)) {
                    $superAdminRole->givePermissionTo($permission);
                }

                // Check if the admin already has this permission
                if (! $admin->hasPermissionTo($permission)) {
                    // Assign the permission to the admin
                    $admin->givePermissionTo($permission);
                }
            }
        }

        // Create Other Users Roles
        Role::firstOrCreate(['name' => 'Site Engineer'], ['name' => 'Site Engineer', 'guard_name' => 'tenant_admin', 'is_built_in' => true]);
        Role::firstOrCreate(['name' => 'Office Engineer'], ['name' => 'Office Engineer', 'guard_name' => 'tenant_admin', 'is_built_in' => true]);
        Role::firstOrCreate(['name' => 'Accountant'], ['name' => 'Accountant', 'guard_name' => 'tenant_admin', 'is_built_in' => true]);
        Role::firstOrCreate(['name' => 'Project Manager'], ['name' => 'Project Manager', 'guard_name' => 'tenant_admin', 'is_built_in' => true]);
    }
}
