<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class DebtPayment extends Model
{
    protected $fillable = [
        'debt_id',
        'user_id',
        'total_debt',
        'paid_amount',
        'remaining_debt',
        'notes',
    ];

    public function debt()
    {
        return $this->belongsTo(Debt::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }
}
