<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Plan::insert([
            [
                // 'image' => 'plans/silver.png',
                'name_ar' => 'Silver Plan',
                'name_en' => 'Silver Plan',
                'price' => 600,
                'number_of_projects' => 120,
                'number_of_site_engineers' => 22,
                'number_of_office_engineers' => 30,
                'number_of_accountants' => 80,
                'number_of_project_managers' => 80,
                'storage' => 4,
                'storage_type' => 'TB',
                'status' => 'Active',
                'has_domain' => true,
                'has_free_website' => true,
                'has_chat_availability' => true,
            ],
            [
                // 'image' => 'plans/gold.png',
                'name_ar' => 'Gold Plan',
                'name_en' => 'Gold Plan',
                'price' => 1000,
                'number_of_projects' => 120,
                'number_of_site_engineers' => 22,
                'number_of_office_engineers' => 30,
                'number_of_accountants' => 80,
                'number_of_project_managers' => 80,
                'storage' => 4,
                'storage_type' => 'TB',
                'status' => 'Active',
                'has_domain' => true,
                'has_free_website' => true,
                'has_chat_availability' => true,
            ],
            [
                // 'image' => 'plans/platinum.png',
                'name_ar' => 'Platinum Plan',
                'name_en' => 'Platinum Plan',
                'price' => 2000,
                'number_of_projects' => 120,
                'number_of_site_engineers' => 22,
                'number_of_office_engineers' => 30,
                'number_of_accountants' => 80,
                'number_of_project_managers' => 80,
                'storage' => 4,
                'storage_type' => 'TB',
                'status' => 'Active',
                'has_domain' => true,
                'has_free_website' => true,
                'has_chat_availability' => true,
            ],
        ]);
    }
}
