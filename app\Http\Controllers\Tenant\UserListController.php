<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\Tenant\Admin\UserListResource;
use App\Services\Tenant\UserService;
use Illuminate\Http\Request;

class UserListController extends Controller
{
    public function __construct(
        protected UserService $userService
    ) {}

    public function ddl()
    {
        $users = $this->userService->getAll();

        return success($users->groupBy('type')->map(fn($group) => UserListResource::collection($group)));
    }

    public function notificationReceivers(Request $request)
    {
        $request->validate([
            'type' => ['required', 'array', 'min:1'],
            'type.*' => ['required', 'in:admin,site_engineer,office_engineer,accountant,project_manager'],
        ]);

        $users = $this->userService->ddl($request->type);

        return success($users->groupBy('type')->map(fn($group) => UserListResource::collection($group)));
    }
}
