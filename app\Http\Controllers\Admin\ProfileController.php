<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateProfilePasswordRequest;
use App\Http\Requests\Admin\UpdateProfileRequest;
use App\Services\ProfileService;

class ProfileController extends Controller
{
    public function __construct(public ProfileService $profileService) {}

    public function me()
    {
        return $this->profileService->get();
    }

    public function update(UpdateProfileRequest $request)
    {
        return $this->profileService->update($request->validated());
    }

    public function updatePassword(UpdateProfilePasswordRequest $request)
    {
        return $this->profileService->updatePassword($request->validated());
    }
}
