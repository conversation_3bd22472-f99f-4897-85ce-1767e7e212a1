<?php

namespace App\Http\Controllers\Tenant;

use Pusher\Pusher;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PusherController extends Controller
{
    public function authenticate(Request $request)
    {
        $pusher = new Pusher(
            config('broadcasting.connections.pusher.key'),
            config('broadcasting.connections.pusher.secret'),
            config('broadcasting.connections.pusher.app_id'),
            [
                'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                'useTLS' => true,
            ]
        );

        $socketId = $request->input('socket_id');
        $channelName = $request->input('channel_name');

        $authData = $pusher->authorizeChannel($channelName, $socketId);

        $decodedData = json_decode($authData, true);

        return response()->json($decodedData);
    }
}
