<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use App\Http\Resources\MediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class OfficePlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'image' => new MediaResource($this->image),
            'name' => $this->name,
            'price' => $this->price,
            'number_of_projects' => $this->number_of_projects,
            'number_of_site_engineers' => $this->number_of_site_engineers,
            'number_of_office_engineers' => $this->number_of_office_engineers,
            'number_of_accountants' => $this->number_of_accountants,
            'number_of_project_managers' => $this->number_of_project_managers,
            'number_of_admins' => $this->number_of_admins,
            'storage' => $this->storage,
            'storage_type' => $this->storage_type,
            'status' => $this->status,
            'has_domain' => (bool) $this->has_domain,
            'has_free_website' => (bool) $this->has_free_website,
            'has_chat_availability' => (bool) $this->has_chat_availability,
            'is_unlimited_projects' => (bool) $this->is_unlimited_projects,
            'is_unlimited_site_engineers' => (bool) $this->is_unlimited_site_engineers,
            'is_unlimited_office_engineers' => (bool) $this->is_unlimited_office_engineers,
            'is_unlimited_accountants' => (bool) $this->is_unlimited_accountants,
            'is_unlimited_project_managers' => (bool) $this->is_unlimited_project_managers,
            'is_unlimited_admins' => (bool) $this->is_unlimited_admins,
        ];
    }
}
