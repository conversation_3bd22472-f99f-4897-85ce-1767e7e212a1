<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'client' =>[
                'id' => $this->client->id,
                'name' => $this->client->name,
            ] ,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'date' => $this->date,
            'number' => $this->number,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at,
            'created_by' => $this->created_by

        ];
    }
}
