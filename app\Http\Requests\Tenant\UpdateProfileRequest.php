<?php

namespace App\Http\Requests\Tenant;

use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'image' => 'sometimes|mimes:jpeg,png,jpg|max:5120',
            'name' => 'required|string|min:3|max:30',
            'email' => 'required|email|unique:users,email,'.auth()->user()->id,
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('users', ignoreId: auth()->user()->id)],
            'address' => 'sometimes|string|min:3|max:100',
            'current_password' => 'required_with:new_password',
            'new_password' => ['required_with:current_password', Password::min(8)->mixedCase()->letters()->numbers()->symbols()->uncompromised(), 'confirmed'],
        ];
    }
}
