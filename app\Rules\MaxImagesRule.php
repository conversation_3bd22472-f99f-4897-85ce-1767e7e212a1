<?php

namespace App\Rules;

use Closure;
use App\Repositories\Tenant\PurchaseRepository;
use Illuminate\Contracts\Validation\ValidationRule;

class MaxImagesRule implements ValidationRule
{
    protected PurchaseRepository $purchaseRepo;

    public function __construct(
        protected string $purchaseId,
        protected string $collection = 'purchases',
        protected int $maxImages = 5
    ) {
        $this->purchaseRepo = app(PurchaseRepository::class);
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $purchase = $this->purchaseRepo->findById($this->purchaseId);

        if (!$purchase) {
            $fail(__('Purchase not found'));
            return;
        }

        $currentImagesCount = $purchase->getMedia($this->collection)->count();
        $newImagesCount = is_array($value) ? count($value) : 1;
        $totalImages = $currentImagesCount + $newImagesCount;

        if ($totalImages > $this->maxImages) {
            $remainingSlots = $this->maxImages - $currentImagesCount;
            $fail(__('You can only upload :count more images', ['count' => $remainingSlots]));
        }
    }
}
