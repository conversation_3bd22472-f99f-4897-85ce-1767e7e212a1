<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class NotificationReceiver extends Model
{
    protected $fillable = ['group_notification_id', 'office_id'];

    public function office()
    {
        return $this->belongsTo(Office::class);
    }

    public function receiverable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }
}
