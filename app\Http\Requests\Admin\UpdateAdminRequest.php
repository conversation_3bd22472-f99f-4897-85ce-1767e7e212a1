<?php

namespace App\Http\Requests\Admin;

use App\Rules\IgnoreSuperAdminRole;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Contracts\Validation\ValidationRule;

class UpdateAdminRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = request()->route('admin');

        return [
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'name' => 'required|string|min:3|max:30',
            'email' => 'required|email|unique:admins,email,' . $id,
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('admins', ignoreId: $id)],
            'address' => 'sometimes|string|min:3|max:100',
            'status' => 'required|in:Active,Disabled',
            'role_id' => ['required', 'exists:roles,id', new IgnoreSuperAdminRole],
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ];
    }
}
