<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'office_name_ar' => 'sometimes|string|min:3|max:30',
            'color_theme' => 'sometimes|string|min:3|max:30',
            'office_name_en' => 'sometimes|string|min:3|max:30',
            'logo' => 'sometimes|image|mimes:jpeg,png,jpg|max:5120',
            'mobile' => 'sometimes|numeric|digits:10',
            'secondary_mobile' => 'sometimes|numeric|digits:10|different:mobile',
            'email' => 'sometimes|email',
            'address' => 'sometimes|string|min:3|max:100',
            'type' => 'required|in:general,withdraw',
            'user_allow_withdraw' => 'sometimes|boolean',
            'project_allow_withdraw' => 'sometimes|boolean',
            'user_overdraft_limit' => 'sometimes',
            'project_overdraft_limit' => 'sometimes',
            'project_unlimitid' => 'sometimes|boolean',
            'user_unlimitid' => 'sometimes|boolean',
        ];
    }
}
