<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Translatable\HasTranslations;

class Transaction extends Model
{
    use HasTranslations;

    protected $fillable = [
        'transactionable_id',
        'transactionable_type',
        'userable_id',
        'userable_type',
        'amount',
        'is_creator',
        'type',
        'message',
    ];

    public $translatable = ['message'];

    public function scopeCreator($query)
    {
        return $query->where('is_creator', true);
    }

    /**
     * Get the owning transactionable model.
     */
    public function transactionable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }

    /**
     * Get the owning userable model.
     */
    public function userable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }
}
