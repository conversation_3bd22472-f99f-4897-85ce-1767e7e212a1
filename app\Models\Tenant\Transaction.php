<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Transaction extends Model
{
    protected $fillable = [
        'transactionable_id',
        'transactionable_type',
        'userable_id',
        'userable_type',
        'amount',
        'is_creator',
        'type',
        'message',
        'message_ar',
        'message_en',
    ];

    public function scopeCreator($query)
    {
        return $query->where('is_creator', true);
    }

    /**
     * Get the localized message based on current locale
     */
    public function getLocalizedMessageAttribute()
    {
        $locale = app()->getLocale();
        $messageField = "message_{$locale}";

        // Return localized message if exists, otherwise fallback to default message
        return $this->{$messageField} ?? $this->message;
    }

    /**
     * Get the owning transactionable model.
     */
    public function transactionable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }

    /**
     * Get the owning userable model.
     */
    public function userable(): MorphTo
    {
        return $this->morphTo()->withTrashed();
    }
}
