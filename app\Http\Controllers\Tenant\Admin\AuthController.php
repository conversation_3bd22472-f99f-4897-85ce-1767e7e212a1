<?php

namespace App\Http\Controllers\Tenant\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Auth\ForgetPasswordRequest;
use App\Http\Requests\Admin\Auth\LoginRequest;
use App\Http\Requests\Admin\Auth\ResetPasswordRequest;
use App\Http\Requests\Admin\Auth\VerifyCodeRequest;
use App\Http\Resources\Tenant\LoginResource;
use App\Mail\PasswordReset;
use App\Repositories\Tenant\UserRepository;
use Exception;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;

class AuthController extends Controller
{
    public function __construct(protected UserRepository $userRepo) {}

    public function login(LoginRequest $request)
    {
        $user = $this->userRepo->findByEmailOrPhone($request->emailOrPhone);

        if (! $user) {
            return error(__('This Email Or Phone Does Not Exist'), 404);
        }

        if (! Hash::check($request->password, $user->password)) {
            return error(__('Your credentials are wrong'), 401);
        }

        if ($request->device_id && $request->token) {
            $user->updateDeviceToken([
                'device_id' => $request->device_id,
                'token' => $request->token,
            ]);
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return success([
            'token' => $token,
            'user' => new LoginResource($user),
        ]);
    }

    public function forgetPassword(ForgetPasswordRequest $request)
    {

        $user = $this->userRepo->findByEmailOrPhone($request->email);

        if (! $user) {
            return error(__('This Email Does Not Exist'), 404);
        }

        // Check for recent reset code
        $lastResetCode = $user->passwordResetCode()
            ->where('updated_at', '>=', now()->subMinutes(1))
            ->exists();

        if ($lastResetCode) {
            return error(__('Please wait 1 minutes before requesting another reset code.'), 422);
        }

        // Reset resend attempts after 12 hours
        $resetCode = $user->passwordResetCode()->firstOrCreate(
            [
                'resettable_id' => $user->id,
                'resettable_type' => get_class($user),
            ],
            [
                'code' => random_int(1000, 9999),
                'expires_at' => now()->addMinutes(10),
                'resend_attempts' => 0,
                'last_resend_attempt_at' => now(),
            ]
        );

        if ($resetCode->resend_attempts >= 3 && $resetCode->last_resend_attempt_at->diffInHours(now()) < 12) {
            return error(__('You have reached the resend limit. Please try again in 12 hours.'), 422);
        }

        if ($resetCode->last_resend_attempt_at->diffInHours(now()) >= 12) {
            $resetCode->update([
                'resend_attempts' => 0,
            ]);
        }

        // Update the reset code details
        $resetCode->update([
            'code' => random_int(1000, 9999),
            'expires_at' => now()->addMinutes(10),
            'resend_attempts' => $resetCode->resend_attempts + 1,
            'last_resend_attempt_at' => now(),
        ]);

        // Update admin attempts
        $user->update(['check_code_attempts' => 5]);

        // Generate the password reset token

        $broker = Password::broker('users');


        $token = $broker->createToken($user);

        // Include the token in the email
        try {
            Mail::to($user->email)->send(new PasswordReset($resetCode->code));
        } catch (Exception $e) {
            logger($e);
        }

        return success([
            'reset_code' => $resetCode->code,
            'reset_token' => $token,
        ]);
    }

    public function verifyResetCode(VerifyCodeRequest $request)
    {
        $user = $this->userRepo->findByEmailOrPhone($request->email);

        if (! $user) {
            return error(__('This Email Does Not Exist'), 404);
        }

        $resetCode = $user->passwordResetCode()->where('code', $request->code)->first();
        $isExpired = $resetCode && now()->gte($resetCode->expires_at);
        $outOfAttempts = $user->check_code_attempts == 0;

        if ((! $resetCode || $isExpired) && $outOfAttempts) {
            return error(__('Too many failed attempts, please resend a new reset code'), 429);
        }

        if (! $resetCode || $isExpired) {
            if ($user->check_code_attempts > 0) {
                $user->decrement('check_code_attempts');
            }

            return error($isExpired ? __('This Code Is Expired') : __('This Code Is Invalid'), 404);
        }

        return success(__('Your Code Is Valid'));
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $user = $this->userRepo->findByEmailOrPhone($request->email);

        if (! $user) {
            return error(__('This Email Does Not Exist'), 404);
        }

        // Validate the reset token
        $isValidToken = Password::broker('users')->tokenExists($user, $request->token);

        if (! $isValidToken) {
            return error(__('This Token Is Invalid or Expired'), 404);
        }

        // Check if the new password is the same as the old password
        if (Hash::check($request->new_password, $user->password)) {
            return error(__('The new password cannot be the same as the old password.'), 422);
        }

        // Update the password
        $user->update([
            'password' => Hash::make($request->new_password),
            'check_code_attempts' => null,
        ]);

        Password::broker('users')->deleteToken($user);

        $user->passwordResetCode()->delete();

        return success(__('Your Password Has Been Updated Successfully'));
    }

    public function logout()
    {
        auth()->user()->currentAccessToken()->delete();

        return success(__('Logged Out Successfully'));
    }
}
