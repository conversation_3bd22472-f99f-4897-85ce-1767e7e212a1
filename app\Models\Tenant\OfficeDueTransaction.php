<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class OfficeDueTransaction extends Model
{
    protected $fillable = [
        'remaining',
        'withdraw_by',
        'project_id',
        'amount'
    ];

    /**
     * Get the office due associated with the transaction.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
