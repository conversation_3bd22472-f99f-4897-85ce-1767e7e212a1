<?php

declare(strict_types=1);

use App\Http\Controllers\Admin\GeneratePasswordController;
use App\Http\Controllers\Tenant\AuthController;
use App\Http\Controllers\Tenant\ChatController;
use App\Http\Controllers\Tenant\ChatRoomController;
use App\Http\Controllers\Tenant\ClientController;
use App\Http\Controllers\Tenant\ContractorPaymentController;
use App\Http\Controllers\Tenant\DebtController;
use App\Http\Controllers\Tenant\DebtPaymentController;
use App\Http\Controllers\Tenant\GeneralSettingController;
use App\Http\Controllers\Tenant\HistoryController;
use App\Http\Controllers\Tenant\ItemController;
use App\Http\Controllers\Tenant\NotificationController;
use App\Http\Controllers\Tenant\OfficeDueController;
use App\Http\Controllers\Tenant\OfficeDueTransactionController;
use App\Http\Controllers\Tenant\PackageController;
use App\Http\Controllers\Tenant\PartnerController;
use App\Http\Controllers\Tenant\PaymentCertificateController;
use App\Http\Controllers\Tenant\PaymentMethodController;
use App\Http\Controllers\Tenant\PlanController;
use App\Http\Controllers\Tenant\ProfileController;
use App\Http\Controllers\Tenant\ProjectAttachmentController;
use App\Http\Controllers\Tenant\ProjectController;
use App\Http\Controllers\Tenant\ProjectPaymentController;
use App\Http\Controllers\Tenant\PurchaseController;
use App\Http\Controllers\Tenant\PusherController;
use App\Http\Controllers\Tenant\RefundController;
use App\Http\Controllers\Tenant\ReportController;
use App\Http\Controllers\Tenant\RoleListController;
use App\Http\Controllers\Tenant\StatisticsController;
use App\Http\Controllers\Tenant\SubscriptionController;
use App\Http\Controllers\Tenant\SupplyController;
use App\Http\Controllers\Tenant\TaskController;
use App\Http\Controllers\Tenant\TaskHistoryController;
use App\Http\Controllers\Tenant\TransactionController;
use App\Http\Controllers\Tenant\UnitController;
use App\Http\Controllers\Tenant\UserBalanceController;
use App\Http\Controllers\Tenant\UserController;
use App\Http\Controllers\Tenant\UserListController;
use App\Http\Middleware\Localization;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
    Localization::class,
])->group(function () {

    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forget-password', [AuthController::class, 'forgetPassword']);
        Route::post('verify-reset-code', [AuthController::class, 'verifyResetCode']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::post('remaining-attempts', [AuthController::class, 'getRemainingAttempts']);
    });

    Route::get('/generate-password', GeneratePasswordController::class);

    Route::middleware(['auth:sanctum'])->group(function () {
        // logout route
        Route::get('logout', [AuthController::class, 'logout']);
        // start of users routes
        Route::post('/pusher/auth', [PusherController::class, 'authenticate']);
        Route::get('users/transactions/{id}', [UserController::class, 'transactions']);
        Route::get('users/purchases/{id}', [UserController::class, 'purchases']);
        Route::get('users/contractor-payments/{id}', [UserController::class, 'contractorPayments']);
        Route::post('users/transfer-balance/{id}', [UserController::class, 'transferBalance'])->name('users.transferBalance');
        Route::post('users/update-balance/{id}', [UserController::class, 'updateBalance'])->name('users.updateBalance');
        Route::get('users/archive', [UserController::class, 'archive'])->name('users.archive');
        Route::get('users/restore/{id}', [UserController::class, 'restore'])->name('users.restore');
        Route::delete('users/force-delete/{id}', [UserController::class, 'forceDelete'])->name('users.forceDelete');
        Route::get('users/projects/ddl', [UserController::class, 'ProjectsDDl']);
        Route::apiResource('users', UserController::class);
        // start of clients routes
        Route::get('clients/archive', [ClientController::class, 'archive'])->name('clients.archive');
        Route::get('clients/restore/{id}', [ClientController::class, 'restore'])->name('clients.restore');
        Route::delete('clients/force-delete/{id}', [ClientController::class, 'forceDelete'])->name('clients.forceDelete');
        Route::get('clients/projects/ddl', [ClientController::class, 'ProjectsDDl']);
        Route::apiResource('clients', ClientController::class);
        // start of partners routes
        Route::apiResource('partners', PartnerController::class);
        Route::get('partners/restore/{partner}', [PartnerController::class, 'restore'])->name('partners.restore');
        Route::delete('partners/force-delete/{partner}', [PartnerController::class, 'forceDelete'])->name('partners.force-delete');
        // start of general settings routes
        Route::get('settings/{type}', [GeneralSettingController::class, 'index']);
        Route::post('settings/update', [GeneralSettingController::class, 'update']);
        // start of items routes
        Route::apiResource('items', ItemController::class);
        // start of units routes
        Route::apiResource('units', UnitController::class);
        // start of package routes
        Route::apiResource('packages', PackageController::class);
        // start of subscription routes
        Route::get('subscription', [SubscriptionController::class, 'index']);
        Route::post('subscription/update', [SubscriptionController::class, 'update']);
        // start of group notification routes
        Route::apiResource('notifications', NotificationController::class)->except('update');
        Route::get('notifications/resend/{notification}', [NotificationController::class, 'resend'])->name('notifications.resend');

        Route::get('chat-rooms', ChatRoomController::class)->middleware('can_access_chat_room');
        Route::apiResource('messages/{room}', ChatController::class)->only(['index', 'store'])->middleware('can_access_chat_room');
        Route::get('messages/{room}/read', [ChatController::class, 'read']);
        // start of projects routes
        Route::apiResource('projects', ProjectController::class)->except('destroy');
        Route::delete('projects/{project}/delete-media/{media}', [ProjectController::class, 'deleteMedia']);

        // start of purchases routes
        Route::apiResource('purchases', PurchaseController::class);
        Route::get('purchases/restore/{purchase}', [PurchaseController::class, 'restore']);
        Route::delete('purchases/force-delete/{purchase}', [PurchaseController::class, 'forceDelete']);
        Route::delete('purchases/{purchase}/delete-media/{media}', [PurchaseController::class, 'deleteMedia']);
        Route::post('purchases/pay/{purchase}', [PurchaseController::class, 'pay'])->name('purchases.pay');

        Route::apiResource('projects-attachments', ProjectAttachmentController::class);
        Route::delete('projects-attachments/{projectAttachmentId}/delete-media/{media}', [ProjectAttachmentController::class, 'deleteMedia']);
        Route::get('projects-attachments/restore/{projectAttachment}', [ProjectAttachmentController::class, 'restore'])->name('projects-attachments.restore');
        Route::get('projects-attachments/force-delete/{projectAttachment}', [ProjectAttachmentController::class, 'forceDelete'])->name('projects-attachments.forceDelete');

        Route::apiResource('refundes', RefundController::class);
        Route::get('refundes/restore/{refund}', [RefundController::class, 'restore']);
        Route::delete('refundes/force-delete/{refund}', [RefundController::class, 'forceDelete']);
        Route::delete('refundes/{refund}/delete-media/{media}', [RefundController::class, 'deleteMedia']);
        Route::post('refundes/pay/{refund}', [RefundController::class, 'pay'])->name('refundes.pay');

        Route::apiResource('contractor-payments', ContractorPaymentController::class);
        Route::get('contractor-payments/restore/{purchase}', [ContractorPaymentController::class, 'restore']);
        Route::delete('contractor-payments/force-delete/{purchase}', [ContractorPaymentController::class, 'forceDelete']);

        Route::apiResource('supplies', SupplyController::class);
        Route::get('supplies/restore/{supply}', [SupplyController::class, 'restore']);
        Route::delete('supplies/force-delete/{supply}', [SupplyController::class, 'forceDelete']);

        Route::apiResource('project-payments', ProjectPaymentController::class);
        Route::get('project-payments/restore/{id}', [ProjectPaymentController::class, 'restore']);
        Route::delete('project-payments/force-delete/{id}', [ProjectPaymentController::class, 'forceDelete']);

        Route::apiResource('payment-certificates', PaymentCertificateController::class);
        Route::get('payment-certificates/restore/{id}', [PaymentCertificateController::class, 'restore']);
        Route::delete('payment-certificates/force-delete/{id}', [PaymentCertificateController::class, 'forceDelete']);

        Route::apiResource('reports', ReportController::class);
        Route::get('reports/{id}/restore', [ReportController::class, 'restore'])->name('reports.restore');
        Route::delete('reports/{id}/force-delete', [ReportController::class, 'forceDelete'])->name('reports.force-delete');

        Route::prefix('debts')->group(function () {
            Route::prefix('supplier')->group(function () {
                Route::get('purchases/{projectId}', [DebtController::class, 'purchasesDebts'])->name('debts.supplier.purchases');
                Route::get('purchases/payments/{projectId}', [DebtPaymentController::class, 'purchasesDebtPayments'])->name('debts.supplier.purchases.payments');
                Route::get('refunds/{projectId}', [DebtController::class, 'refundsDebts'])->name('debts.supplier.refunds');
                Route::get('refunds/payments/{projectId}', [DebtPaymentController::class, 'refundsDebtPayments'])->name('debts.supplier.refunds.payments');
            });

            Route::post('payments/{debtId}', [DebtPaymentController::class, 'store'])->name('debts.payments.store');
        });

        Route::prefix('profile')->group(function () {
            Route::get('me', [ProfileController::class, 'me'])->name('profile.show');
            Route::post('update', [ProfileController::class, 'update'])->name('profile.update');
            Route::post('update-password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');
        });

        Route::post('payment-certificates/pay/{id}', [PaymentCertificateController::class, 'pay']);
        Route::get('history', [HistoryController::class, 'index'])->name('history.index');
        Route::get('statistics', [StatisticsController::class, 'index'])->name('statistics.index');
        Route::get('projects/{projectId}/statistics', [StatisticsController::class, 'projectStatistics'])->name('statistics.project');
        Route::get('statistics/items-chart', [StatisticsController::class, 'itemsChart'])->name('statistics.items-chart');

        Route::post('office-due-withdraw', [OfficeDueTransactionController::class, 'store']);
        Route::get('office-due-transactions', [OfficeDueTransactionController::class, 'index']);
        Route::get('office-due', [OfficeDueController::class, 'show']);
        Route::get('transactions/{projectId}', [TransactionController::class, 'index']);

        Route::get('user-balances', [UserBalanceController::class, 'index'])->name('user-balances.index');
        Route::post('user-balances/{id}', [UserBalanceController::class, 'changeBalance'])->name('user-balances.update');

        Route::prefix('debts')->group(function () {
            Route::prefix('supplier')->group(function () {
                Route::get('purchases/{projectId}', [DebtController::class, 'purchasesDebts'])->name('debts.supplier.purchases');
                Route::get('purchases/payments/{debtId}', [DebtPaymentController::class, 'purchasesDebtPayments'])->name('debts.supplier.purchases.payments');
                Route::get('refunds/{projectId}', [DebtController::class, 'refundsDebts'])->name('debts.supplier.refunds');
                Route::get('refunds/payments/{debtId}', [DebtPaymentController::class, 'refundsDebtPayments'])->name('debts.supplier.refunds.payments');
            });

            Route::prefix('contractor')->group(function () {
                Route::get('{projectId}', [DebtController::class, 'contractorDebts'])->name('debts.contractor');
                Route::get('payments/{debtId}', [DebtPaymentController::class, 'contractorDebtPayments'])->name('debts.contractor.payments');
            });

            Route::post('payments/{debtId}', [DebtPaymentController::class, 'store'])->name('debts.payments.store');
        });

        Route::get('my-tasks', [TaskController::class, 'myTasks']);
        Route::put('tasks/{id}/status', [TaskController::class, 'updateStatus']);
        Route::get('tasks/{id}/restore', [TaskController::class, 'restore'])->name('tasks.restore');
        Route::delete('tasks/{id}/force', [TaskController::class, 'forceDelete'])->name('tasks.force-delete');
        Route::get('tasks/archived', [TaskController::class, 'archived'])->name('tasks.archived');
        Route::apiResource('tasks', TaskController::class);

        Route::post('add-comment', [TaskHistoryController::class, 'addComment']);
    });

    Route::get('users-list', [UserListController::class, 'ddl']);

    Route::get('notification-receivers', [UserListController::class, 'notificationReceivers']);

    Route::get('role-list', RoleListController::class);

    Route::get('projects-list', [ProjectController::class, 'ddl']);

    Route::get('partners-list', [PartnerController::class, 'ddl']);

    Route::get('items-list', [ItemController::class, 'ddl']);

    Route::get('units-list', [UnitController::class, 'ddl']);

    Route::get('clients-list', [ClientController::class, 'ddl']);

    Route::get('purchases-list', [PurchaseController::class, 'ddl']);

    Route::get('packages-list', [PackageController::class, 'ddl'])->name('packages-list');

    Route::get('plans-list', [PlanController::class, 'ddl']);

    Route::get('payment-methods-list', PaymentMethodController::class);
});
