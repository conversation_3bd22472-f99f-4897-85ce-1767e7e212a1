<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContractorPayment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'date',
        'amount',
        'notes',
        'project_id',
        'item_id',
        'contractor_id',
        'created_by',
        'archived_by',
        'deleted_at',
        'permanent_deleted',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function contractor()
    {
        return $this->belongsTo(Partner::class, 'contractor_id')->withTrashed();
    }

    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }
}
