<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class Domain implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Subdomain validation regex
        $pattern = '/^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/';

        // Perform the validation
        if (! preg_match($pattern, $value)) {
            // Trigger validation failure with a custom error message
            $fail(__('validation.domain'));
        }
    }
}
