<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\TaskHistoryData;
use App\Repositories\Tenant\TaskHistoryRepository;
use Illuminate\Support\Facades\DB;

class TaskHistoryService
{
    protected $taskHistoryRepository;

    public function __construct(TaskHistoryRepository $taskHistoryRepository)
    {
        $this->taskHistoryRepository = $taskHistoryRepository;
    }

    public function logAction($taskId, $action, $message = null)
    {
        return $this->taskHistoryRepository->create([
            'task_id' => $taskId,
            'user_id' => auth()->id(),
            'action' => $action,
            'message' => $message,
        ]);
    }


    public function addComment(array $request)
    {
        DB::beginTransaction();

        try {
            $data = TaskHistoryData::from($request);
        
            $history = $this->taskHistoryRepository->create([
                'task_id' => $data->task_id,
                'user_id' => auth()->id(),
                'message' => $data->message,
                'action' => 'comment'
            ]);

            if (!empty($request['images'])) {
                $history->clearMediaCollection('tasks-history');
                foreach (request()->file('images') as $image) {
                    $history->addMedia($image)
                        ->toMediaCollection('tasks-history');
                }
            }

            DB::commit();

            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }
}
