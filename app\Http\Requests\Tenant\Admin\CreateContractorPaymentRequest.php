<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateContractorPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => ['required', 'date'],
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'contractor_id' => ['required', Rule::exists('partners', 'id')->where('type', 'contractor')],
            'project_id' => ['required', 'exists:projects,id'],
            'item_id' => ['required', 'exists:items,id'],
            'amount' => ['required', 'numeric', 'regex:/^\d{1,10}(\.\d{1,2})?$/', 'min:1'],
            'notes' => ['nullable', 'string', 'max:100'],
        ];
    }
}
