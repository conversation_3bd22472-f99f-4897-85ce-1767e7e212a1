<?php

namespace App\Http\Requests\Tenant\Admin;

use App\Rules\IgnoreSuperAdminRole;
use App\Rules\PasswordNotContainPersonalInfo;
use App\Rules\UniquePhoneWithoutLeadingZero;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public static function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:30'],
            'email' => ['required', 'email', 'unique:users,email'],
            'phone' => ['required', 'string', 'regex:/^\+?[0-9]{10,14}$/', new UniquePhoneWithoutLeadingZero('users')],
            'password_type' => 'required|in:Manual,Auto',
            'password' => [
                'required',
                'string',
                Password::min(8)->mixedCase()->numbers()->symbols(),
                Rule::when(request('password_type') === 'Manual', 'confirmed'),
                new PasswordNotContainPersonalInfo([
                    request('email'),
                    request('phone'),
                    request('name'),
                ]),
            ],
            'image' => ['sometimes', 'mimes:jpeg,png,jpg,gif,svg', 'max:5120'],
            'address' => ['nullable', 'string', 'max:100'],
            'status' => ['sometimes', Rule::in(['Active', 'Disabled'])],
            'type' => ['required', Rule::in(['admin', 'site_engineer', 'office_engineer', 'accountant', 'project_manager'])],
            'national_id' => ['sometimes', 'numeric', 'min_digits:14', 'max_digits:14', 'unique:users,national_id'],
            'details' => ['nullable', 'string', 'max:255'],
            'can_access_all_projects' => ['required', 'boolean'],
            'project_ids' => [
                'array',
            ],
            'project_ids.*' => ['integer', 'exists:projects,id'],
            'role_id' => ['required_if:type,admin', 'exists:roles,id', new IgnoreSuperAdminRole],
            'permissions' => 'sometimes|array',
            'permissions.*' => 'exists:permissions,id',
        ];
    }

    public function messages()
    {
        return [
            'role_id.required_if' => __('validation.required'),
            'permissions.required_if' => __('validation.required'),
        ];
    }
}
