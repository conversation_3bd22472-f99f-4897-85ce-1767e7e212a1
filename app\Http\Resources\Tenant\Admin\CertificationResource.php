<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CertificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
     $debt = $this->project->asDebtor()->where(['creditor_id'=> $this->contractor_id , 'type' => 'certification'])->first();

        return [
            'id' => $this->id,
            'date' => $this->date,
            'title' => $this->title,
            'item' => [
                'id' => $this->item?->id,
                'name' => $this->item?->name,

            ],
            'contractor' => [
                'id' => $this->contractor->id,
                'name' => $this->contractor->name,
                'total_certificates' => $this->contractor->PaymentCertificate->sum('total')
            ],
            'items' => $this->when($request->routeIs('payment-certificates.show'),$this->certificationItems->map(fn($item) => [
                'id' => $item->id,
                'item_name' => $item->item_name,
                'unit' => [
                    'id' => $item->unit->id,
                    'name' => $item->unit->name,
                ],
                'qty' => $item->qty,
                'price' => $item->price,
                'total' => $item->total,
            ])),
            'total' => $this->total,
            'notes' => $this->notes,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'images' => MediaResource::collection($this->whenLoaded('media')),

            'debt' => [
                'total_invoices' => $debt?->total_invoices,
                'total_payments' => $debt?->total_payments,
                'total_debt' => $debt?->total_debt
            ]
        ];
    }
    
}
