<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\ProjectAttachmentRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\ProjectAttachmentResource;
use App\Services\Tenant\ProjectAttachmentService;

class ProjectAttachmentController extends Controller
{
    public function __construct(public ProjectAttachmentService $projectAttachmentService) {}

    public function index()
    {
        $data = $this->projectAttachmentService->get();

        return success(new BaseCollection($data, ProjectAttachmentResource::class));
    }

    public function store(ProjectAttachmentRequest $request)
    {

        return $this->projectAttachmentService->create($request->validated());
    }

    public function update(ProjectAttachmentRequest $request, string $id)
    {
        return $this->projectAttachmentService->update($id, $request->validated());
    }

    public function deleteMedia(string $projectId, string $mediaId)
    {
        return $this->projectAttachmentService->deleteMedia($projectId, $mediaId);
    }

    public function destroy(string $id)
    {
        return $this->projectAttachmentService->delete($id);
    }

    public function show(string $id)
    {
        $data = $this->projectAttachmentService->getById($id);

        return success(new ProjectAttachmentResource($data));
    }

    public function restore(string $id)
    {
        $this->projectAttachmentService->restore($id);

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $this->projectAttachmentService->forceDelete($id);

        return success(__('Deleted Successfully'));
    }
}
