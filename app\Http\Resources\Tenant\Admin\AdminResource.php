<?php

namespace App\Http\Resources\Tenant\Admin;

use App\Http\Resources\Admin\PermissionResource;
use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'image' => MediaResource::make($this->image),
            'status' => $this->status,
            'role' => $this->role?->name,
            'permissions' => PermissionResource::collection($this->permissions->groupBy('group')),
        ];
    }
}
