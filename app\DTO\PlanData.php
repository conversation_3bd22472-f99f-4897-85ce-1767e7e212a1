<?php

namespace App\DTO;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class PlanData extends Data
{
    public function __construct(
        public string $name_ar,
        public string $name_en,
        public float $price,
        public int|Optional $number_of_projects,
        public int|Optional $number_of_site_engineers,
        public int|Optional $number_of_office_engineers,
        public int|Optional $number_of_accountants,
        public int|Optional $number_of_project_managers,
        public int|Optional $number_of_admins,
        public int|Optional $storage,
        public string $storage_type,
        public string $status,
        public bool $has_domain,
        public bool $has_free_website,
        public bool $has_chat_availability,
        public bool|Optional $is_unlimited_projects,
        public bool|Optional $is_unlimited_site_engineers,
        public bool|Optional $is_unlimited_office_engineers,
        public bool|Optional $is_unlimited_accountants,
        public bool|Optional $is_unlimited_project_managers,
        public bool|Optional $is_unlimited_admins,
    ) {}
}
