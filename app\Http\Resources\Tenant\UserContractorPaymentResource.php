<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserContractorPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $payment = $this->transactionable; // Get the associated payment from the transaction

        return [
            'id' => $payment->id,
            'name' => $payment->name,
            'contractor' => $payment->contractor->name,
            'project' => $payment->project->name,
            'date' => $payment->date,
            'amount' => $payment->amount,
        ];
    }
}
