<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PasswordResetCode extends Model
{
    protected $fillable = [
        'code',
        'expires_at',
        'last_resend_attempt_at',
        'resend_attempts',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'last_resend_attempt_at' => 'datetime',
    ];

    public function resettable()
    {
        return $this->morphTo();
    }
}
