<?php

namespace App\Services\Tenant;

use Illuminate\Support\Facades\DB;
use App\DTO\Tenant\DebtPaymentData;
use App\Repositories\Tenant\DebtRepository;
use App\Repositories\Tenant\SettingRepository;
use App\Repositories\Tenant\DebtPaymentRepository;
use App\Repositories\Tenant\ContractorPaymentRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DebtPaymentService
{
    public function __construct(
        protected DebtPaymentRepository $debtPaymentRepository,
        protected DebtRepository $debtRepository,
        protected ContractorPaymentRepository $contractorPaymentRepository,
        protected SettingRepository $settingRepository
    ) {
        //
    }

    public function getPurchasesDebtPayments(string $debtId)
    {
        return [
            'total_debts' => $this->debtRepository->totalPurchasesDebts($debtId),
            'payments' => $this->debtPaymentRepository->getPurchasesDebtPayments($debtId),
        ];
    }

    public function getRefundsDebtPayments(string $debtId)
    {
        return [
            'total_debts' => $this->debtRepository->totalRefundsDebts($debtId),
            'payments' => $this->debtPaymentRepository->getRefundsDebtPayments($debtId),
        ];
    }

    public function getContractorDebtPayments(string $debtId)
    {
        return [
            'total_debts' => $this->debtRepository->totalContractorDebts($debtId),
            'payments' => $this->debtPaymentRepository->getContractorDebtPayments($debtId),
        ];
    }

    public function create(array $request, string $debtId)
    {
        $user = auth()->user();
        $debt = $this->debtRepository->findById($debtId);
        if (! $debt) {
            throw new NotFoundHttpException('Debt not found');
        }
        $debtType = $debt->type;
        if ((int) $debt->total_debt === 0) {
            return error(__('Debt already paid'));
        }
        if ($request['paid_amount'] > $debt->total_debt) {
            return error(__('Paid amount cannot be greater than total debt'));
        }
        // Determine project and partner
        $isProjectDebtor = in_array($debtType, ['purchase', 'certification']);
        $project = $isProjectDebtor ? $debt->debtor : $debt->creditor;
        $partner = $isProjectDebtor ? $debt->creditor : $debt->debtor;
        if ($isProjectDebtor) {
            $balanceCheck = $this->settingRepository->balanceCheck($request['paid_amount'], $user->id, $project->id);
            if (! $balanceCheck['status']) {
                return error($balanceCheck['message']);
            }
        }
        DB::beginTransaction();
        try {
            $request = array_merge($request, [
                'debt_id' => $debt->id,
                'user_id' => $user->id,
                'total_debt' => $debt->total_debt,
                'remaining_debt' => $debt->total_debt - $request['paid_amount'],
            ]);
            $data = DebtPaymentData::from($request)->all();
            $this->debtPaymentRepository->create($data);
            $debt->update([
                'total_payments' => $debt->total_payments + $request['paid_amount'],
                'total_debt' => $debt->total_debt - $request['paid_amount'],
            ]);
            // Determine methods
            $method = $isProjectDebtor ? 'decrement' : 'increment';
            $oppositeMethod = $method === 'increment' ? 'decrement' : 'increment';
            $this->updateBalance($project, $user, $request['paid_amount'], $method);
            $this->updateBalance($partner, null, $request['paid_amount'], $oppositeMethod);
            DB::commit();
            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollback();
            logger($e->getMessage());
            return error($e->getMessage());
        }
    }
    private function updateBalance($model, $user = null, float $amount, string $method = 'decrement')
    {
        if (! in_array($method, ['increment', 'decrement'])) {
            throw new \Exception('Invalid method');
        }
        $model->{$method}('balance', $amount);
        if ($user) {
            $user->{$method}('balance', $amount);
        }
    }
}
