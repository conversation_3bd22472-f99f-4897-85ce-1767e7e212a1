<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ProjectData;
use App\Repositories\Tenant\ProjectRepository;
use App\Repositories\Tenant\UserRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ProjectService
{
    public function __construct(protected ProjectRepository $projectRepository, protected UserRepository $userRepository) {}

    public function searchddl()
    {
        return $this->projectRepository->searchDdl();
    }

    public function get()
    {
        $filterable = request()->only([
            'search',
            'status',
            'type',
            'size',
            'payment_method',
            'date_from',
            'date_to',
        ]);

        return $this->projectRepository->getPaginated($filterable);
    }

    public function getById(string $id)
    {
        $project = $this->projectRepository->findById($id);

        if (! $project) {
            throw new NotFoundHttpException;
        }

        return $project;
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {
            $data = ProjectData::from($request)->all();

            $project = $this->projectRepository->create($data);

            $subscription = tenant()->subscription;

            if ($subscription->has_chat_availability) {
                $this->updateChatRoom($project, $request);
            }

            if (! empty($request['clients'])) {
                $project->clients()->sync($request['clients']);
            }

            if (! empty($request['users'])) {
                $project->users()->sync($request['users']);
            }

            if (request()->hasFile('images')) {
                foreach (request()->file('images') as $image) {
                    $project->addMedia($image)
                        ->toMediaCollection('projects');
                }
            }

            $project->officeDue()->create([
                'project_id' => $project->id,
                'project_expenses' => 0,
                'office_ratio' => $project->office_ratio,
                'total_profits' => 0,
                'recieved_profits' => 0,
                'due_profits' => 0,
            ]);

            if (! $subscription->is_unlimited_projects && $subscription->number_of_projects > 0) {
                $subscription->decrement('number_of_projects');
            }

            $subscription->save();

            DB::commit();

            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function update(array $request, string $id)
    {
        $project = $this->getById($id);

        DB::beginTransaction();

        try {
            $data = ProjectData::from($request)->all();

            $project->update($data);

            $subscription = tenant()->subscription;

            if ($data['payment_method_id'] != $project->payment_method_id && $project->has_project_statements == 1) {
                return error(__('Cannot change payment method while project statements exist.'), 422);
            }

            if ($subscription->has_chat_availability) {
                $this->updateChatRoom($project, $request);
            }

            if (! empty($request['clients'])) {
                $project->clients()->sync($request['clients']);
            } else {
                $project->clients()->detach();
            }

            if (! empty($request['users'])) {
                $project->users()->sync($request['users']);
            } else {
                $project->users()->detach();
            }

            if (! empty($request['images'])) {
                // Clear existing images in the 'projects' collection
                $project->clearMediaCollection('projects');

                // Loop through and add each uploaded image
                foreach (request()->file('images') as $image) {
                    $project->addMedia($image)
                        ->toMediaCollection('projects');
                }
            }

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    private function updateChatRoom($project, $request)
    {
        $chatRoom = $project->room()->firstOrCreate([
            'project_id' => $project->id,
        ], [
            'name' => $project->name,
        ]);

        if (! empty($request['users'])) {
            $chatRoom->users()->sync($request['users']);
        }

        $superAdmin = $this->userRepository->getSuperAdmin();

        // Check if Super Admin is in the chat room users before attaching
        if (! $chatRoom->users()->where('user_id', $superAdmin->id)->exists()) {
            $chatRoom->users()->attach($superAdmin->id);
        }
    }

    public function deleteMedia(string $projectId, string $mediaId)
    {
        $project = $this->getById($projectId);

        $media = $project->media()->find($mediaId);

        if (! $media) {
            throw new NotFoundHttpException;
        }

        $media->delete();

        return success(__('Deleted Successfully'));
    }

    public function ddl(?array $columns = ['id', 'name'])
    {
        return $this->projectRepository->getAll($columns);
    }
}
