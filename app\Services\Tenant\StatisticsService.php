<?php

namespace App\Services\Tenant;

use App\Repositories\Tenant\StatisticsRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Models\Tenant\Project;

class StatisticsService
{
    public function __construct(protected StatisticsRepository $statisticsRepository)
    {
    }

    /**
     * Get statistics with optional unit_id and project_id filters for items chart
     *
     * @param int|null $unitId Optional unit ID to filter items chart by
     * @param int|null $projectId Optional project ID to filter items chart by
     * @return array
     */
    public function getStatistics()
    {
        return $this->statisticsRepository->getStatistics();
    }

    public function getProjectStatistics($projectId)
    {
        // Validate project exists
        $project = Project::find($projectId);
        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        return $this->statisticsRepository->getProjectStatistics($projectId);
    }


    /**
     * Get items by unit chart data with optional unit_id and project_id filters
     *
     * @param int|null $unitId Optional unit ID to filter by
     * @param int|null $projectId Optional project ID to filter by
     * @return \Illuminate\Support\Collection
     */
    public function getItemsByUnitChart($unitId = null, $projectId = null)
    {
        return $this->statisticsRepository->getItemsByUnitChart($unitId, $projectId);
    }
}
