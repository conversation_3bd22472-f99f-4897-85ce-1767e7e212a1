<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\ContractorPayment;
use Carbon\Carbon;

class ContractorPaymentRepository
{
    public function __construct(protected ContractorPayment $model)
    {
        //
    }

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with(['contractor', 'item'])
            ->where('project_id', request()->project)->orderByDesc('id')
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->where(function ($query) use ($filters) {
                    $query->where('name', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('created_by', 'like', '%' . $filters['search'] . '%');
                })->when(isset($filters['contractor']), function ($query) use ($filters) {
                    return $query->where('contractor_id', $filters['contractor']);
                })->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                    $dates = [
                        Carbon::parse($filters['date_from'])->startOfDay(),
                        Carbon::parse($filters['date_to'] ?? null)->endOfDay(),
                    ];

                    return $query->whereBetween('date', $dates);
                });
            })->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->where(function ($query) {
            $query->where('project_id', request()->project)
                ->where('permanent_deleted', false);
        })->onlyTrashed()->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function totalPayments(string $project_id)
    {
        return $this->model->where('project_id', $project_id)->sum('amount');
    }
}
