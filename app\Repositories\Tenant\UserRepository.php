<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\User;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UserRepository
{
    public function __construct(protected User $model) {}

    public function getPaginated(int $limit = 10)
    {
        return $this->model
            ->when(request('userType'), function ($query) {
                match (request('userType')) {
                    'engineer' => $query->whereIn('type', ['site_engineer', 'office_engineer']),
                    'admin' => $query->where('type', 'admin'),
                    default => $query->where('type', request('userType')),
                };
            })
            ->when(request('search'), function ($query) {
                return $query->where('name', 'like', '%' . request('search') . '%');
            })
            ->when(request('type'), function ($query) {
                return $query->where('type', request('type'));
            })
            ->when(request('status'), function ($query) {
                return $query->where('status', request('status'));
            })
            ->when(request('project_name'), function ($query) {
                // Filter users based on the related project name
                return $query->whereHas('projects', function ($subQuery) {
                    $subQuery->where('name', 'like', '%' . request('project_name') . '%');
                });
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function findOnlyTrashedById($id)
    {
        return $this->model->onlyTrashed()->where('permanent_deleted', false)->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(User $user, array $data)
    {
        $user->update($data);
    }

    public function delete(User $user)
    {
        $user->delete();
    }

    public function forceDelete(User $user)
    {
        $user->forceDelete();
    }

    public function getArchived(int $limit = 10, array $filters = [])
    {
        return $this->model
            ->onlyTrashed()
            ->where('permanent_deleted', false)
            ->when(! empty($filters['search']), function ($query) use ($filters) {
                $query->where('name', 'like', '%' . $filters['search'] . '%');
            })
            ->when(! empty($filters['type']), function ($query) use ($filters) {
                $query->where('type', $filters['type']);
            })
            ->when(request()->filled('userType'), function ($query) {
                $userType = request('userType');
                if ($userType === 'engineer') {
                    $query->whereIn('type', ['site_engineer', 'office_engineer']);
                } else {
                    $query->where('type', $userType);
                }
            })
            ->orderByDesc('id')->paginate($limit);
    }

    public function restore(User $user)
    {
        $user->restore();
    }

    public function all()
    {
        return $this->model->when(request()->project_id, function ($q) {
            $q->whereHas('projects', function ($query) {
                $query->where('projects.id', request()->project_id);
            });
        })->get();
    }
    
    
    

    public function updateBalance($user, $balance, $operator)
    {

        if (! $user) {
            throw new NotFoundHttpException('User not found.');
        }

        $user->balance = $operator === '+' ? $user->balance + $balance : $user->balance - $balance;

        $user->save();

        return $user;
    }

    public function findByEmailOrPhone($emailOrPhone)
    {
        return $this->model
            ->where('status', 'Active')
            ->where(function ($query) use ($emailOrPhone) {
                $query->where('email', $emailOrPhone)
                    ->orWhere('phone', $emailOrPhone);
            })
            ->first();
    }

    public function getDdl(array $types)
    {
        $users = $this->model->whereIn('type', $types)->get();

        return $users;
    }

    public function getSuperAdmin()
    {
        return $this->model->whereRelation('role', 'name', 'Super Admin')->first();
    }

    public function findUser(string $id)
    {
        return $this->model->find($id);
    }
}
