<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\ProjectPayment;
use Carbon\Carbon;

class ProjectPaymentRepository
{
    public function __construct(protected ProjectPayment $model)
    {
        //
    }

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model->where('project_id', request()->project)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->where(function($q) use ($filters) {
                    $q->where('title', 'like', "%{$filters['search']}%")
                      ->orWhereHas('client', function ($q) use ($filters) {
                          $q->where('name', 'like', "%{$filters['search']}%");
                      });
                });
            })
            ->when(isset($filters['date_from']) || isset($filters['date_to']), function ($query) use ($filters) {
                $dates = [
                    Carbon::parse($filters['date_from'])->startOfDay(),
                    Carbon::parse($filters['date_to'] ?? null)->endOfDay(),
                ];

                return $query->whereBetween('created_at', $dates);
            })->orderByDesc('id')->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function update($ProjectPayment, array $data)
    {

        $ProjectPayment->update($data);
    }

    public function all()
    {
        return $this->model->all();
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function getTrashedWithPaginate($limit = 10)
    {
        return $this->model->where(['project_id' => request()->project, 'permanent_deleted' => false])->onlyTrashed()->orderByDesc('id')->paginate($limit);
    }

    public function findTrashedById($id)
    {
        return $this->model->onlyTrashed()->find($id);
    }

    public function totalpayments($project_id)
    {
        return $this->model->where('project_id', $project_id)->sum('amount');
    }
}

