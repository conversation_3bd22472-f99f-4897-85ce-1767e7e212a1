<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\PurchaseService;
use App\Http\Resources\Tenant\PurchaseDdlResource;
use App\Http\Resources\Tenant\Admin\PurchaseResource;
use App\Http\Requests\Tenant\Admin\PayPurchaseRequest;
use App\Http\Requests\Tenant\Admin\CreatePurchaseRequest;
use App\Http\Requests\Tenant\Admin\UpdatePurchaseRequest;
use App\Http\Resources\Tenant\Admin\PurchaseDetailsResource;

class PurchaseController extends Controller
{
    public function __construct(public PurchaseService $purchaseService) {}

    public function index()
    {
        $purchases = $this->purchaseService->get();

        return success(new BaseCollection($purchases, PurchaseResource::class, [
            'total_purchases' => $this->purchaseService->getTotal(),
        ]));
    }

    public function store(CreatePurchaseRequest $request)
    {
        return $this->purchaseService->create($request->validated());
    }

    public function show(string $id)
    {
        $purchase = $this->purchaseService->getById($id);

        return success(new PurchaseDetailsResource($purchase));
    }

    public function update(UpdatePurchaseRequest $request, string $id)
    {
        return $this->purchaseService->update($id, $request->validated());
    }

    public function destroy(string $id)
    {
        return $this->purchaseService->delete($id);
    }

    public function restore(string $id)
    {
        return $this->purchaseService->restore($id);
    }

    public function forceDelete(string $id)
    {
        return $this->purchaseService->forceDelete($id);
    }

    public function deleteMedia(string $projectId, string $mediaId)
    {
        return $this->purchaseService->deleteMedia($projectId, $mediaId);
    }

    public function pay(PayPurchaseRequest $request, string $id)
    {
        return $this->purchaseService->pay($request->validated(), $id);
    }

    public function ddl()
    {
        $purchases = $this->purchaseService->ddl();

        return success(PurchaseDdlResource::collection($purchases));
    }
}
