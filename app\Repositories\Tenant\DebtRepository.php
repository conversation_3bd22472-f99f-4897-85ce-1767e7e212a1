<?php

namespace App\Repositories\Tenant;

use App\Models\Tenant\Debt;
use App\Models\Tenant\Partner;
use App\Models\Tenant\Project;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DebtRepository
{
    public function __construct(protected Debt $model)
    {
        //
    }

    public function getPurchaseDebts(string $projectId, array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with([
                'creditor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Partner::class => [],
                    ]);
                },
                'debtor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Project::class => [],
                    ]);
                },
            ])
            ->where('type', 'purchase')
            ->where('debtor_id', $projectId)
            ->where('debtor_type', Project::class)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->whereHasMorph('creditor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    })->orWhereHasMorph('debtor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    });
                });
            })
            ->paginate(request()->per_page ? request()->per_page : $limit);
    }


    public function getRefundDebts(string $projectId, array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with([
                'creditor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Project::class => [],
                    ]);
                },
                'debtor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Partner::class => [],
                    ]);
                },
            ])
            ->where('type', 'refund')
            ->where('creditor_id', $projectId)
            ->where('creditor_type', Project::class)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->whereHasMorph('creditor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    })->orWhereHasMorph('debtor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    });
                });
            })
            ->paginate(request('per_page', $limit));
    }

    public function getContractorDebts(string $projectId, array $filters = [], int $limit = 10)
    {
        return $this->model
            ->with([
                'creditor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Partner::class => [],
                    ]);
                },
                'debtor' => function ($morphTo) {
                    $morphTo->morphWith([
                        Project::class => [],
                    ]);
                },
            ])
            ->where('type', 'certification')
            ->where('debtor_id', $projectId)
            ->where('debtor_type', Project::class)
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $search = $filters['search'];
                $query->where(function ($q) use ($search) {
                    $q->whereHasMorph('creditor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    })->orWhereHasMorph('debtor', '*', function ($sub) use ($search) {
                        $sub->withoutGlobalScopes()
                            ->where('name', 'like', "%{$search}%");
                    });
                });
            })
            ->paginate(request()->per_page ? request()->per_page : $limit);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function totalPurchasesDebts(string $debtId)
    {
        $debt = $this->model
            ->where('type', 'purchase')
            ->find($debtId);

        if (! $debt) {
            throw new NotFoundHttpException;
        }

        return $debt->total_invoices;
    }

    public function totalRefundsDebts(string $debtId)
    {
        $debt = $this->model
            ->where('type', 'refund')
            ->find($debtId);

        if (! $debt) {
            throw new NotFoundHttpException;
        }

        return $debt->total_invoices;
    }

    public function totalContractorDebts(string $debtId)
    {
        $debt = $this->model
            ->where('type', 'certification')
            ->find($debtId);

        if (! $debt) {
            throw new NotFoundHttpException;
        }

        return $debt->total_invoices;
    }

    public function totalDebts(string $projectId, string $type)
    {
        $projectType = in_array($type, ['purchase', 'certification']) ? 'debtor' : 'creditor';

        return $this->model
            ->where("{$projectType}_id", $projectId)
            ->where("{$projectType}_type", Project::class)
            ->where('type', $type)
            ->sum('total_invoices');
    }
}
