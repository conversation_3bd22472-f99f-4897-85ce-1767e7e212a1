<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use Illuminate\Console\Command;

class EndSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:end';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deactivate subscriptions that have expired';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $expired = Subscription::where('end_date', '<=', now()->format('Y-m-d'))
            ->where('status', 'Active')
            ->update(['status' => 'Ended']);

        $this->info("Ended {$expired} expired subscriptions.");

        return Command::SUCCESS;
    }
}
