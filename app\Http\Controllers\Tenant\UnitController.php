<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Admin\CreateUnitRequest;
use App\Http\Requests\Tenant\Admin\UpdateUnitRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Tenant\Admin\UnitResource;
use App\Http\Resources\Tenant\DDLResource;
use App\Services\Tenant\UnitService;

class UnitController extends Controller
{
    public function __construct(public UnitService $unitService) {}

    public function index()
    {
        $clients = $this->unitService->index();

        return success(new BaseCollection($clients, UnitResource::class));
    }

    public function show($id)
    {
        $client = $this->unitService->show($id);

        return success(new UnitResource($client));
    }

    public function store(CreateUnitRequest $request)
    {
        $this->unitService->create($request->validated());

        return success(__('Created Successfully'));
    }

    public function update(string $id, UpdateUnitRequest $request)
    {
        $this->unitService->update($id, $request->validated());

        return success(__('updated Successfully'));
    }

    public function destroy(string $id)
    {
        $this->unitService->delete($id);

        return success(__('deleted successfully'));
    }

    public function ddl()
    {
        $units = $this->unitService->ddl(['id', 'name_ar', 'name_en']);

        return success(DDLResource::collection($units));
    }
}
