<?php

namespace App\Services;

use App\DTO\AdminData;
use App\Http\Resources\Admin\ProfileResource;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class ProfileService
{
    public function get()
    {
        return success(new ProfileResource(auth()->user()));
    }

    public function update(array $request)
    {
        $admin = auth()->user();

        $data = AdminData::from($request)->all();

        $admin->update($data);

        if (request()->hasFile('image')) {
            // Remove the old image(s)
            $admin->clearMediaCollection('admins');

            // Add the new image
            $admin->addMedia(request()->file('image'))
                ->toMediaCollection('admins');
        }

        return success(new ProfileResource($admin->refresh()));
    }

    public function updatePassword(array $request)
    {
        $admin = auth()->user();

        if (! Hash::check($request['current_password'], $admin->password)) {
            return error(__('Your current password is incorrect'));
        }

        $admin->update([
            'password' => Hash::make($request['new_password']),
        ]);

        return success(__('Updated Successfully'));
    }
}
