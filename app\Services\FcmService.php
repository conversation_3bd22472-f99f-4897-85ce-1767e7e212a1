<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FcmService
{
    public $url = 'https://fcm.googleapis.com/v1/projects/engsaas-de75d/messages:send';

    public $serverKey = '';

    public function send($fcm_token, $notification)
    {
        // Retrieve the server key
        $this->serverKey = $this->getToken();

        // Prepare the notification data
        $data = [
            'message' => [
                'token' => $fcm_token, // string not array
                'notification' => [
                    'title' => $notification['title'],
                    'body' => $notification['body'],
                ],
                'data' => [
                    'title' => $notification['title'],
                    'body' => $notification['body'],
                ],
            ],
        ];

        try {
            // Send the HTTP request using Laravel's HTTP client
            $response = Http::withToken($this->serverKey)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->url, $data);

            // Log the response
            Log::info($response->body());

            // Check if the response was successful
            if (! $response->successful()) {
                Log::error('Request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            // Log the exception
            Log::error('HTTP request failed', ['error' => $e->getMessage()]);
        }
    }

    public function getToken()
    {
        $keyFilePath = Storage::disk('root')->path('engsaas-de75d-c5fc94eb24d2.json');
        $keyData = json_decode(file_get_contents($keyFilePath), true);

        $header = [
            'alg' => 'RS256',
            'typ' => 'JWT',
        ];

        $now = time();
        $claims = [
            'iss' => $keyData['client_email'],
            'scope' => 'https://www.googleapis.com/auth/cloud-platform',
            'aud' => 'https://oauth2.googleapis.com/token',
            'exp' => $now + 3600,
            'iat' => $now,
        ];

        $base64UrlHeader = $this->base64UrlEncode(json_encode($header));
        $base64UrlClaims = $this->base64UrlEncode(json_encode($claims));

        $signatureInput = $base64UrlHeader.'.'.$base64UrlClaims;

        openssl_sign($signatureInput, $signature, $keyData['private_key'], 'sha256WithRSAEncryption');
        $base64UrlSignature = $this->base64UrlEncode($signature);

        $jwt = $signatureInput.'.'.$base64UrlSignature;

        $postFields = [
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt,
        ];

        try {
            // Send the HTTP request using Laravel's HTTP client
            $response = Http::asForm() // Sends the request as application/x-www-form-urlencoded
                ->post('https://oauth2.googleapis.com/token', $postFields);

            if ($response->successful()) {
                $responseData = $response->json();

                return $responseData['access_token'];
            } else {
                // Log error for debugging
                Log::error('Token request failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            // Log exception for debugging
            Log::error('HTTP request failed', ['error' => $e->getMessage()]);
        }

        return null;
    }

    private function base64UrlEncode($data)
    {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($data));
    }
}
