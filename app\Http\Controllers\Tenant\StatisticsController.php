<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\Tenant\StatisticsResource;
use App\Services\Tenant\StatisticsService;
use Illuminate\Http\Request;

class StatisticsController extends Controller
{
    public function __construct(protected StatisticsService $statisticsService)
    {
    }

    /**
     * Get statistics with optional unit_id and project_id filters
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $statistics = $this->statisticsService->getStatistics();

        return success(new StatisticsResource($statistics));
    }

    public function projectStatistics($projectId)
    {
        $statistics = $this->statisticsService->getProjectStatistics($projectId);

        return success(new StatisticsResource($statistics));
    }

    /**
     * Get items by unit chart data with optional unit_id and project_id filters
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function itemsChart(Request $request)
    {
        $unitId = $request->input('unit_id');
        $projectId = $request->input('project_id');

        $itemsChart = $this->statisticsService->getItemsByUnitChart($unitId, $projectId);

        return success([
            'items_by_unit_chart' => $itemsChart->map(function ($item) {
                return [
                    'unit_name' => $item['unit_name'],
                    'unit_id' => $item['unit_id'],
                    'total' => $item['total_amount'],
                ];
            }),
        ]);
    }
}

