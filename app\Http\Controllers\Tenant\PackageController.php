<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Resources\BaseCollection;
use App\Services\Tenant\PackageService;
use App\Http\Resources\Tenant\DDLResource;
use App\Http\Requests\Tenant\Admin\PackageRequest;
use App\Http\Resources\Tenant\Admin\PackageResource;

class PackageController extends Controller
{
    public function __construct(public PackageService $packageService) {}

    public function index()
    {
        $packages = $this->packageService->index();

        return success(new BaseCollection($packages, PackageResource::class));
    }

    public function show($id)
    {
        $package = $this->packageService->show($id);

        return success(new PackageResource($package));
    }

    public function store(PackageRequest $request)
    {
        $this->packageService->create($request->validated());

        return success(__('Created Successfully'));
    }

    public function update(string $id, PackageRequest $request)
    {
        $this->packageService->update($id, $request->validated());

        return success(__('updated Successfully'));
    }

    public function destroy(string $id)
    {
        $this->packageService->delete($id);

        return success(__('deleted successfully'));
    }

    public function ddl()
    {
        $items = $this->packageService->ddl(['id', 'name_ar', 'name_en' , 'price_per_meter']);

        return success(DDLResource::collection($items));
    }
}
