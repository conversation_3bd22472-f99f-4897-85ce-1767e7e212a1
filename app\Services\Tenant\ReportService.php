<?php

namespace App\Services\Tenant;

use App\DTO\Tenant\ReportData;
use App\Repositories\Tenant\ReportRepository;
use Illuminate\Pagination\LengthAwarePaginator;

class ReportService
{
    public function __construct(protected ReportRepository $reportRepository) {}

    public function get()
    {
        $filterable = request()->only(['search', 'date']);

        if (request()->type === 'archive') {
            return $this->reportRepository->getTrashedWithPaginate(
                limit: request()->per_page ?? 10,
                filters: $filterable
            );
        } else {
            return $this->reportRepository->getPaginated($filterable);
        }
    }

    public function getById(string $id)
    {
        $report = $this->reportRepository->findById($id);

        if (!$report) {
            throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
        }

        return $report;
    }

    public function create(array $data)
    {
        $data['created_by'] = auth()->user()->name;
        $reportData = ReportData::from($data)->all();
        $report = $this->reportRepository->create($reportData);

        if (! empty($data['images'])) {
            foreach (request()->file('images') as $image) {
                $report->addMedia($image)
                    ->toMediaCollection('reports');
            }
        }

        return success(__('Created Successfully'));
    }

    public function update(string $id, array $data)
    {
        $reportData = ReportData::from($data)->all();
        $report = $this->getById($id);

        $this->reportRepository->update($report, $reportData);
        if (! empty($request['images'])) {
            $report->clearMediaCollection('reports');

            foreach (request()->file('images') as $image) {
                $report->addMedia($image)
                    ->toMediaCollection('reports');
            }
        }
        return success(__('Updated Successfully'));
    }

    public function delete(string $id)
    {
        $report = $this->getById($id);
        $this->reportRepository->delete($report);

        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $report = $this->reportRepository->findTrashedById($id);

        if (!$report) {
            throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
        }

        $this->reportRepository->restore($report);

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $report = $this->reportRepository->findTrashedById($id);

        if (!$report) {
            throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
        }

        $this->reportRepository->forceDelete($report);

        return success(__('Permanently Deleted Successfully'));
    }
}
