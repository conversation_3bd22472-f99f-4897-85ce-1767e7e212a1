<?php

declare(strict_types=1);

namespace App\Services\Tenant;

use App\DTO\Tenant\SupplyData;
use App\Repositories\Tenant\SupplyRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class SupplyService
{
    public function __construct(protected SupplyRepository $supplyRepo) {}

    public function get()
    {
        $filterable = request()->only(['search', 'partner', 'date_from', 'date_to']);

        if (request()->type === 'archive') {
            return $this->supplyRepo->getTrashedWithPaginate();
        } else {
            return $this->supplyRepo->getPaginated($filterable);
        }
    }

    protected function calcItems(array $items)
    {
        $categories = collect($items);

        // Calculate item totals and category totals
        $categories->transform(function ($category) {
            $category['items'] = collect($category['items'])->transform(function ($item) {
                $item['total'] = ($item['price'] ?? 0) * $item['qty'];

                return $item;
            });

            // Calculate sum of item totals for the category
            $category['total'] = $category['items']->sum('total');

            return $category;
        });

        // Calculate overall sum of all categories' totals
        $overallTotal = $categories->sum('total');

        return [
            'categories' => $categories->toArray(),
            'overall_total' => $overallTotal,
        ];
    }

    protected function createCategoryItems(array $calc, $purchase)
    {
        foreach ($calc['categories'] as $category) {
            $invoiceCategory = $purchase->invoiceCategory()->updateOrCreate(
                ['item_id' => $category['id']],
                ['item_id' => $category['id']]
            );

            $invoiceCategory->supplyItems()->delete();
            $invoiceCategory->supplyItems()->createMany($category['items']);
        }
    }

    public function create(array $request)
    {
        DB::beginTransaction();

        try {

            $request['created_by'] = auth()->user()->name;

            // Calculate category items and overall total
            $calc = $this->calcItems($request['categories']);

            $request['total'] = $calc['overall_total'];

            // Prepare data for insertion
            $data = SupplyData::from($request)->all();

            // Create supply record
            $supply = $this->supplyRepo->create($data);

            $this->createCategoryItems($calc, $supply);

            DB::commit();

            return success(__('Created Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function getById($id)
    {
        $supply = $this->supplyRepo->findById($id);

        if (! $supply) {
            throw new NotFoundHttpException;
        }

        return $supply;
    }

    public function update(string $id, array $request)
    {
        $supply = $this->getById($id);

        DB::beginTransaction();

        try {
            // Calculate category items and overall total
            $calc = $this->calcItems($request['categories']);

            $request['total'] = $calc['overall_total'];

            // Prepare data for insertion
            $data = SupplyData::from($request)->all();

            // Update purchase record
            $supply->update($data);

            $this->createCategoryItems($calc, $supply);

            DB::commit();

            return success(__('Updated Successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());

            return error($e->getMessage());
        }
    }

    public function delete(string $id)
    {
        $supply = $this->getById($id);

        $supply->delete();

        return success(__('Deleted Successfully'));
    }

    public function restore(string $id)
    {
        $supply = $this->supplyRepo->findTrashedById($id);

        if (! $supply || $supply->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        $supply->restore();

        return success(__('Restored Successfully'));
    }

    public function forceDelete(string $id)
    {
        $supply = $this->supplyRepo->findTrashedById($id);

        if (! $supply || $supply->permanent_deleted) {
            throw new NotFoundHttpException;
        }

        $supply->update([
            'permanent_deleted' => true,
            'deleted_at' => now(),
        ]);

        return success(__('Deleted Successfully'));
    }
}
