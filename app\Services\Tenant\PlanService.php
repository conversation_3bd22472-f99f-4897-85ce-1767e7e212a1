<?php

namespace App\Services\Tenant;

use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PlanService
{
    public function ddl()
    {
        $subscription = tenant()->subscription;

        $query = Plan::on('central')->where('status', 'Active');

        if ($subscription) {
            $centralSubscription = DB::connection('central')
                ->table('subscriptions')
                ->where('id', $subscription->id)
                ->first();

            if (! $centralSubscription) {
                throw new NotFoundHttpException;
            }

            $query->where('id', '!=', $centralSubscription->plan_id);
        }

        return $query->with('media')->get();
    }
}
