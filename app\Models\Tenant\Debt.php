<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class Debt extends Model
{
    protected $fillable = [
        'creditor_id',
        'creditor_type',
        'debtor_id',
        'debtor_type',
        'type',
        'total_invoices',
        'total_payments',
        'total_debt',
    ];

    public function creditor()
    {
        return $this->morphTo()->withTrashed();
    }

    public function debtor()
    {
        return $this->morphTo()->withTrashed();
    }
}
