<?php

namespace App\Http\Resources\Tenant;

use App\Http\Resources\Admin\PermissionResource;
use App\Http\Resources\Tenant\Admin\ProjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'national_id' => $this->national_id,
            'image' => MediaResource::make($this->getMedia('users')->first()),
            'status' => $this->status,
            'type' => $this->type,
            'details' => $this->details,
            'projects' => ProjectResource::collection($this->projects),
            'balance' => $this->balance,
            'can_access_all_projects' => (bool) $this->can_access_all_projects,
            'role' => [
                'id' => $this->role?->id,
                'name' => $this->role?->name,
            ],
            'permissions' => PermissionResource::collection($this->permissions->groupBy('group')),

        ];
    }
}
