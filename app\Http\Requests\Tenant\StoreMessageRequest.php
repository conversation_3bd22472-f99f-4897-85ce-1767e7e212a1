<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class StoreMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'required|string|in:text,file',
            'body' => 'required_if:type,text|string',
            'file' => [
                'required_if:type,file',
                'file',
                function ($attribute, $value, $fail) {
                    $supportedExtensions = [
                        'jpg',
                        'png',
                        'pdf',
                        'docx',
                        'doc',
                        'xlsx',
                        'dwg',
                        'dxf',
                        'max',
                        'fbx',
                        'obj',
                        '3ds'

                    ];

                    $extension = $value->getClientOriginalExtension();

                    // Check if the extension is supported
                    if (! in_array($extension, $supportedExtensions)) {
                        return $fail("The {$attribute} has an unsupported file extension.");
                    }

                    // Define max size for each extension
                    $maxSize = match ($extension) {
                        'jpg', 'png' => 5120,  // 5MB for images
                        'pdf', 'docx', 'doc', 'xlsx' => 10240,  // 10MB for documents
                        'mp4', 'avi', 'mkv', 'flv' => 51200,  // 50MB for videos
                        default => 0,
                    };

                    // Check file size
                    if ($value->getSize() > $maxSize * 1024) {
                        $fail("The {$attribute} exceeds the maximum allowed size of ".($maxSize / 1024).'MB.');
                    }
                },
            ],
        ];
    }
}
