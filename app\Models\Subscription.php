<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    protected $fillable = [
        'office_id',
        'plan_id',
        'duration',
        'plan_price',
        'total',
        'status',
        'start_date',
        'end_date',
        'number_of_projects',
        'number_of_site_engineers',
        'number_of_office_engineers',
        'number_of_accountants',
        'number_of_project_managers',
        'number_of_admins',
        'storage',
        'storage_type',
        'has_domain',
        'has_free_website',
        'has_chat_availability',
        'is_unlimited_projects',
        'is_unlimited_site_engineers',
        'is_unlimited_office_engineers',
        'is_unlimited_accountants',
        'is_unlimited_project_managers',
        'is_unlimited_admins',
    ];

    public function casts()
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
        ];
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class)->withTrashed();
    }

    public function office()
    {
        return $this->belongsTo(Office::class)->withTrashed();
    }
}
