<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartnerItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'partner_id',
        'item_id',
    ];

    /**
     * Relationship: PartnerItem belongs to a Partner.
     */
    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    /**
     * Relationship: PartnerItem belongs to an Item.
     */
    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
