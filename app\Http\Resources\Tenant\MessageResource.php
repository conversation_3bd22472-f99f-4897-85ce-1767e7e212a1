<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'body' => $this->type === 'text' ? $this->body : null,
            'file' => $this->type === 'file' ? new MediaResource($this->getMedia('messages')->first()) : null,
            'sender' => [
                'id' => $this->sender->id,
                'name' => $this->sender->name,
                'email' => str_replace('deleted_', '', $this->sender->email),
                'owner' => $this->sender->is(auth()->user()),
            ],
            'room' => [
                'id' => $this->room->id,
                'name' => $this->room->name,
            ],
            'created_at' => $this->created_at->format('Y-m-d h:i A'),
            'read_at' => optional($this->readStatusFor(auth()->user()))?->read_at?->format('Y-m-d h:i A'),
        ];
    }
}
