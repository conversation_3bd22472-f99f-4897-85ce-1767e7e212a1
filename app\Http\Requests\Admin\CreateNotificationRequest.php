<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateNotificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|min:3|max:30',
            'body' => 'required|string|min:3|max:255',
            'receivers' => 'required|array',
            'receivers.offices' => 'sometimes|array',
            'receivers.offices.*' => 'exists:offices,id',
            'receivers.admins' => 'sometimes|array',
            'receivers.admins.*' => 'exists:admins,id',
        ];
    }
}
