<?php

namespace App\Http\Resources\Tenant\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RefundsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $debt = $this->project->asCreditor()->where('debtor_id', $this->supplier_id)->first();

        return [
            'id' => $this->id,
            'number' => $this->number,
            'name' => $this->name,
            'type' => $this->type,
            'partner' => $this->supplier?->name,
            'total_after_discount' => $this->total_after_discount,
            'status' => $this->status,
            'paid_amount' => $this->paid_amount,
            'remaining_amount' => $this->remaining_amount,
            'date' => $this->date,
            'created_at' => $this->created_at,
            'created_by' => $this->created_by,
            'debt' => [
                'total_invoices' => $debt?->total_invoices ?? 0,
                'total_payments' => $debt?->total_payments ?? 0,
                'total_debt' => $debt?->total_debt ?? 0
            ]
        ];
    }
}
