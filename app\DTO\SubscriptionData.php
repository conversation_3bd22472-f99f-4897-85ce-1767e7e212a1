<?php

namespace App\DTO;

use <PERSON><PERSON>\LaravelData\Data;
use Spa<PERSON>\LaravelData\Optional;

class SubscriptionData extends Data
{
    public function __construct(
        public string|Optional $office_id,
        public string|Optional $plan_id,
        public int $duration,
        public float $plan_price,
        public float $total,
        public string|Optional $status,
        public string $start_date,
        public string $end_date,
        public int|Optional $number_of_projects,
        public int|Optional $number_of_site_engineers,
        public int|Optional $number_of_office_engineers,
        public int|Optional $number_of_accountants,
        public int|Optional $number_of_project_managers,
        public int|Optional $number_of_admins,
        public int|Optional $storage,
        public string|Optional $storage_type,
        public bool|Optional $has_domain,
        public bool|Optional $has_free_website,
        public bool|Optional $has_chat_availability,
        public bool|Optional $is_unlimited_projects,
        public bool|Optional $is_unlimited_site_engineers,
        public bool|Optional $is_unlimited_office_engineers,
        public bool|Optional $is_unlimited_accountants,
        public bool|Optional $is_unlimited_project_managers,
        public bool|Optional $is_unlimited_admins,
    ) {}
}
