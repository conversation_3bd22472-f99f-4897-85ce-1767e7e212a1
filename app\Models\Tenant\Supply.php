<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supply extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'date',
        'project_id',
        'partner_id',
        'total',
        'notes',
        'created_by',
        'archived_by',
        'permanent_deleted',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function invoiceCategory()
    {
        return $this->morphMany(InvoiceCategory::class, 'invoiceable');
    }

    public function items()
    {
        return $this->hasManyThrough(
            SupplyItem::class,
            InvoiceCategory::class,
            'invoiceable_id', // Foreign key on `invoice_categories` table
            'invoice_category_id', // Foreign key on `purchase_items` table
            'id', // Local key on `purchases` table
            'id' // Local key on `invoice_categories` table
        )->where('invoiceable_type', static::class);
    }
}
