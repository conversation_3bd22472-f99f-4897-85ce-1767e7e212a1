<?php

namespace App\Http\Requests\Tenant\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|min:3|max:30',
            'type' => 'required|in:residential,commercial,industrial,infrastructure',
            'size' => 'required|in:small,medium,large',
            'status' => 'required|in:new,in_progress,completed,deferred',
            'area' => 'required|integer|digits_between:1,50',
            'clients' => 'required|array',
            'clients.*' => 'required|integer|exists:clients,id',
            'payment_method_id' => 'required|integer|exists:payment_methods,id',
            'package_id' => 'required_if:payment_method_id,6|integer|exists:packages,id',
            'office_ratio' => 'required_if:payment_method_id,1|integer|min:1',
            'amount_per_meter' => 'required_if:payment_method_id,4,5,6|numeric|min:1|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'total' => 'required_unless:payment_method_id,1|min:1|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
            'users' => 'nullable|array',
            'users.*' => 'nullable|integer|exists:users,id',
            'withdraw_limit' => 'required|min:1|regex:/^\d{1,10}(\.\d{1,2})?$/',
            'images' => 'nullable|array|max:5',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg|max:5120',
            'description' => 'nullable|string|max:255',
        ];
    }

    public function messages()
    {
        return [
            'package_id.required_if' => __('validation.required'),
            'office_ratio.required_if' => __('validation.required'),
            'amount_per_meter.required_if' => __('validation.required'),
            'total.required_unless' => __('validation.required'),
        ];
    }
}
