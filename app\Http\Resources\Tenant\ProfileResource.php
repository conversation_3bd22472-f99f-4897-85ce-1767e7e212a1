<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use App\Http\Resources\Tenant\MediaResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'image' => MediaResource::make($this->image),
        ];
    }
}
